// JobbLogg ESLint Configuration
// Preventive rules for import/module resolution issues

module.exports = {
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:import/recommended',
    'plugin:import/typescript'
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true
    }
  },
  plugins: [
    '@typescript-eslint',
    'react',
    'react-hooks',
    'import'
  ],
  rules: {
    // Import validation rules
    'import/no-unresolved': 'error',
    'import/named': 'error',
    'import/default': 'error',
    'import/namespace': 'error',
    'import/no-absolute-path': 'error',
    'import/no-dynamic-require': 'warn',
    'import/no-self-import': 'error',
    'import/no-cycle': 'error',
    'import/no-useless-path-segments': 'error',
    
    // Consistent import patterns
    'import/order': ['error', {
      'groups': [
        'builtin',
        'external',
        'internal',
        'parent',
        'sibling',
        'index'
      ],
      'newlines-between': 'never',
      'alphabetize': {
        'order': 'asc',
        'caseInsensitive': true
      }
    }],
    
    // TypeScript import consistency
    '@typescript-eslint/consistent-type-imports': ['error', {
      'prefer': 'type-imports',
      'disallowTypeAnnotations': false
    }],
    
    // React component export patterns
    'import/prefer-default-export': 'warn',
    'import/no-default-export': 'off',
    
    // Convex-specific rules
    'no-restricted-imports': ['error', {
      'patterns': [
        {
          'group': ['**/convex/_generated/dataModel'],
          'message': 'Avoid importing Id types in dynamically imported components. Use string types instead.',
          'allowTypeImports': true
        }
      ]
    }],
    
    // Dynamic import safety
    'import/no-restricted-paths': ['error', {
      'zones': [
        {
          'target': './src/pages/**/*',
          'from': './convex/_generated/dataModel.ts',
          'message': 'Page components should avoid Id types to prevent dynamic import issues. Use string types instead.'
        }
      ]
    }]
  },
  
  // Custom rules for JobbLogg patterns
  overrides: [
    {
      // Stricter rules for page components (likely to be dynamically imported)
      files: ['src/pages/**/*.tsx', 'src/pages/**/*.ts'],
      rules: {
        'import/no-restricted-paths': ['error', {
          'zones': [
            {
              'target': './src/pages/**/*',
              'from': './convex/_generated/dataModel.ts',
              'message': 'Page components should use string types instead of Id<T> to avoid dynamic import issues'
            }
          ]
        }],
        // Ensure default exports for lazy loading
        'import/prefer-default-export': 'error'
      }
    },
    
    {
      // Relaxed rules for utility components
      files: ['src/components/**/*.tsx', 'src/components/**/*.ts'],
      rules: {
        'import/prefer-default-export': 'off'
      }
    },
    
    {
      // Convex function files
      files: ['convex/**/*.ts'],
      rules: {
        'import/no-restricted-paths': 'off',
        '@typescript-eslint/consistent-type-imports': 'off'
      }
    }
  ],
  
  settings: {
    'import/resolver': {
      'typescript': {
        'alwaysTryTypes': true,
        'project': './tsconfig.json'
      },
      'node': {
        'extensions': ['.js', '.jsx', '.ts', '.tsx']
      }
    },
    'react': {
      'version': 'detect'
    }
  },
  
  env: {
    browser: true,
    es2022: true,
    node: true
  }
};
