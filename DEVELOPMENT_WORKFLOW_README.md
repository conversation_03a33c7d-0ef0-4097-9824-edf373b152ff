# JobbLogg Development Workflow
## Preventing Import and Module Resolution Issues

This document outlines the new development workflow designed to prevent recurring import/module resolution issues in the JobbLogg application.

## 🎯 Quick Start

### For New Components
```bash
# Generate a new component with proper patterns
npm run generate:component

# Validate imports before testing
npm run validate:imports

# Run full pre-commit validation
npm run validate:pre-commit
```

### For Existing Development
```bash
# Start development with validation
npm run dev

# Before committing changes
npm run validate:pre-commit

# Before building for production
npm run validate:pre-build
```

## 📋 Available Scripts

### Validation Scripts
- `npm run validate:imports` - Check import patterns and paths
- `npm run validate:pre-build` - Full validation before build
- `npm run validate:pre-commit` - Complete pre-commit checks
- `npm run type-check` - TypeScript compilation check
- `npm run test:imports` - Quick import validation test
- `npm run test:dynamic-imports` - Validate dynamic imports via build

### Development Scripts
- `npm run generate:component` - Interactive component generator
- `npm run lint:jobblogg` - JobbLogg-specific linting rules
- `npm run build` - Build with pre-validation

## 🛠️ Tools and Configuration

### 1. Import Validator (`scripts/validate-imports.js`)
Analyzes your codebase for common import issues:
- ✅ Convex Id type usage in dynamic imports
- ✅ Import path consistency
- ✅ Dynamic import path validation
- ✅ Export pattern compliance
- ✅ TypeScript compilation

### 2. Component Generator (`scripts/component-template-generator.js`)
Creates new components with proper patterns:
- ✅ Correct import paths based on component location
- ✅ Proper export patterns for lazy loading
- ✅ Safe Convex integration patterns
- ✅ Automatic lazy component registration

### 3. ESLint Configuration (`.eslintrc.jobblogg.js`)
Enforces import best practices:
- ✅ Consistent import ordering
- ✅ Restricted imports for dynamic components
- ✅ TypeScript import consistency
- ✅ Path validation

### 4. Pre-commit Hooks (`.husky/pre-commit`)
Prevents problematic commits:
- ✅ Import validation
- ✅ TypeScript compilation
- ✅ Linting with JobbLogg rules

## 📖 Development Guidelines

### Creating New Page Components

```bash
# Use the generator for consistency
npm run generate:component

# Select "Page Component" when prompted
# This ensures:
# - Proper dynamic import setup
# - Correct export patterns
# - Safe Convex integration
# - Automatic lazy loading registration
```

### Manual Component Creation

If creating components manually, follow these patterns:

#### ✅ Correct Patterns

```typescript
// Page component with Convex (dynamic import safe)
import React from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';

interface Props {
  projectId: string; // Use string, not Id<"projects">
}

const MyPageComponent: React.FC<Props> = ({ projectId }) => {
  const project = useQuery(api.projects.get, { projectId });
  return <div>Content</div>;
};

export default MyPageComponent; // Default export for lazy loading
```

```typescript
// UI component with Convex (static import - can use Id types)
import React from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';

interface Props {
  projectId: Id<"projects">; // OK for static imports
}

export const MyUIComponent: React.FC<Props> = ({ projectId }) => {
  const project = useQuery(api.projects.get, { projectId });
  return <div>Content</div>;
};
```

#### ❌ Patterns to Avoid

```typescript
// ❌ AVOID: Id types in page components (dynamic import issues)
import { Id } from '../../../convex/_generated/dataModel';
interface Props {
  projectId: Id<"projects">; // This breaks dynamic imports
}

// ❌ AVOID: Inconsistent import depths
import { api } from '../../../../convex/_generated/api'; // Too deep
import { api } from '../convex/_generated/api'; // Too shallow

// ❌ AVOID: Missing default export for page components
export const MyPageComponent = () => { /* ... */ }; // Should be default export
```

### Import Path Guidelines

```typescript
// ✅ CORRECT: Consistent relative paths based on file location

// From src/pages/MyPage/MyPage.tsx
import { api } from '../../../convex/_generated/api';
import { PrimaryButton } from '../../components/ui';

// From src/components/MyComponent/MyComponent.tsx  
import { api } from '../../../convex/_generated/api';
import { PrimaryButton } from '../ui';

// From src/components/ui/MyUIComponent/MyUIComponent.tsx
import { api } from '../../../../convex/_generated/api';
import { TextStrong } from '../';
```

## 🔍 Troubleshooting

### Common Issues and Solutions

#### Dynamic Import Fails with 500 Error
**Cause**: Convex Id types in dynamically imported components
**Solution**: 
```typescript
// Change from:
import { Id } from '../../../convex/_generated/dataModel';
interface Props { id: Id<"projects">; }

// To:
interface Props { id: string; }
```

#### TypeScript Compilation Errors
**Cause**: Import path resolution issues
**Solution**:
```bash
# Run validation to identify issues
npm run validate:imports

# Check specific import paths
npm run type-check
```

#### Lazy Loading Component Not Found
**Cause**: Incorrect import path in LazyComponents.tsx
**Solution**:
```typescript
// Ensure correct path in LazyComponents.tsx
export const LazyMyComponent = createLazyComponent(
  () => import('../pages/MyComponent') // Check this path
);
```

### Validation Failures

If validation fails, check:

1. **Import Paths**: Ensure consistent relative path depths
2. **Export Patterns**: Page components need default exports
3. **Convex Types**: Use string types in dynamic imports
4. **File Structure**: Verify index.ts files exist and export correctly

## 🚀 Best Practices

### Before Starting Development
1. Run `npm run validate:imports` to check current state
2. Use `npm run generate:component` for new components
3. Plan import strategy based on component type

### During Development
1. Test components immediately after creation
2. Validate imports frequently: `npm run validate:imports`
3. Check TypeScript compilation: `npm run type-check`

### Before Committing
1. Run `npm run validate:pre-commit`
2. Test dynamic imports manually if adding new page components
3. Verify all routes work in development mode

### Before Production
1. Run `npm run validate:pre-build`
2. Test production build: `npm run build`
3. Verify all lazy-loaded routes work

## 📊 Success Metrics

Track these to measure workflow effectiveness:
- ✅ Zero dynamic import failures in development
- ✅ First-time success rate for new components
- ✅ Reduced debugging time for import issues
- ✅ Faster development cycles

## 🔄 Continuous Improvement

This workflow will evolve based on:
- New patterns discovered during development
- Additional tooling requirements
- Team feedback and pain points
- Framework updates and best practices

Update this documentation when new patterns or tools are added.
