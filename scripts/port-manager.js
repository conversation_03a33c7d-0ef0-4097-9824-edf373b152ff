#!/usr/bin/env node

/**
 * Port Manager for JobbLogg Development Environment
 * 
 * This script ensures consistent port usage by:
 * 1. Checking if required ports are available
 * 2. Optionally killing processes using those ports
 * 3. Providing clear error messages about port conflicts
 */

import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Configuration for JobbLogg development ports
const REQUIRED_PORTS = {
  vite: 5173,
  preview: 4173
};

/**
 * Find process using a specific port
 */
async function findProcessOnPort(port) {
  try {
    const { stdout } = await execAsync(`lsof -ti:${port}`);
    const pids = stdout.trim().split('\n').filter(pid => pid);
    return pids;
  } catch (error) {
    // No process found on port (which is good)
    return [];
  }
}

/**
 * Get process details for a PID
 */
async function getProcessDetails(pid) {
  try {
    const { stdout } = await execAsync(`ps -p ${pid} -o pid,ppid,command --no-headers`);
    return stdout.trim();
  } catch (error) {
    return `PID ${pid} (process details unavailable)`;
  }
}

/**
 * Kill process by PID
 */
async function killProcess(pid) {
  try {
    await execAsync(`kill -9 ${pid}`);
    return true;
  } catch (error) {
    console.error(`Failed to kill process ${pid}:`, error.message);
    return false;
  }
}

/**
 * Check all required ports and report conflicts
 */
async function checkPorts() {
  const conflicts = {};
  
  for (const [service, port] of Object.entries(REQUIRED_PORTS)) {
    const pids = await findProcessOnPort(port);
    if (pids.length > 0) {
      const processDetails = await Promise.all(
        pids.map(pid => getProcessDetails(pid))
      );
      conflicts[service] = { port, pids, processDetails };
    }
  }
  
  return conflicts;
}

/**
 * Kill all processes on required ports
 */
async function clearPorts(conflicts) {
  let allKilled = true;
  
  for (const [service, { port, pids }] of Object.entries(conflicts)) {
    console.log(`\n🔄 Clearing port ${port} for ${service}...`);
    
    for (const pid of pids) {
      const success = await killProcess(pid);
      if (success) {
        console.log(`  ✅ Killed process ${pid}`);
      } else {
        console.log(`  ❌ Failed to kill process ${pid}`);
        allKilled = false;
      }
    }
  }
  
  return allKilled;
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);
  const shouldKill = args.includes('--kill') || args.includes('-k');
  const shouldForce = args.includes('--force') || args.includes('-f');
  
  console.log('🔍 JobbLogg Port Manager - Checking required ports...\n');
  
  const conflicts = await checkPorts();
  
  if (Object.keys(conflicts).length === 0) {
    console.log('✅ All required ports are available!');
    console.log(`   - Vite dev server: http://localhost:${REQUIRED_PORTS.vite}/`);
    console.log(`   - Vite preview: http://localhost:${REQUIRED_PORTS.preview}/`);
    process.exit(0);
  }
  
  // Report conflicts
  console.log('⚠️  Port conflicts detected:\n');
  for (const [service, { port, processDetails }] of Object.entries(conflicts)) {
    console.log(`❌ Port ${port} (${service}) is occupied by:`);
    processDetails.forEach(details => {
      console.log(`   ${details}`);
    });
    console.log('');
  }
  
  if (shouldKill || shouldForce) {
    console.log('🔄 Attempting to clear conflicting processes...');
    const success = await clearPorts(conflicts);
    
    if (success) {
      console.log('\n✅ All conflicting processes cleared!');
      console.log('You can now start the development servers.');
      process.exit(0);
    } else {
      console.log('\n❌ Some processes could not be cleared.');
      console.log('You may need to manually stop them or use sudo.');
      process.exit(1);
    }
  } else {
    console.log('💡 Solutions:');
    console.log('   1. Run with --kill flag to automatically clear ports:');
    console.log('      npm run port-check -- --kill');
    console.log('   2. Manually stop the conflicting processes');
    console.log('   3. Use different ports (not recommended for consistency)');
    process.exit(1);
  }
}

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
  console.error('❌ Unexpected error:', error.message);
  process.exit(1);
});

main();
