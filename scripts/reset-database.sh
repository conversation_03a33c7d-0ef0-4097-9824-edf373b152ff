#!/bin/bash

# JobbLogg Database Reset Script
# This script demonstrates how to use the development utilities to reset the database

set -e  # Exit on any error

echo "🚨 JobbLogg Database Reset Utility"
echo "=================================="
echo ""
echo "⚠️  WARNING: This will permanently delete all project data!"
echo "⚠️  Only use in development/testing environments!"
echo ""

# Check if we're in the right directory
if [ ! -f "convex/schema.ts" ]; then
    echo "❌ Error: Please run this script from the JobbLogg project root directory"
    exit 1
fi

# Check if Convex CLI is available
if ! command -v npx &> /dev/null; then
    echo "❌ Error: npx is not available. Please install Node.js and npm."
    exit 1
fi

# Function to get current data count
get_data_count() {
    echo "📊 Getting current data count..."
    npx convex run clearAllProjectData:getProjectDataCount '{}'
    echo ""
}

# Function to create test data
create_test_data() {
    echo "🧪 Creating test data..."
    echo "Please enter your Clerk user ID (you can find this in the Convex dashboard):"
    read -r USER_ID

    if [ -z "$USER_ID" ]; then
        echo "❌ Error: User ID is required"
        exit 1
    fi

    npx convex run testDataUtilities:createTestData "{\"confirmationCode\": \"CREATE_TEST_DATA\", \"testUserId\": \"$USER_ID\"}"
    echo ""
}

# Function to clear all data
clear_all_data() {
    echo "🗑️  Clearing all project data..."
    npx convex run clearAllProjectData:clearAllProjectData '{"confirmationCode": "DELETE_ALL_PROJECT_DATA"}'
    echo ""
}

# Main menu
while true; do
    echo "What would you like to do?"
    echo "1) Check current data count"
    echo "2) Create test data"
    echo "3) Clear all project data"
    echo "4) Full reset (clear + create test data)"
    echo "5) Exit"
    echo ""
    read -p "Enter your choice (1-5): " choice

    case $choice in
        1)
            get_data_count
            ;;
        2)
            create_test_data
            echo "✅ Test data created successfully!"
            echo ""
            ;;
        3)
            echo ""
            echo "⚠️  Are you sure you want to delete ALL project data?"
            echo "This action cannot be undone!"
            read -p "Type 'yes' to confirm: " confirm
            
            if [ "$confirm" = "yes" ]; then
                clear_all_data
                echo "✅ All project data cleared successfully!"
            else
                echo "❌ Operation cancelled"
            fi
            echo ""
            ;;
        4)
            echo ""
            echo "⚠️  This will delete all existing data and create fresh test data."
            echo "Are you sure you want to proceed?"
            read -p "Type 'yes' to confirm: " confirm
            
            if [ "$confirm" = "yes" ]; then
                echo "📊 Current state before reset:"
                get_data_count
                
                clear_all_data
                echo "✅ All project data cleared!"
                
                create_test_data
                echo "✅ Test data created!"
                
                echo "📊 Final state after reset:"
                get_data_count
                
                echo "🎉 Full database reset completed successfully!"
            else
                echo "❌ Operation cancelled"
            fi
            echo ""
            ;;
        5)
            echo "👋 Goodbye!"
            exit 0
            ;;
        *)
            echo "❌ Invalid choice. Please enter 1-5."
            echo ""
            ;;
    esac
done
