#!/usr/bin/env node

/**
 * JobbLogg Import Validation Script
 * Prevents common import/module resolution issues before they reach testing
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const CONFIG = {
  srcDir: './src',
  convexDir: './convex',
  pageComponentsDir: './src/pages',
  excludePatterns: [
    'node_modules',
    '.git',
    'dist',
    'build',
    '_generated'
  ]
};

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

class ImportValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.pageComponents = new Set();
    this.dynamicImports = new Set();
  }

  log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`);
  }

  error(message) {
    this.errors.push(message);
    this.log(`❌ ERROR: ${message}`, colors.red);
  }

  warning(message) {
    this.warnings.push(message);
    this.log(`⚠️  WARNING: ${message}`, colors.yellow);
  }

  success(message) {
    this.log(`✅ ${message}`, colors.green);
  }

  info(message) {
    this.log(`ℹ️  ${message}`, colors.blue);
  }

  // Find all TypeScript/JavaScript files
  findFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
    const files = [];
    
    const traverse = (currentDir) => {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (!CONFIG.excludePatterns.some(pattern => item.includes(pattern))) {
            traverse(fullPath);
          }
        } else if (extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    };
    
    traverse(dir);
    return files;
  }

  // Analyze file content for import patterns
  analyzeFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative('.', filePath);
    
    // Check for problematic patterns
    this.checkConvexIdImports(content, relativePath);
    this.checkImportPaths(content, relativePath);
    this.checkDynamicImportPatterns(content, relativePath);
    this.checkExportPatterns(content, relativePath);
    
    // Track page components and dynamic imports
    if (filePath.includes('/pages/')) {
      this.pageComponents.add(relativePath);
    }
    
    if (content.includes('import(') || content.includes('lazy(')) {
      this.dynamicImports.add(relativePath);
    }
  }

  // Check for problematic Convex Id imports in page components
  checkConvexIdImports(content, filePath) {
    const hasIdImport = content.includes('import { Id }') || content.includes('import type { Id }');
    const isPageComponent = filePath.includes('/pages/');
    const hasDynamicImport = content.includes('createLazyComponent') || filePath.includes('LazyComponents');
    
    if (hasIdImport && isPageComponent) {
      this.warning(`${filePath}: Page component imports Id type - may cause dynamic import issues`);
    }
    
    if (hasIdImport && hasDynamicImport) {
      this.error(`${filePath}: Component with dynamic import uses Id type - this will cause runtime errors`);
    }
  }

  // Check import path consistency
  checkImportPaths(content, filePath) {
    const importLines = content.split('\n').filter(line => 
      line.trim().startsWith('import ') && !line.includes('//') && !line.includes('/*')
    );
    
    for (const line of importLines) {
      // Check for inconsistent relative path depths
      const convexApiMatch = line.match(/from ['"](.+convex\/_generated\/api)['"]/);
      if (convexApiMatch) {
        const importPath = convexApiMatch[1];
        const expectedDepth = this.calculateExpectedDepth(filePath);
        const actualDepth = (importPath.match(/\.\.\//g) || []).length;
        
        if (actualDepth !== expectedDepth) {
          this.warning(`${filePath}: Inconsistent import depth for Convex API. Expected ${expectedDepth} levels, got ${actualDepth}`);
        }
      }
      
      // Check for absolute imports that should be relative
      if (line.includes('from "src/') || line.includes("from 'src/")) {
        this.warning(`${filePath}: Using absolute import path - consider relative path for consistency`);
      }
    }
  }

  // Calculate expected relative path depth based on file location
  calculateExpectedDepth(filePath) {
    const pathParts = filePath.split('/').filter(part => part !== '.' && part !== 'src');
    return pathParts.length - 1; // Subtract 1 because we don't count the file itself
  }

  // Check dynamic import patterns
  checkDynamicImportPatterns(content, filePath) {
    // Check for lazy component creation
    if (content.includes('createLazyComponent')) {
      const importMatches = content.match(/import\(['"](.+)['"]\)/g);
      if (importMatches) {
        for (const match of importMatches) {
          const importPath = match.match(/['"](.+)['"]/)[1];
          
          // Check if the imported file exists
          const resolvedPath = this.resolveImportPath(importPath, filePath);
          if (resolvedPath && !fs.existsSync(resolvedPath)) {
            this.error(`${filePath}: Dynamic import path does not exist: ${importPath}`);
          }
        }
      }
    }
  }

  // Check export patterns for lazy loading compatibility
  checkExportPatterns(content, filePath) {
    const isPageComponent = filePath.includes('/pages/') && filePath.endsWith('.tsx');
    const hasDefaultExport = content.includes('export default');
    const isIndexFile = filePath.endsWith('/index.ts') || filePath.endsWith('/index.tsx');
    
    if (isPageComponent && !hasDefaultExport && !isIndexFile) {
      this.warning(`${filePath}: Page component should have default export for lazy loading`);
    }
    
    // Check index file export patterns
    if (isIndexFile) {
      const hasBarrelExport = content.includes('export { default }') || content.includes('export * from');
      if (!hasBarrelExport && !content.includes('export default')) {
        this.warning(`${filePath}: Index file should re-export default export`);
      }
    }
  }

  // Resolve import path relative to file location
  resolveImportPath(importPath, fromFile) {
    if (importPath.startsWith('.')) {
      const fromDir = path.dirname(fromFile);
      let resolved = path.resolve(fromDir, importPath);
      
      // Try different extensions
      const extensions = ['.ts', '.tsx', '.js', '.jsx', '/index.ts', '/index.tsx'];
      for (const ext of extensions) {
        const withExt = resolved + ext;
        if (fs.existsSync(withExt)) {
          return withExt;
        }
      }
      
      return resolved;
    }
    return null;
  }

  // Validate TypeScript compilation
  validateTypeScript() {
    this.info('Validating TypeScript compilation...');
    
    try {
      execSync('npx tsc --noEmit', { stdio: 'pipe' });
      this.success('TypeScript compilation successful');
    } catch (error) {
      this.error('TypeScript compilation failed');
      console.log(error.stdout?.toString() || error.message);
    }
  }

  // Test dynamic imports
  async testDynamicImports() {
    this.info('Testing dynamic imports...');
    
    // This would require a more complex setup to actually test dynamic imports
    // For now, we'll just validate the patterns
    
    const lazyComponentsFile = './src/components/LazyComponents.tsx';
    if (fs.existsSync(lazyComponentsFile)) {
      const content = fs.readFileSync(lazyComponentsFile, 'utf8');
      const importMatches = content.match(/import\(['"](.+)['"]\)/g);
      
      if (importMatches) {
        for (const match of importMatches) {
          const importPath = match.match(/['"](.+)['"]/)[1];
          const resolvedPath = this.resolveImportPath(importPath, lazyComponentsFile);
          
          if (!resolvedPath || !fs.existsSync(resolvedPath)) {
            this.error(`LazyComponents.tsx: Import path does not exist: ${importPath}`);
          } else {
            this.success(`Dynamic import validated: ${importPath}`);
          }
        }
      }
    }
  }

  // Generate report
  generateReport() {
    this.log('\n' + '='.repeat(60), colors.bold);
    this.log('JOBBLOGG IMPORT VALIDATION REPORT', colors.bold);
    this.log('='.repeat(60), colors.bold);
    
    this.log(`\nPage Components Found: ${this.pageComponents.size}`);
    this.log(`Dynamic Imports Found: ${this.dynamicImports.size}`);
    
    if (this.errors.length > 0) {
      this.log(`\n❌ ERRORS (${this.errors.length}):`, colors.red);
      this.errors.forEach((error, index) => {
        this.log(`  ${index + 1}. ${error}`, colors.red);
      });
    }
    
    if (this.warnings.length > 0) {
      this.log(`\n⚠️  WARNINGS (${this.warnings.length}):`, colors.yellow);
      this.warnings.forEach((warning, index) => {
        this.log(`  ${index + 1}. ${warning}`, colors.yellow);
      });
    }
    
    if (this.errors.length === 0 && this.warnings.length === 0) {
      this.success('\n🎉 All import validations passed!');
    }
    
    this.log('\n' + '='.repeat(60), colors.bold);
    
    return this.errors.length === 0;
  }

  // Main validation method
  async validate() {
    this.log('🔍 Starting JobbLogg import validation...', colors.bold);
    
    // Find and analyze all files
    const files = this.findFiles(CONFIG.srcDir);
    this.info(`Analyzing ${files.length} files...`);
    
    for (const file of files) {
      this.analyzeFile(file);
    }
    
    // Run additional validations
    this.validateTypeScript();
    await this.testDynamicImports();
    
    // Generate and return report
    return this.generateReport();
  }
}

// Run validation if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new ImportValidator();
  validator.validate().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Validation failed:', error);
    process.exit(1);
  });
}

export default ImportValidator;
