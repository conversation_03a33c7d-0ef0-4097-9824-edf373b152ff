#!/usr/bin/env node

/**
 * JobbLogg Component Template Generator
 * Creates new components with proper import patterns and validation
 */

import fs from 'fs';
import path from 'path';
import readline from 'readline';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

class ComponentGenerator {
  constructor() {
    this.componentName = '';
    this.componentType = '';
    this.componentPath = '';
    this.needsConvex = false;
    this.isDynamic = false;
  }

  async gatherRequirements() {
    log('🚀 JobbLogg Component Generator', colors.bold);
    log('Creating components with proper import patterns\n');

    // Component name
    this.componentName = await question('Component name (e.g., UserProfile): ');
    if (!this.componentName) {
      throw new Error('Component name is required');
    }

    // Component type
    log('\nComponent types:');
    log('1. Page Component (will be dynamically imported)');
    log('2. UI Component (static import)');
    log('3. Feature Component (static import)');
    
    const typeChoice = await question('\nSelect component type (1-3): ');
    
    switch (typeChoice) {
      case '1':
        this.componentType = 'page';
        this.componentPath = `src/pages/${this.componentName}`;
        this.isDynamic = true;
        break;
      case '2':
        this.componentType = 'ui';
        this.componentPath = `src/components/ui/${this.componentName}`;
        break;
      case '3':
        this.componentType = 'feature';
        this.componentPath = `src/components/${this.componentName}`;
        break;
      default:
        throw new Error('Invalid component type');
    }

    // Convex integration
    const convexChoice = await question('\nWill this component use Convex? (y/n): ');
    this.needsConvex = convexChoice.toLowerCase() === 'y';

    log(`\n📋 Component Configuration:`);
    log(`   Name: ${this.componentName}`);
    log(`   Type: ${this.componentType}`);
    log(`   Path: ${this.componentPath}`);
    log(`   Dynamic Import: ${this.isDynamic ? 'Yes' : 'No'}`);
    log(`   Convex Integration: ${this.needsConvex ? 'Yes' : 'No'}`);

    const confirm = await question('\nProceed with generation? (y/n): ');
    if (confirm.toLowerCase() !== 'y') {
      throw new Error('Generation cancelled');
    }
  }

  generateComponentTemplate() {
    const imports = this.generateImports();
    const interfaces = this.generateInterfaces();
    const component = this.generateComponent();
    const exports = this.generateExports();

    return `${imports}

${interfaces}

${component}

${exports}`;
  }

  generateImports() {
    const imports = ["import React from 'react';"];

    if (this.needsConvex) {
      // Calculate relative path depth
      const depth = this.componentPath.split('/').length - 1;
      const relativePath = '../'.repeat(depth);
      
      imports.push(`import { useQuery, useMutation } from 'convex/react';`);
      imports.push(`import { useUser } from '@clerk/clerk-react';`);
      imports.push(`import { api } from '${relativePath}convex/_generated/api';`);
    }

    // UI component imports
    const uiDepth = this.componentType === 'page' ? '../../' : '../';
    imports.push(`import { PageLayout, Heading2, TextMedium, PrimaryButton } from '${uiDepth}components/ui';`);

    return imports.join('\n');
  }

  generateInterfaces() {
    let interfaces = `interface ${this.componentName}Props {`;
    
    if (this.needsConvex && this.isDynamic) {
      // Use string types for dynamic imports
      interfaces += `
  projectId?: string; // Use string instead of Id<"projects"> for dynamic imports`;
    } else if (this.needsConvex) {
      // Can use Id types for static imports
      interfaces += `
  projectId?: Id<"projects">;`;
    }
    
    interfaces += `
  className?: string;
}`;

    return interfaces;
  }

  generateComponent() {
    let component = `const ${this.componentName}: React.FC<${this.componentName}Props> = ({`;
    
    if (this.needsConvex) {
      component += ` projectId,`;
    }
    
    component += ` className }) => {`;

    if (this.needsConvex) {
      component += `
  const { user } = useUser();
  
  // Example query - adjust based on your needs
  const data = useQuery(api.projects.get, 
    projectId ? { projectId } : "skip"
  );
  
  // Example mutation - adjust based on your needs
  const updateData = useMutation(api.projects.update);`;
    }

    component += `

  return (
    <PageLayout>
      <div className={\`space-y-6 \${className || ''}\`}>
        <Heading2>${this.componentName}</Heading2>
        
        <div className="bg-white rounded-xl shadow-sm border border-jobblogg-border p-6">
          <TextMedium>
            Component content goes here...
          </TextMedium>
          
          <div className="mt-4">
            <PrimaryButton onClick={() => console.log('Button clicked')}>
              Action Button
            </PrimaryButton>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};`;

    return component;
  }

  generateExports() {
    if (this.isDynamic) {
      return `export default ${this.componentName};`;
    } else {
      return `export { ${this.componentName} };`;
    }
  }

  generateIndexFile() {
    if (this.isDynamic) {
      return `export { default } from './${this.componentName}';`;
    } else {
      return `export { ${this.componentName} } from './${this.componentName}';`;
    }
  }

  generateTestFile() {
    return `import React from 'react';
import { render, screen } from '@testing-library/react';
import { ${this.componentName} } from './${this.componentName}';

// Mock Convex hooks if needed
${this.needsConvex ? `
jest.mock('convex/react', () => ({
  useQuery: jest.fn(() => null),
  useMutation: jest.fn(() => jest.fn()),
}));

jest.mock('@clerk/clerk-react', () => ({
  useUser: jest.fn(() => ({ user: { id: 'test-user' } })),
}));` : ''}

describe('${this.componentName}', () => {
  it('renders without crashing', () => {
    render(<${this.componentName} />);
    expect(screen.getByText('${this.componentName}')).toBeInTheDocument();
  });

  ${this.isDynamic ? `
  it('can be dynamically imported', async () => {
    const { default: Component } = await import('./${this.componentName}');
    expect(Component).toBeDefined();
    
    render(<Component />);
    expect(screen.getByText('${this.componentName}')).toBeInTheDocument();
  });` : ''}

  // Add more tests as needed
});`;
  }

  async createFiles() {
    // Create directory
    if (!fs.existsSync(this.componentPath)) {
      fs.mkdirSync(this.componentPath, { recursive: true });
    }

    // Create component file
    const componentFile = path.join(this.componentPath, `${this.componentName}.tsx`);
    fs.writeFileSync(componentFile, this.generateComponentTemplate());
    log(`✅ Created: ${componentFile}`, colors.green);

    // Create index file
    const indexFile = path.join(this.componentPath, 'index.ts');
    fs.writeFileSync(indexFile, this.generateIndexFile());
    log(`✅ Created: ${indexFile}`, colors.green);

    // Create test file
    const testFile = path.join(this.componentPath, `${this.componentName}.test.tsx`);
    fs.writeFileSync(testFile, this.generateTestFile());
    log(`✅ Created: ${testFile}`, colors.green);

    // Update lazy components if it's a page component
    if (this.isDynamic) {
      await this.updateLazyComponents();
    }

    // Update UI barrel exports if it's a UI component
    if (this.componentType === 'ui') {
      await this.updateUIBarrelExports();
    }
  }

  async updateLazyComponents() {
    const lazyComponentsPath = 'src/components/LazyComponents.tsx';
    
    if (fs.existsSync(lazyComponentsPath)) {
      let content = fs.readFileSync(lazyComponentsPath, 'utf8');
      
      // Add lazy component export
      const lazyExport = `
export const Lazy${this.componentName} = createLazyComponent(
  () => import('../pages/${this.componentName}')
);`;
      
      // Insert before the wrapper component
      const insertPoint = content.indexOf('// Wrapper component');
      if (insertPoint !== -1) {
        content = content.slice(0, insertPoint) + lazyExport + '\n\n' + content.slice(insertPoint);
        fs.writeFileSync(lazyComponentsPath, content);
        log(`✅ Updated: ${lazyComponentsPath}`, colors.green);
      }
    }
  }

  async updateUIBarrelExports() {
    const uiIndexPath = 'src/components/ui/index.ts';
    
    if (fs.existsSync(uiIndexPath)) {
      let content = fs.readFileSync(uiIndexPath, 'utf8');
      
      // Add export
      const exportLine = `export { ${this.componentName} } from './${this.componentName}';`;
      content += '\n' + exportLine;
      
      fs.writeFileSync(uiIndexPath, content);
      log(`✅ Updated: ${uiIndexPath}`, colors.green);
    }
  }

  generateUsageInstructions() {
    log('\n📖 Usage Instructions:', colors.bold);
    
    if (this.isDynamic) {
      log(`
1. Add route to App.tsx:
   <Route path="/${this.componentName.toLowerCase()}" element={
     <LazyPageWrapper>
       <Lazy${this.componentName} />
     </LazyPageWrapper>
   } />

2. Import in App.tsx:
   import { Lazy${this.componentName} } from './components/LazyComponents';

3. Test the route:
   - Navigate to /${this.componentName.toLowerCase()}
   - Check browser Network tab for chunk loading
   - Verify no dynamic import errors in console`);
    } else {
      log(`
1. Import the component:
   import { ${this.componentName} } from './components/${this.componentType}/${this.componentName}';

2. Use in your JSX:
   <${this.componentName} />

3. Run tests:
   npm test -- ${this.componentName}.test.tsx`);
    }

    log(`
4. Validate imports:
   node scripts/validate-imports.js

5. Check TypeScript:
   npm run type-check`, colors.blue);
  }

  async generate() {
    try {
      await this.gatherRequirements();
      await this.createFiles();
      this.generateUsageInstructions();
      
      log('\n🎉 Component generated successfully!', colors.green);
      log('Remember to test the component before committing.', colors.yellow);
      
    } catch (error) {
      log(`\n❌ Error: ${error.message}`, colors.red);
      process.exit(1);
    } finally {
      rl.close();
    }
  }
}

// Run generator if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const generator = new ComponentGenerator();
  generator.generate();
}

export default ComponentGenerator;
