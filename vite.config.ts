import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 5173,
    strictPort: true, // Fail if port 5173 is occupied instead of trying other ports
    host: 'localhost',
    open: false // Don't auto-open browser to avoid conflicts
  },
  preview: {
    port: 4173,
    strictPort: true,
    host: 'localhost'
  }
})
