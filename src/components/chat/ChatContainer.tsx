import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import type { Id } from '../../../convex/_generated/dataModel';
import { ChatContainerProps, MessageFormData, ChatError, TypingIndicator, OptimisticMessage, MessageWithDisplayInfo } from '../../types/chat';
import { MessageList } from './MessageList';
import { VirtualizedMessageList } from './VirtualizedMessageList';
import { MessageInput } from './MessageInput';
import { TypingIndicator as TypingIndicatorComponent } from './TypingIndicator';
import { ConnectionStatus } from './ConnectionStatus';
import { useKeyboardNavigation } from '../../hooks/useKeyboardNavigation';
import { ScreenReaderAnnouncer, ChatScreenReaderContext, useScreenReaderAnnouncements } from './ScreenReaderAnnouncer';

export const ChatContainer: React.FC<ChatContainerProps & {
  enableVirtualization?: boolean;
}> = ({
  logId,
  userId,
  userRole,
  className = '',
  enableVirtualization = true
}) => {
  const [error, setError] = useState<ChatError | null>(null);
  const [replyingTo, setReplyingTo] = useState<Id<"messages"> | null>(null);
  const [optimisticMessages, setOptimisticMessages] = useState<OptimisticMessage[]>([]);
  const [typingUsers, setTypingUsers] = useState<TypingIndicator[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Pagination state
  const [messageLimit, setMessageLimit] = useState(50);
  const [messageCursor, setMessageCursor] = useState<number | undefined>(undefined);

  // Convex hooks
  const messagesData = useQuery(api.messages.getMessagesWithDisplayNames, {
    logId,
    userId,
    userRole,
    limit: messageLimit,
    cursor: messageCursor
  });

  const sendMessage = useMutation(api.messages.sendMessage);
  const markAsRead = useMutation(api.messages.markAsRead);
  const addReaction = useMutation(api.messages.addReaction);
  const removeReaction = useMutation(api.messages.removeReaction);
  const editMessage = useMutation(api.messages.editMessage);
  const deleteMessage = useMutation(api.messages.deleteMessage);
  const markMessageDelivered = useMutation(api.messages.markMessageDelivered);

  // Handle retry for failed messages
  const handleRetryMessage = useCallback(async (optimisticId: string) => {
    const failedMessage = optimisticMessages.find(msg => msg._id === optimisticId);
    if (!failedMessage) return;

    // Mark as sending again
    setOptimisticMessages(prev =>
      prev.map(msg =>
        msg._id === optimisticId
          ? { ...msg, isSending: true, sendError: undefined }
          : msg
      )
    );

    try {
      // Retry sending the message
      await sendMessage({
        logId: failedMessage.logId,
        parentId: failedMessage.parentId,
        senderId: failedMessage.senderId,
        senderRole: failedMessage.senderRole,
        text: failedMessage.text,
        file: failedMessage.file
      });

      // Remove optimistic message after successful retry
      setOptimisticMessages(prev => prev.filter(msg => msg._id !== optimisticId));
    } catch (err) {
      // Mark as failed again
      setOptimisticMessages(prev =>
        prev.map(msg =>
          msg._id === optimisticId
            ? { ...msg, isSending: false, sendError: err instanceof Error ? err.message : 'Kunne ikke sende melding' }
            : msg
        )
      );
    }
  }, [optimisticMessages, sendMessage]);

  // Handle sending messages with optimistic updates
  const handleSendMessage = useCallback(async (data: MessageFormData) => {
    try {
      setError(null);

      // Create optimistic message for immediate UI feedback
      const optimisticId = `optimistic-${Date.now()}-${Math.random()}`;
      const optimisticMessage: OptimisticMessage = {
        _id: optimisticId,
        _creationTime: Date.now(),
        logId,
        parentId: data.parentId || replyingTo,
        senderId: userId,
        senderRole: userRole,
        text: data.text,
        file: data.file ? {
          url: data.file.url || '',
          name: data.file.name,
          size: data.file.size,
          type: data.file.type
        } : undefined,
        createdAt: Date.now(),
        isOptimistic: true,
        isSending: true
      };

      // Add optimistic message to state
      setOptimisticMessages(prev => [...prev, optimisticMessage]);

      // Send actual message
      await sendMessage({
        logId,
        parentId: data.parentId || replyingTo,
        senderId: userId,
        senderRole: userRole,
        text: data.text,
        file: data.file ? {
          url: data.file.url || '',
          name: data.file.name,
          size: data.file.size,
          type: data.file.type
        } : undefined
      });

      // Remove optimistic message after successful send
      setOptimisticMessages(prev => prev.filter(msg => msg._id !== optimisticId));

      // Clear reply state after sending
      setReplyingTo(null);
    } catch (err) {
      // Mark optimistic message as failed
      setOptimisticMessages(prev =>
        prev.map(msg =>
          msg._id === optimisticId
            ? { ...msg, isSending: false, sendError: err instanceof Error ? err.message : 'Kunne ikke sende melding' }
            : msg
        )
      );

      setError({
        type: 'unknown',
        message: err instanceof Error ? err.message : 'Kunne ikke sende melding',
        timestamp: Date.now()
      });
    }
  }, [sendMessage, logId, userId, userRole, replyingTo]);

  // Handle message reactions
  const handleReaction = useCallback(async (messageId: Id<"messages">, emoji: string) => {
    try {
      setError(null);
      
      // Check if user already reacted with this emoji
      const message = messagesData?.messages.find(m => 
        m._id === messageId || m.replies.some(r => r._id === messageId)
      );
      
      if (!message) return;
      
      const targetMessage = message._id === messageId ? message : 
        message.replies.find(r => r._id === messageId);
      
      if (!targetMessage) return;
      
      const existingReaction = targetMessage.reactions?.find(r => r.emoji === emoji);
      const userHasReacted = existingReaction?.userIds.includes(userId);
      
      if (userHasReacted) {
        await removeReaction({ messageId, userId, emoji });
      } else {
        await addReaction({ messageId, userId, emoji });
      }
    } catch (err) {
      setError({
        type: 'unknown',
        message: err instanceof Error ? err.message : 'Kunne ikke reagere på melding',
        timestamp: Date.now()
      });
    }
  }, [addReaction, removeReaction, userId, messagesData]);

  // Handle message editing
  const handleEdit = useCallback(async (messageId: Id<"messages">) => {
    // TODO: Implement edit functionality
    console.log('Edit message:', messageId);
  }, []);

  // Handle message deletion
  const handleDelete = useCallback(async (messageId: Id<"messages">) => {
    try {
      setError(null);
      await deleteMessage({ messageId, userId });
    } catch (err) {
      setError({
        type: 'unknown',
        message: err instanceof Error ? err.message : 'Kunne ikke slette melding',
        timestamp: Date.now()
      });
    }
  }, [deleteMessage, userId]);

  // Handle reply
  const handleReply = useCallback((messageId: Id<"messages">) => {
    setReplyingTo(messageId);
  }, []);

  // Handle cancel reply
  const handleCancelReply = useCallback(() => {
    setReplyingTo(null);
  }, []);

  // Handle typing indicators
  const handleTypingStart = useCallback(() => {
    if (!isTyping) {
      setIsTyping(true);
      // TODO: Send typing indicator to other users via Convex mutation
    }

    // Reset typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      // TODO: Send stop typing indicator to other users
    }, 3000);
  }, [isTyping]);

  const handleTypingStop = useCallback(() => {
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    setIsTyping(false);
    // TODO: Send stop typing indicator to other users
  }, []);

  // Merge optimistic messages with real messages
  const mergedMessages = React.useMemo(() => {
    if (!messagesData?.messages) return [];

    const realMessages = messagesData.messages;
    const optimisticAsDisplay: MessageWithDisplayInfo[] = optimisticMessages.map(opt => ({
      ...opt,
      senderDisplayName: userRole === 'contractor' ? 'Leverandør' : 'Kunde',
      isOwnMessage: true,
      replies: []
    }));

    // Add optimistic messages to the end
    return [...realMessages, ...optimisticAsDisplay];
  }, [messagesData?.messages, optimisticMessages, userRole]);

  // Mark messages as read when component mounts or messages change
  React.useEffect(() => {
    if (messagesData?.messages.length) {
      markAsRead({ logId, userId, userRole }).catch(console.error);
    }
  }, [markAsRead, logId, userId, userRole, messagesData?.messages.length]);

  // Mark messages as delivered when they are viewed
  React.useEffect(() => {
    if (messagesData?.messages.length) {
      // Mark all messages from other users as delivered
      messagesData.messages.forEach(message => {
        if (message.senderId !== userId && message.deliveryStatus !== 'delivered') {
          markMessageDelivered({
            messageId: message._id,
            userId,
            userRole
          }).catch(console.error);
        }
      });
    }
  }, [markMessageDelivered, userId, userRole, messagesData?.messages]);

  // Load more messages function
  const handleLoadMore = useCallback(() => {
    if (messagesData?.hasMore && messagesData?.nextCursor) {
      setMessageCursor(messagesData.nextCursor);
      setMessageLimit(prev => prev + 50);
    }
  }, [messagesData?.hasMore, messagesData?.nextCursor]);

  // Screen reader announcements
  const { announcement, announcePolite, announceAssertive } = useScreenReaderAnnouncements();

  // Announce new messages to screen readers
  const prevMessageCount = useRef(0);
  useEffect(() => {
    if (messagesData?.messages && messagesData.messages.length > prevMessageCount.current) {
      const newMessageCount = messagesData.messages.length - prevMessageCount.current;
      if (newMessageCount > 0 && prevMessageCount.current > 0) {
        announcePolite(`${newMessageCount} ny${newMessageCount > 1 ? 'e' : ''} melding${newMessageCount > 1 ? 'er' : ''}`);
      }
    }
    prevMessageCount.current = messagesData?.messages?.length || 0;
  }, [messagesData?.messages, announcePolite]);

  // Announce typing status changes
  useEffect(() => {
    if (typingUsers.length > 0) {
      const typingNames = typingUsers.map(user => user.displayName).join(', ');
      announcePolite(`${typingNames} skriver`);
    }
  }, [typingUsers, announcePolite]);

  // Keyboard navigation for chat
  useKeyboardNavigation({
    onEscape: () => {
      // Clear reply state on Escape
      if (replyingTo) {
        setReplyingTo(null);
        announcePolite('Avbrøt svar på melding');
      }
    },
    enabled: true,
    preventDefault: false // Let other components handle their own prevention
  });

  // Cleanup typing timeout on unmount
  React.useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  if (!messagesData) {
    return (
      <div className={`jobblogg-chat-container ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`jobblogg-chat-container flex flex-col h-full ${className}`}
      role="main"
      aria-label="Chat-samtale"
    >
      {/* Screen reader announcements */}
      <ScreenReaderAnnouncer
        message={announcement?.message}
        priority={announcement?.priority}
      />

      {/* Chat context for screen readers */}
      <ChatScreenReaderContext
        totalMessages={mergedMessages.length}
        unreadCount={0} // TODO: Calculate unread count
        isTyping={typingUsers.length > 0}
        typingUsers={typingUsers.map(user => user.displayName)}
        connectionStatus="connected" // TODO: Get actual connection status
      />

      {/* Connection status */}
      <div className="flex justify-center mb-2" role="status" aria-live="polite">
        <ConnectionStatus />
      </div>

      {/* Error display */}
      {error && (
        <div
          className="bg-jobblogg-error/10 border border-jobblogg-error/20 text-jobblogg-error px-4 py-3 rounded-lg mb-4"
          role="alert"
          aria-live="assertive"
        >
          <p className="text-sm font-medium">{error.message}</p>
          <button
            onClick={() => setError(null)}
            className="text-xs underline mt-1 hover:no-underline focus:outline-none focus:ring-2 focus:ring-jobblogg-error/20 rounded"
            aria-label="Lukk feilmelding"
          >
            Lukk
          </button>
        </div>
      )}

      {/* Load more messages button */}
      {messagesData?.hasMore && (
        <div className="flex justify-center py-4 border-b border-jobblogg-border">
          <button
            onClick={handleLoadMore}
            className="px-4 py-2 text-sm text-jobblogg-primary hover:text-jobblogg-primary-dark border border-jobblogg-primary hover:border-jobblogg-primary-dark rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20"
            aria-label="Last inn flere meldinger fra samtalehistorikken"
          >
            Last inn flere meldinger
          </button>
        </div>
      )}

      {/* Messages list */}
      <div
        className="flex-1 overflow-hidden"
        role="log"
        aria-label="Meldinger i samtalen"
        aria-live="polite"
      >
        {enableVirtualization ? (
          <VirtualizedMessageList
            messages={mergedMessages}
            userId={userId}
            onReply={handleReply}
            onReaction={handleReaction}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onRetry={handleRetryMessage}
            enableVirtualization={mergedMessages.length > 20}
          />
        ) : (
          <MessageList
            messages={mergedMessages}
            userId={userId}
            onReply={handleReply}
            onReaction={handleReaction}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onRetry={handleRetryMessage}
          />
        )}
      </div>

      {/* Typing indicator */}
      {typingUsers.length > 0 && (
        <div role="status" aria-live="polite" aria-label="Skriveindikatorer">
          <TypingIndicatorComponent
            typingUsers={typingUsers}
            className="border-t border-jobblogg-border"
          />
        </div>
      )}

      {/* Reply indicator */}
      {replyingTo && (
        <div
          className="bg-jobblogg-card border-l-4 border-jobblogg-primary px-4 py-2 mb-4"
          role="status"
          aria-label="Svarer på melding"
        >
          <div className="flex items-center justify-between">
            <span className="text-sm text-jobblogg-text-muted">
              Svarer på melding...
            </span>
            <button
              onClick={handleCancelReply}
              className="text-jobblogg-text-muted hover:text-jobblogg-text-strong text-sm focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20 rounded px-2 py-1"
              aria-label="Avbryt svar på melding"
            >
              Avbryt
            </button>
          </div>
        </div>
      )}

      {/* Message input */}
      <MessageInput
        logId={logId}
        userId={userId}
        userRole={userRole}
        parentId={replyingTo}
        onSend={handleSendMessage}
        onCancel={replyingTo ? handleCancelReply : undefined}
        onTypingStart={handleTypingStart}
        onTypingStop={handleTypingStop}
        placeholder={replyingTo ? "Skriv et svar..." : "Skriv en melding..."}
      />
    </div>
  );
};
