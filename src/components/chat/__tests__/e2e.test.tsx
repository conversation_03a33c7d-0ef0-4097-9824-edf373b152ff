import { test, expect } from '@playwright/test';

// End-to-End tests for JobbLogg Chat System
// These tests simulate real user interactions with the chat system

test.describe('JobbLogg Chat System E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the chat page
    await page.goto('/project/test-project-id');
    
    // Wait for the chat to load
    await page.waitForSelector('[role="main"][aria-label="Chat-samtale"]');
  });

  test.describe('Basic Chat Functionality', () => {
    test('should load chat interface correctly', async ({ page }) => {
      // Verify main chat elements are present
      await expect(page.locator('[role="main"][aria-label="Chat-samtale"]')).toBeVisible();
      await expect(page.locator('[role="log"][aria-label="Meldinger i samtalen"]')).toBeVisible();
      await expect(page.locator('[aria-label="Skriv melding"]')).toBeVisible();
      await expect(page.locator('button:has-text("Send")')).toBeVisible();
    });

    test('should send a message successfully', async ({ page }) => {
      const messageText = 'Dette er en testmelding fra E2E test';
      
      // Type message
      await page.fill('[aria-label="Skriv melding"]', messageText);
      
      // Send message
      await page.click('button:has-text("Send")');
      
      // Verify message appears in chat
      await expect(page.locator(`text=${messageText}`)).toBeVisible();
      
      // Verify input is cleared
      await expect(page.locator('[aria-label="Skriv melding"]')).toHaveValue('');
    });

    test('should send message with Enter key', async ({ page }) => {
      const messageText = 'Melding sendt med Enter-tasten';
      
      // Type message
      await page.fill('[aria-label="Skriv melding"]', messageText);
      
      // Press Enter
      await page.press('[aria-label="Skriv melding"]', 'Enter');
      
      // Verify message appears
      await expect(page.locator(`text=${messageText}`)).toBeVisible();
    });

    test('should create new line with Shift+Enter', async ({ page }) => {
      const textarea = page.locator('[aria-label="Skriv melding"]');
      
      // Type first line
      await textarea.fill('Første linje');
      
      // Press Shift+Enter for new line
      await textarea.press('Shift+Enter');
      
      // Type second line
      await textarea.type('Andre linje');
      
      // Verify textarea contains both lines
      await expect(textarea).toHaveValue('Første linje\nAndre linje');
    });
  });

  test.describe('Message Interactions', () => {
    test('should reply to a message', async ({ page }) => {
      // First, send a message to reply to
      await page.fill('[aria-label="Skriv melding"]', 'Original melding');
      await page.click('button:has-text("Send")');
      
      // Wait for message to appear
      await expect(page.locator('text=Original melding')).toBeVisible();
      
      // Hover over message to show actions
      await page.hover('[role="article"]:has-text("Original melding")');
      
      // Click reply button
      await page.click('[aria-label="Svar på melding"]');
      
      // Verify reply context is shown
      await expect(page.locator('text=Svarer på')).toBeVisible();
      
      // Send reply
      await page.fill('[aria-label="Skriv melding"]', 'Dette er et svar');
      await page.click('button:has-text("Send")');
      
      // Verify reply appears
      await expect(page.locator('text=Dette er et svar')).toBeVisible();
    });

    test('should add reaction to message', async ({ page }) => {
      // Send a message first
      await page.fill('[aria-label="Skriv melding"]', 'Melding for reaksjon');
      await page.click('button:has-text("Send")');
      
      // Wait for message
      await expect(page.locator('text=Melding for reaksjon')).toBeVisible();
      
      // Hover over message
      await page.hover('[role="article"]:has-text("Melding for reaksjon")');
      
      // Click reaction button
      await page.click('[aria-label="Legg til tommel opp reaksjon"]');
      
      // Verify reaction appears (this would depend on your reaction UI)
      // await expect(page.locator('text=👍')).toBeVisible();
    });

    test('should edit own message', async ({ page }) => {
      // Send a message first
      await page.fill('[aria-label="Skriv melding"]', 'Melding som skal redigeres');
      await page.click('button:has-text("Send")');
      
      // Wait for message
      await expect(page.locator('text=Melding som skal redigeres')).toBeVisible();
      
      // Hover over message
      await page.hover('[role="article"]:has-text("Melding som skal redigeres")');
      
      // Click edit button
      await page.click('[aria-label="Rediger melding"]');
      
      // This would open an edit modal or inline editor
      // The exact implementation depends on your edit UI
    });
  });

  test.describe('File Upload', () => {
    test('should upload and send file', async ({ page }) => {
      // Create a test file
      const fileContent = 'Dette er testinnhold for fil';
      
      // Click file upload button
      await page.click('[aria-label="Last opp fil"]');
      
      // Upload file
      await page.setInputFiles('input[type="file"]', {
        name: 'test.txt',
        mimeType: 'text/plain',
        buffer: Buffer.from(fileContent)
      });
      
      // Verify file preview appears
      await expect(page.locator('text=test.txt')).toBeVisible();
      
      // Send message with file
      await page.click('button:has-text("Send")');
      
      // Verify file attachment appears in chat
      await expect(page.locator('text=test.txt')).toBeVisible();
    });

    test('should remove uploaded file before sending', async ({ page }) => {
      // Upload file
      await page.click('[aria-label="Last opp fil"]');
      await page.setInputFiles('input[type="file"]', {
        name: 'test.txt',
        mimeType: 'text/plain',
        buffer: Buffer.from('test content')
      });
      
      // Verify file appears
      await expect(page.locator('text=test.txt')).toBeVisible();
      
      // Remove file
      await page.click('[aria-label="Fjern fil"]');
      
      // Verify file is removed
      await expect(page.locator('text=test.txt')).not.toBeVisible();
    });
  });

  test.describe('Keyboard Navigation', () => {
    test('should navigate with Tab key', async ({ page }) => {
      // Focus textarea
      await page.focus('[aria-label="Skriv melding"]');
      
      // Tab to send button
      await page.keyboard.press('Tab');
      await expect(page.locator('button:has-text("Send")')).toBeFocused();
      
      // Tab to file upload
      await page.keyboard.press('Tab');
      await expect(page.locator('[aria-label="Last opp fil"]')).toBeFocused();
    });

    test('should cancel reply with Escape', async ({ page }) => {
      // Send a message first
      await page.fill('[aria-label="Skriv melding"]', 'Test melding');
      await page.click('button:has-text("Send")');
      
      // Start reply
      await page.hover('[role="article"]:has-text("Test melding")');
      await page.click('[aria-label="Svar på melding"]');
      
      // Verify reply context
      await expect(page.locator('text=Svarer på')).toBeVisible();
      
      // Press Escape
      await page.keyboard.press('Escape');
      
      // Verify reply context is gone
      await expect(page.locator('text=Svarer på')).not.toBeVisible();
    });
  });

  test.describe('Accessibility', () => {
    test('should have proper ARIA labels', async ({ page }) => {
      // Check main landmarks
      await expect(page.locator('[role="main"][aria-label="Chat-samtale"]')).toBeVisible();
      await expect(page.locator('[role="log"][aria-label="Meldinger i samtalen"]')).toBeVisible();
      await expect(page.locator('[role="status"]')).toBeVisible();
      
      // Check form labels
      await expect(page.locator('[aria-label="Skriv melding"]')).toBeVisible();
      await expect(page.locator('[aria-label="Last opp fil"]')).toBeVisible();
    });

    test('should announce new messages to screen readers', async ({ page }) => {
      // This test would require screen reader simulation
      // For now, we verify the live regions exist
      await expect(page.locator('[aria-live="polite"]')).toBeVisible();
    });

    test('should have proper focus indicators', async ({ page }) => {
      // Focus textarea and verify focus is visible
      await page.focus('[aria-label="Skriv melding"]');
      
      // Check that focus styles are applied (this depends on your CSS)
      const textarea = page.locator('[aria-label="Skriv melding"]');
      await expect(textarea).toBeFocused();
      
      // You could also check computed styles if needed
      // const focusRing = await textarea.evaluate(el => 
      //   window.getComputedStyle(el).getPropertyValue('box-shadow')
      // );
      // expect(focusRing).toContain('rgb'); // Focus ring color
    });
  });

  test.describe('Real-time Features', () => {
    test('should show connection status', async ({ page }) => {
      // Verify connection status indicator exists
      await expect(page.locator('[role="status"]')).toBeVisible();
      
      // In a real implementation, you might test different connection states
      // by mocking network conditions
    });

    test('should display typing indicators', async ({ page }) => {
      // This would require multiple browser contexts to simulate
      // different users typing simultaneously
      
      // For now, verify the structure exists
      await expect(page.locator('[role="main"]')).toBeVisible();
    });
  });

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // Mock network failure
      await page.route('**/api/chat/**', route => route.abort());
      
      // Try to send message
      await page.fill('[aria-label="Skriv melding"]', 'Test melding');
      await page.click('button:has-text("Send")');
      
      // Verify error message appears
      await expect(page.locator('[role="alert"]')).toBeVisible();
    });

    test('should retry failed messages', async ({ page }) => {
      // This would test the retry mechanism for failed messages
      // Implementation depends on your error handling strategy
      
      await expect(page.locator('[role="main"]')).toBeVisible();
    });
  });

  test.describe('Performance', () => {
    test('should handle large message lists efficiently', async ({ page }) => {
      // This test would verify virtual scrolling works correctly
      // with large numbers of messages
      
      // For now, verify the basic structure
      await expect(page.locator('[role="log"]')).toBeVisible();
    });

    test('should load quickly', async ({ page }) => {
      const startTime = Date.now();
      
      // Navigate to chat
      await page.goto('/project/test-project-id');
      
      // Wait for chat to be ready
      await page.waitForSelector('[role="main"][aria-label="Chat-samtale"]');
      
      const loadTime = Date.now() - startTime;
      
      // Verify reasonable load time (adjust threshold as needed)
      expect(loadTime).toBeLessThan(3000); // 3 seconds
    });
  });
});
