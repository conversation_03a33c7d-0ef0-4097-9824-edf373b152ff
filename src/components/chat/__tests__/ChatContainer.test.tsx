import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ChatContainer } from '../ChatContainer';
import { ConvexProvider } from 'convex/react';
import { ConvexReactClient } from 'convex/react';

// Mock Convex client
const mockConvex = new ConvexReactClient('https://test.convex.cloud');

// Mock data
const mockMessages = [
  {
    _id: 'msg1' as any,
    logId: 'log1' as any,
    senderId: 'user1',
    senderRole: 'contractor' as const,
    senderDisplayName: 'Leverandør',
    text: 'Test message 1',
    createdAt: Date.now() - 1000,
    isOwnMessage: true,
    replies: []
  },
  {
    _id: 'msg2' as any,
    logId: 'log1' as any,
    senderId: 'user2',
    senderRole: 'customer' as const,
    senderDisplayName: 'Kunde',
    text: 'Test message 2',
    createdAt: Date.now(),
    isOwnMessage: false,
    replies: []
  }
];

// Mock Convex hooks
vi.mock('convex/react', async () => {
  const actual = await vi.importActual('convex/react');
  return {
    ...actual,
    useQuery: vi.fn(() => ({ messages: mockMessages })),
    useMutation: vi.fn(() => vi.fn()),
  };
});

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ConvexProvider client={mockConvex}>
    {children}
  </ConvexProvider>
);

describe('ChatContainer', () => {
  const defaultProps = {
    logId: 'log1' as any,
    userId: 'user1',
    userRole: 'contractor' as const
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders chat container with messages', () => {
    render(
      <TestWrapper>
        <ChatContainer {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByRole('main')).toBeInTheDocument();
    expect(screen.getByLabelText('Chat-samtale')).toBeInTheDocument();
  });

  it('displays connection status', () => {
    render(
      <TestWrapper>
        <ChatContainer {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByRole('status')).toBeInTheDocument();
  });

  it('shows message input', () => {
    render(
      <TestWrapper>
        <ChatContainer {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Skriv melding')).toBeInTheDocument();
    expect(screen.getByText('Send')).toBeInTheDocument();
  });

  it('handles keyboard navigation - Escape clears reply', async () => {
    render(
      <TestWrapper>
        <ChatContainer {...defaultProps} />
      </TestWrapper>
    );

    // Simulate Escape key
    fireEvent.keyDown(document, { key: 'Escape' });

    // Should not crash and should handle escape properly
    expect(screen.getByRole('main')).toBeInTheDocument();
  });

  it('displays error messages with proper accessibility', () => {
    const ChatContainerWithError = () => {
      const [error, setError] = React.useState({ message: 'Test error' });
      
      return (
        <TestWrapper>
          <ChatContainer {...defaultProps} />
        </TestWrapper>
      );
    };

    render(<ChatContainerWithError />);
    
    // Error should be announced to screen readers
    expect(screen.getByRole('main')).toBeInTheDocument();
  });

  it('supports virtualization toggle', () => {
    render(
      <TestWrapper>
        <ChatContainer {...defaultProps} enableVirtualization={false} />
      </TestWrapper>
    );

    expect(screen.getByRole('main')).toBeInTheDocument();
  });

  it('has proper ARIA labels and roles', () => {
    render(
      <TestWrapper>
        <ChatContainer {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByRole('main')).toHaveAttribute('aria-label', 'Chat-samtale');
    expect(screen.getByRole('log')).toHaveAttribute('aria-label', 'Meldinger i samtalen');
  });

  it('announces new messages to screen readers', async () => {
    const { rerender } = render(
      <TestWrapper>
        <ChatContainer {...defaultProps} />
      </TestWrapper>
    );

    // Check for screen reader announcements
    expect(screen.getByRole('status')).toBeInTheDocument();
  });
});
