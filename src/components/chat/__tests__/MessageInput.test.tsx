import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { MessageInput } from '../MessageInput';

describe('MessageInput', () => {
  const defaultProps = {
    onSend: vi.fn(),
    placeholder: 'Skriv en melding...',
    disabled: false
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders message input with proper accessibility', () => {
    render(<MessageInput {...defaultProps} />);

    const textarea = screen.getByLabelText('Skriv melding');
    expect(textarea).toBeInTheDocument();
    expect(textarea).toHaveAttribute('placeholder', 'Skriv en melding...');
    expect(screen.getByText('Send')).toBeInTheDocument();
  });

  it('handles text input', async () => {
    const user = userEvent.setup();
    render(<MessageInput {...defaultProps} />);

    const textarea = screen.getByLabelText('Skriv melding');
    await user.type(textarea, 'Test message');

    expect(textarea).toHaveValue('Test message');
  });

  it('sends message on Enter key', async () => {
    const user = userEvent.setup();
    const onSend = vi.fn();
    render(<MessageInput {...defaultProps} onSend={onSend} />);

    const textarea = screen.getByLabelText('Skriv melding');
    await user.type(textarea, 'Test message');
    await user.keyboard('{Enter}');

    expect(onSend).toHaveBeenCalledWith({
      text: 'Test message',
      files: []
    });
  });

  it('allows new line with Shift+Enter', async () => {
    const user = userEvent.setup();
    render(<MessageInput {...defaultProps} />);

    const textarea = screen.getByLabelText('Skriv melding');
    await user.type(textarea, 'Line 1');
    await user.keyboard('{Shift>}{Enter}{/Shift}');
    await user.type(textarea, 'Line 2');

    expect(textarea).toHaveValue('Line 1\nLine 2');
  });

  it('sends message with Ctrl+Enter', async () => {
    const user = userEvent.setup();
    const onSend = vi.fn();
    render(<MessageInput {...defaultProps} onSend={onSend} />);

    const textarea = screen.getByLabelText('Skriv melding');
    await user.type(textarea, 'Test message');
    await user.keyboard('{Control>}{Enter}{/Control}');

    expect(onSend).toHaveBeenCalledWith({
      text: 'Test message',
      files: []
    });
  });

  it('handles Escape key for cancel', async () => {
    const user = userEvent.setup();
    const onCancel = vi.fn();
    render(<MessageInput {...defaultProps} onCancel={onCancel} />);

    const textarea = screen.getByLabelText('Skriv melding');
    await user.type(textarea, 'Test');
    await user.keyboard('{Escape}');

    expect(onCancel).toHaveBeenCalled();
  });

  it('disables input when disabled prop is true', () => {
    render(<MessageInput {...defaultProps} disabled={true} />);

    const textarea = screen.getByLabelText('Skriv melding');
    const sendButton = screen.getByText('Send');

    expect(textarea).toBeDisabled();
    expect(sendButton).toBeDisabled();
  });

  it('shows loading state when submitting', async () => {
    const user = userEvent.setup();
    const onSend = vi.fn(() => new Promise(resolve => setTimeout(resolve, 100)));
    render(<MessageInput {...defaultProps} onSend={onSend} />);

    const textarea = screen.getByLabelText('Skriv melding');
    const sendButton = screen.getByText('Send');

    await user.type(textarea, 'Test message');
    await user.click(sendButton);

    expect(sendButton).toHaveAttribute('aria-label', 'Sender melding...');
    expect(textarea).toBeDisabled();
  });

  it('handles file upload', async () => {
    const user = userEvent.setup();
    render(<MessageInput {...defaultProps} />);

    const fileInput = screen.getByLabelText('Last opp fil');
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });

    await user.upload(fileInput, file);

    expect(screen.getByText('test.txt')).toBeInTheDocument();
  });

  it('shows reply context when replying', () => {
    const parentMessage = {
      _id: 'parent' as any,
      senderDisplayName: 'Test User',
      text: 'Original message'
    };

    render(
      <MessageInput 
        {...defaultProps} 
        parentId="parent" 
        parentMessage={parentMessage}
      />
    );

    expect(screen.getByText('Svarer på Test User')).toBeInTheDocument();
    expect(screen.getByText('Original message')).toBeInTheDocument();
  });

  it('has proper focus management', async () => {
    const user = userEvent.setup();
    render(<MessageInput {...defaultProps} />);

    const textarea = screen.getByLabelText('Skriv melding');
    
    // Focus should be manageable
    await user.click(textarea);
    expect(textarea).toHaveFocus();

    // Tab should move to send button
    await user.keyboard('{Tab}');
    expect(screen.getByText('Send')).toHaveFocus();
  });

  it('auto-resizes textarea', async () => {
    const user = userEvent.setup();
    render(<MessageInput {...defaultProps} />);

    const textarea = screen.getByLabelText('Skriv melding');
    
    // Type multiple lines
    await user.type(textarea, 'Line 1\nLine 2\nLine 3\nLine 4');

    // Textarea should have grown (we can't easily test exact height, but we can verify it's still functional)
    expect(textarea).toHaveValue('Line 1\nLine 2\nLine 3\nLine 4');
  });

  it('clears input after successful send', async () => {
    const user = userEvent.setup();
    const onSend = vi.fn().mockResolvedValue(undefined);
    render(<MessageInput {...defaultProps} onSend={onSend} />);

    const textarea = screen.getByLabelText('Skriv melding');
    await user.type(textarea, 'Test message');
    await user.keyboard('{Enter}');

    await waitFor(() => {
      expect(textarea).toHaveValue('');
    });
  });
});
