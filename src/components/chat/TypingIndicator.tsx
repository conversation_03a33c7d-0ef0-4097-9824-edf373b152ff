import React from 'react';
import { TypingIndicator as TypingIndicatorType } from '../../types/chat';

interface TypingIndicatorProps {
  typingUsers: TypingIndicatorType[];
  className?: string;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  typingUsers,
  className = ''
}) => {
  if (typingUsers.length === 0) return null;

  const formatTypingText = (users: TypingIndicatorType[]) => {
    if (users.length === 1) {
      return `${users[0].displayName} skriver...`;
    } else if (users.length === 2) {
      return `${users[0].displayName} og ${users[1].displayName} skriver...`;
    } else {
      return `${users[0].displayName} og ${users.length - 1} andre skriver...`;
    }
  };

  return (
    <div className={`jobblogg-typing-indicator flex items-center space-x-3 px-4 py-3 ${className}`}>
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-jobblogg-text-muted rounded-full animate-bounce"></div>
        <div className="w-2 h-2 bg-jobblogg-text-muted rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
        <div className="w-2 h-2 bg-jobblogg-text-muted rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
      </div>
      <span className="text-sm text-jobblogg-text-muted italic">
        {formatTypingText(typingUsers)}
      </span>
    </div>
  );
};
