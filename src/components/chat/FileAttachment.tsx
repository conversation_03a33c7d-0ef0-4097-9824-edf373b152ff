import React, { useState } from 'react';
import { FileAttachmentProps } from '../../types/chat';
import { ImageLightbox } from './ImageLightbox';

export const FileAttachment: React.FC<FileAttachmentProps> = ({
  file,
  className = ''
}) => {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [lightboxOpen, setLightboxOpen] = useState(false);

  if (!file) return null;

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get file type icon
  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) {
      return (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      );
    }
    
    if (type === 'application/pdf') {
      return (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
    }
    
    if (type.includes('word') || type.includes('document')) {
      return (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
    }
    
    // Default file icon
    return (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
      </svg>
    );
  };

  // Check if it's an image that can be displayed
  const isImage = file.type.startsWith('image/');

  return (
    <div className={`jobblogg-file-attachment ${className}`}>
      {isImage ? (
        <>
          <ImagePreview
            file={file}
            imageLoading={imageLoading}
            imageError={imageError}
            lightboxOpen={lightboxOpen}
            onLoadStart={() => setImageLoading(true)}
            onLoadComplete={() => setImageLoading(false)}
            onError={() => {
              setImageLoading(false);
              setImageError(true);
            }}
            onImageClick={() => setLightboxOpen(true)}
          />
          <ImageLightbox
            file={file}
            isOpen={lightboxOpen}
            onClose={() => setLightboxOpen(false)}
          />
        </>
      ) : (
        // Non-image file
        <FileAttachmentFallback file={file} />
      )}
    </div>
  );
};

// Enhanced image preview component with loading states and better responsive design
interface ImagePreviewProps {
  file: NonNullable<FileAttachmentProps['file']>;
  imageLoading: boolean;
  imageError: boolean;
  lightboxOpen: boolean;
  onLoadStart: () => void;
  onLoadComplete: () => void;
  onError: () => void;
  onImageClick: () => void;
}

const ImagePreview: React.FC<ImagePreviewProps> = ({
  file,
  imageLoading,
  imageError,
  lightboxOpen,
  onLoadStart,
  onLoadComplete,
  onError,
  onImageClick
}) => {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleImageLoad = () => {
    onLoadComplete();
  };

  const handleImageError = () => {
    onError();
  };

  const handleImageClick = () => {
    onImageClick();
  };

  // If image failed to load, show fallback
  if (imageError) {
    return <FileAttachmentFallback file={file} />;
  }

  const imageUrl = file.thumbnailUrl || file.url;

  return (
    <div className={`relative ${lightboxOpen ? 'pointer-events-none' : 'group'}`}>
      {/* Loading skeleton */}
      {imageLoading && (
        <div className="absolute inset-0 bg-jobblogg-neutral-secondary animate-pulse rounded-lg flex items-center justify-center">
          <svg className="w-8 h-8 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
      )}

      {/* Image */}
      <img
        src={imageUrl}
        alt={file.name}
        className={`
          w-full max-w-sm max-h-64 object-cover rounded-lg cursor-pointer shadow-sm
          ${lightboxOpen ? 'opacity-100' : 'transition-all duration-200 hover:opacity-90 hover:scale-[1.02] hover:shadow-md'}
          ${imageLoading ? 'opacity-0' : 'opacity-100'}
        `}
        onClick={handleImageClick}
        onLoad={handleImageLoad}
        onError={handleImageError}
        onLoadStart={onLoadStart}
        loading="lazy"
        style={lightboxOpen ? { pointerEvents: 'none' } : {}}
      />

      {/* Image overlay with file info */}
      <div className={`absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent rounded-lg pointer-events-none ${lightboxOpen ? 'opacity-0' : 'opacity-0 transition-opacity duration-200 group-hover:opacity-100'}`}>
        <div className="absolute bottom-2 left-2 right-2">
          <div className="flex items-center justify-between text-white text-xs">
            <span className="truncate font-medium">{file.name}</span>
            <span className="ml-2 bg-black/30 px-2 py-1 rounded">
              {formatFileSize(file.size)}
            </span>
          </div>
        </div>
      </div>

      {/* Click to expand hint */}
      <div className={`absolute top-2 right-2 ${lightboxOpen ? 'opacity-0' : 'opacity-0 transition-opacity duration-200 group-hover:opacity-100'}`}>
        <div className="bg-black/70 text-white p-1.5 rounded-full">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
        </div>
      </div>
    </div>
  );
};

// Fallback component for non-image files or failed image loads
const FileAttachmentFallback: React.FC<{ file: NonNullable<FileAttachmentProps['file']> }> = ({ file }) => {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string) => {
    if (type === 'application/pdf') {
      return (
        <svg className="w-6 h-6 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
    }

    if (type.includes('word') || type.includes('document')) {
      return (
        <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
    }

    if (type.includes('sheet') || type.includes('excel')) {
      return (
        <svg className="w-6 h-6 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      );
    }

    return (
      <svg className="w-6 h-6 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
      </svg>
    );
  };

  return (
    <a
      href={file.url}
      target="_blank"
      rel="noopener noreferrer"
      className="group flex items-center space-x-3 p-3 bg-jobblogg-card hover:bg-jobblogg-neutral-secondary border border-jobblogg-border rounded-lg transition-all duration-200 max-w-xs hover:shadow-sm"
    >
      <div className="flex-shrink-0">
        {getFileIcon(file.type)}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-jobblogg-text-strong truncate group-hover:text-jobblogg-primary transition-colors">
          {file.name}
        </p>
        <p className="text-xs text-jobblogg-text-muted">
          {formatFileSize(file.size)}
        </p>
      </div>
      <div className="flex-shrink-0 opacity-60 group-hover:opacity-100 transition-opacity">
        <svg className="w-4 h-4 text-jobblogg-text-muted group-hover:text-jobblogg-primary transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
        </svg>
      </div>
    </a>
  );
};
