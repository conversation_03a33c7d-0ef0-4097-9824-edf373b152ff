import React, { useState, useEffect } from 'react';

export interface ConnectionStatusProps {
  className?: string;
}

type ConnectionState = 'online' | 'offline' | 'reconnecting' | 'poor';

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  className = ''
}) => {
  const [connectionState, setConnectionState] = useState<ConnectionState>('online');
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [lastConnected, setLastConnected] = useState<Date>(new Date());

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => {
      setConnectionState('online');
      setReconnectAttempts(0);
      setLastConnected(new Date());
    };

    const handleOffline = () => {
      setConnectionState('offline');
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Set initial state
    if (!navigator.onLine) {
      setConnectionState('offline');
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Simulate connection quality monitoring (in real app, this would use WebSocket events)
  useEffect(() => {
    if (connectionState === 'offline') {
      const reconnectInterval = setInterval(() => {
        if (navigator.onLine) {
          setConnectionState('reconnecting');
          setReconnectAttempts(prev => prev + 1);
          
          // Simulate reconnection attempt
          setTimeout(() => {
            if (navigator.onLine) {
              setConnectionState('online');
              setReconnectAttempts(0);
              setLastConnected(new Date());
            } else {
              setConnectionState('offline');
            }
          }, 2000);
        }
      }, 5000);

      return () => clearInterval(reconnectInterval);
    }
  }, [connectionState]);

  // Don't show anything if connection is good
  if (connectionState === 'online' && reconnectAttempts === 0) {
    return null;
  }

  const getStatusConfig = () => {
    switch (connectionState) {
      case 'offline':
        return {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-12.728 12.728m0-12.728l12.728 12.728" />
            </svg>
          ),
          text: 'Frakoblet',
          bgColor: 'bg-jobblogg-error',
          textColor: 'text-white',
          pulseColor: 'bg-jobblogg-error'
        };
      case 'reconnecting':
        return {
          icon: (
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent"></div>
          ),
          text: `Kobler til igjen... (${reconnectAttempts})`,
          bgColor: 'bg-jobblogg-warning',
          textColor: 'text-jobblogg-text-strong',
          pulseColor: 'bg-jobblogg-warning'
        };
      case 'poor':
        return {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          ),
          text: 'Dårlig tilkobling',
          bgColor: 'bg-jobblogg-warning',
          textColor: 'text-jobblogg-text-strong',
          pulseColor: 'bg-jobblogg-warning'
        };
      default:
        return {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          ),
          text: 'Tilkoblet',
          bgColor: 'bg-jobblogg-success',
          textColor: 'text-white',
          pulseColor: 'bg-jobblogg-success'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`jobblogg-connection-status ${className}`}>
      <div className={`inline-flex items-center space-x-2 px-3 py-2 rounded-lg ${config.bgColor} ${config.textColor} shadow-sm`}>
        {/* Status indicator with pulse animation */}
        <div className="relative">
          {config.icon}
          {connectionState === 'reconnecting' && (
            <div className={`absolute inset-0 rounded-full ${config.pulseColor} animate-ping opacity-20`}></div>
          )}
        </div>
        
        {/* Status text */}
        <span className="text-sm font-medium">
          {config.text}
        </span>
        
        {/* Last connected time for offline state */}
        {connectionState === 'offline' && (
          <span className="text-xs opacity-80">
            Sist tilkoblet: {lastConnected.toLocaleTimeString('nb-NO', {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </span>
        )}
      </div>
    </div>
  );
};
