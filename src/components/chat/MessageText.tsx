import React from 'react';
import { processTextWithLinks, detectLinks } from '../../utils/linkDetection';
import { useLinkPreviews } from '../../hooks/useLinkPreviews';
import { LinkPreview, LinkPreviewSkeleton, CompactLinkPreview } from './LinkPreview';

interface MessageTextProps {
  text: string;
  className?: string;
}

export const MessageText: React.FC<MessageTextProps> = ({ text, className = '' }) => {
  const segments = processTextWithLinks(text);

  return (
    <div className={`jobblogg-message-text ${className}`}>
      {segments.map((segment, index) => {
        if (segment.type === 'link') {
          return (
            <a
              key={index}
              href={segment.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-jobblogg-primary hover:text-jobblogg-primary-dark underline decoration-jobblogg-primary/50 hover:decoration-jobblogg-primary-dark transition-colors break-all"
              onClick={(e) => {
                // Prevent event bubbling to parent message handlers
                e.stopPropagation();
              }}
            >
              {segment.content}
            </a>
          );
        }
        
        return (
          <span key={index} className="whitespace-pre-wrap break-words">
            {segment.content}
          </span>
        );
      })}
    </div>
  );
};

// Component for rendering message text with inline link previews
interface MessageTextWithPreviewsProps {
  text: string;
  showPreviews?: boolean;
  previewStyle?: 'full' | 'compact';
  className?: string;
}

export const MessageTextWithPreviews: React.FC<MessageTextWithPreviewsProps> = ({
  text,
  showPreviews = true,
  previewStyle = 'full',
  className = ''
}) => {
  const segments = processTextWithLinks(text);
  const hasLinks = segments.some(segment => segment.type === 'link');
  const { getPreviewsForText } = useLinkPreviews();

  // Get previews for all links in the text
  const linkPreviews = getPreviewsForText(text);

  return (
    <div className={`jobblogg-message-text-with-previews ${className}`}>
      {/* Render text with clickable links */}
      <div className={showPreviews && hasLinks ? "mb-3" : ""}>
        {segments.map((segment, index) => {
          if (segment.type === 'link') {
            return (
              <a
                key={index}
                href={segment.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-jobblogg-primary hover:text-jobblogg-primary-dark underline decoration-jobblogg-primary/50 hover:decoration-jobblogg-primary-dark transition-colors break-all"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                {segment.content}
              </a>
            );
          }

          return (
            <span key={index} className="whitespace-pre-wrap break-words">
              {segment.content}
            </span>
          );
        })}
      </div>

      {/* Render link previews if enabled and links exist */}
      {showPreviews && hasLinks && (
        <div className="space-y-2">
          {linkPreviews.map((linkPreview, index) => (
            <LinkPreviewContainer
              key={`${linkPreview.url}-${index}`}
              url={linkPreview.url}
              preview={linkPreview.preview}
              style={previewStyle}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Container component that handles link preview loading
interface LinkPreviewContainerProps {
  url: string;
  preview: {
    data: any;
    loading: boolean;
    error: string | null;
  };
  style?: 'full' | 'compact';
}

const LinkPreviewContainer: React.FC<LinkPreviewContainerProps> = ({ url, preview, style = 'full' }) => {
  if (preview.loading) {
    return <LinkPreviewSkeleton />;
  }

  if (preview.error || !preview.data) {
    // Show minimal link for failed previews
    return (
      <div className="text-xs text-jobblogg-text-muted">
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className="text-jobblogg-primary hover:text-jobblogg-primary-dark underline"
        >
          {url}
        </a>
      </div>
    );
  }

  const PreviewComponent = style === 'compact' ? CompactLinkPreview : LinkPreview;
  return <PreviewComponent data={preview.data} />;
};

// Simple component for rendering just clickable links without previews
export const ClickableText: React.FC<MessageTextProps> = ({ text, className = '' }) => {
  const segments = processTextWithLinks(text);

  return (
    <span className={className}>
      {segments.map((segment, index) => {
        if (segment.type === 'link') {
          return (
            <a
              key={index}
              href={segment.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-jobblogg-primary hover:text-jobblogg-primary-dark underline decoration-jobblogg-primary/50 hover:decoration-jobblogg-primary-dark transition-colors"
              onClick={(e) => e.stopPropagation()}
            >
              {segment.content}
            </a>
          );
        }
        
        return segment.content;
      })}
    </span>
  );
};
