import React, { useEffect, useState } from 'react';

export interface ScreenReaderAnnouncerProps {
  message?: string;
  priority?: 'polite' | 'assertive';
  clearAfter?: number; // Clear message after X milliseconds
}

/**
 * Component for making announcements to screen readers
 * Uses aria-live regions to announce important updates
 */
export const ScreenReaderAnnouncer: React.FC<ScreenReaderAnnouncerProps> = ({
  message,
  priority = 'polite',
  clearAfter = 3000
}) => {
  const [currentMessage, setCurrentMessage] = useState('');

  useEffect(() => {
    if (message) {
      setCurrentMessage(message);
      
      if (clearAfter > 0) {
        const timer = setTimeout(() => {
          setCurrentMessage('');
        }, clearAfter);
        
        return () => clearTimeout(timer);
      }
    }
  }, [message, clearAfter]);

  return (
    <div
      aria-live={priority}
      aria-atomic="true"
      className="sr-only"
      role="status"
    >
      {currentMessage}
    </div>
  );
};

/**
 * Hook for managing screen reader announcements
 */
export function useScreenReaderAnnouncements() {
  const [announcement, setAnnouncement] = useState<{
    message: string;
    priority: 'polite' | 'assertive';
    timestamp: number;
  } | null>(null);

  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    setAnnouncement({
      message,
      priority,
      timestamp: Date.now()
    });
  };

  const announcePolite = (message: string) => announce(message, 'polite');
  const announceAssertive = (message: string) => announce(message, 'assertive');

  return {
    announcement,
    announce,
    announcePolite,
    announceAssertive
  };
}

/**
 * Component that provides context about the current chat state for screen readers
 */
export const ChatScreenReaderContext: React.FC<{
  totalMessages: number;
  unreadCount: number;
  isTyping: boolean;
  typingUsers: string[];
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
}> = ({
  totalMessages,
  unreadCount,
  isTyping,
  typingUsers,
  connectionStatus
}) => {
  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Tilkoblet';
      case 'connecting':
        return 'Kobler til...';
      case 'disconnected':
        return 'Frakoblet';
      default:
        return '';
    }
  };

  const getTypingText = () => {
    if (!isTyping || typingUsers.length === 0) return '';
    
    if (typingUsers.length === 1) {
      return `${typingUsers[0]} skriver...`;
    } else if (typingUsers.length === 2) {
      return `${typingUsers[0]} og ${typingUsers[1]} skriver...`;
    } else {
      return `${typingUsers.length} personer skriver...`;
    }
  };

  const contextText = [
    `${totalMessages} meldinger totalt`,
    unreadCount > 0 ? `${unreadCount} uleste meldinger` : '',
    getConnectionStatusText(),
    getTypingText()
  ].filter(Boolean).join(', ');

  return (
    <div className="sr-only" aria-live="polite" role="status">
      Chat-kontekst: {contextText}
    </div>
  );
};

/**
 * Component for describing message thread structure to screen readers
 */
export const MessageThreadDescription: React.FC<{
  messageCount: number;
  replyCount: number;
  level: number;
}> = ({ messageCount, replyCount, level }) => {
  const getThreadDescription = () => {
    if (level === 0) {
      return replyCount > 0 
        ? `Hovedmelding med ${replyCount} svar`
        : 'Hovedmelding';
    } else {
      return `Svar på melding, nivå ${level}`;
    }
  };

  return (
    <span className="sr-only">
      {getThreadDescription()}
    </span>
  );
};

/**
 * Component for describing file attachments to screen readers
 */
export const FileAttachmentDescription: React.FC<{
  fileName: string;
  fileSize: number;
  fileType: string;
}> = ({ fileName, fileSize, fileType }) => {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileTypeDescription = (type: string) => {
    if (type.startsWith('image/')) return 'Bilde';
    if (type.startsWith('video/')) return 'Video';
    if (type.startsWith('audio/')) return 'Lyd';
    if (type.includes('pdf')) return 'PDF-dokument';
    if (type.includes('word')) return 'Word-dokument';
    return 'Fil';
  };

  return (
    <span className="sr-only">
      Vedlegg: {getFileTypeDescription(fileType)}, {fileName}, {formatFileSize(fileSize)}
    </span>
  );
};

/**
 * Component for describing emoji reactions to screen readers
 */
export const ReactionDescription: React.FC<{
  emoji: string;
  count: number;
  userHasReacted: boolean;
}> = ({ emoji, count, userHasReacted }) => {
  const getEmojiDescription = (emoji: string) => {
    const emojiDescriptions: Record<string, string> = {
      '👍': 'tommel opp',
      '👎': 'tommel ned',
      '❤️': 'hjerte',
      '😂': 'ler',
      '😮': 'overrasket',
      '😢': 'trist',
      '😡': 'sint'
    };
    return emojiDescriptions[emoji] || emoji;
  };

  const description = userHasReacted
    ? `Du og ${count - 1} andre reagerte med ${getEmojiDescription(emoji)}`
    : `${count} personer reagerte med ${getEmojiDescription(emoji)}`;

  return (
    <span className="sr-only">
      {description}
    </span>
  );
};
