import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import type { Id } from '../../../convex/_generated/dataModel';
import { ChatContainerProps, MessageFormData, ChatError, OptimisticMessage, MessageWithDisplayInfo } from '../../types/chat';
import { MessageInput } from './MessageInput';
import { TextMuted, TextMedium, TextStrong } from '../ui';

// Embedded chat props with additional configuration
export interface EmbeddedChatContainerProps extends ChatContainerProps {
  maxHeight?: string;
  showHeader?: boolean;
  headerText?: string;
  compact?: boolean;
  showLikeButton?: boolean;
  onLike?: () => void;
  likeCount?: number;
  hasUserLiked?: boolean;
}

export const EmbeddedChatContainer: React.FC<EmbeddedChatContainerProps> = ({
  logId,
  userId,
  userRole,
  className = '',
  maxHeight = '400px',
  showHeader = false, // Default to false for new design
  headerText,
  compact = true,
  showLikeButton = true,
  onLike,
  likeCount = 0,
  hasUserLiked = false
}) => {
  const [error, setError] = useState<ChatError | null>(null);
  const [optimisticMessages, setOptimisticMessages] = useState<OptimisticMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [showCommentInput, setShowCommentInput] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Convex hooks - always fetch for new always-visible design
  const messagesData = useQuery(
    api.messages.getMessagesWithDisplayNames,
    {
      logId,
      userId,
      userRole,
      limit: 50, // Increased limit for always-visible view
      cursor: undefined
    }
  );

  const typingIndicatorsData = useQuery(
    api.messages.getTypingIndicators,
    {
      logId,
      userId
    }
  );

  // Smooth typing indicators with debouncing and minimum display duration
  const [smoothTypingIndicators, setSmoothTypingIndicators] = React.useState<any[]>([]);
  const [isShowingTyping, setIsShowingTyping] = React.useState(false);
  const smoothTypingTimeoutRef = React.useRef<NodeJS.Timeout>();
  const hideTimeoutRef = React.useRef<NodeJS.Timeout>();
  const lastShowTimeRef = React.useRef<number>(0);

  React.useEffect(() => {
    const hasTypingIndicators = typingIndicatorsData && typingIndicatorsData.length > 0;

    if (hasTypingIndicators) {
      // Clear any pending hide timeout
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
        hideTimeoutRef.current = undefined;
      }

      // Update the indicators data immediately
      setSmoothTypingIndicators(typingIndicatorsData);

      // Show typing indicators if not already showing
      if (!isShowingTyping) {
        setIsShowingTyping(true);
        lastShowTimeRef.current = Date.now();
      }

      // Reset the typing timeout (debounce hiding)
      if (smoothTypingTimeoutRef.current) {
        clearTimeout(smoothTypingTimeoutRef.current);
      }

      smoothTypingTimeoutRef.current = setTimeout(() => {
        // Calculate minimum display time (1 second minimum)
        const elapsed = Date.now() - lastShowTimeRef.current;
        const minDisplayTime = 1000;
        const remainingTime = Math.max(0, minDisplayTime - elapsed);

        hideTimeoutRef.current = setTimeout(() => {
          setIsShowingTyping(false);
        }, remainingTime);
      }, 500); // Hide after 500ms of no updates

    } else if (isShowingTyping) {
      // Clear any pending typing timeout
      if (smoothTypingTimeoutRef.current) {
        clearTimeout(smoothTypingTimeoutRef.current);
        smoothTypingTimeoutRef.current = undefined;
      }

      // Calculate minimum display time
      const elapsed = Date.now() - lastShowTimeRef.current;
      const minDisplayTime = 1000;
      const remainingTime = Math.max(0, minDisplayTime - elapsed);

      hideTimeoutRef.current = setTimeout(() => {
        setIsShowingTyping(false);
      }, remainingTime);
    }

    // Cleanup function
    return () => {
      if (smoothTypingTimeoutRef.current) {
        clearTimeout(smoothTypingTimeoutRef.current);
      }
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }
    };
  }, [typingIndicatorsData, isShowingTyping]);

  const sendMessage = useMutation(api.messages.sendMessage);
  const startTypingMutation = useMutation(api.messages.startTyping);
  const stopTypingMutation = useMutation(api.messages.stopTyping);
  const markAsRead = useMutation(api.messages.markAsRead);

  // Handle sending messages with optimistic updates
  const handleSendMessage = useCallback(async (data: MessageFormData) => {
    try {
      setError(null);

      // Create optimistic message for immediate UI feedback
      const optimisticId = `optimistic-${Date.now()}-${Math.random()}`;
      const optimisticMessage: OptimisticMessage = {
        _id: optimisticId,
        _creationTime: Date.now(),
        logId,
        parentId: data.parentId,
        senderId: userId,
        senderRole: userRole,
        text: data.text,
        file: data.file ? {
          url: data.file.url || '',
          name: data.file.name,
          size: data.file.size,
          type: data.file.type
        } : undefined,
        createdAt: Date.now(),
        isOptimistic: true,
        isSending: true
      };

      setOptimisticMessages(prev => [...prev, optimisticMessage]);

      // Send the actual message
      await sendMessage({
        logId,
        parentId: data.parentId,
        senderId: userId,
        senderRole: userRole,
        text: data.text,
        file: data.file
      });

      // Remove optimistic message after successful send
      setOptimisticMessages(prev => prev.filter(msg => msg._id !== optimisticId));

    } catch (err) {
      // Mark optimistic message as failed
      setOptimisticMessages(prev =>
        prev.map(msg =>
          msg._id === optimisticId
            ? { ...msg, isSending: false, sendError: err instanceof Error ? err.message : 'Kunne ikke sende melding' }
            : msg
        )
      );
      setError({
        type: 'network',
        message: err instanceof Error ? err.message : 'Kunne ikke sende melding',
        timestamp: Date.now()
      });
    }
  }, [logId, userId, userRole, sendMessage]);

  // Typing indicator handlers
  const handleTypingStart = useCallback(async () => {
    console.log('🔵 EmbeddedChat - handleTypingStart called', { isTyping, logId, userId, userRole });
    if (!isTyping) {
      setIsTyping(true);
      try {
        console.log('🔵 EmbeddedChat - Calling startTypingMutation');
        await startTypingMutation({
          logId,
          userId,
          userRole
        });
        console.log('🔵 EmbeddedChat - startTypingMutation success');
      } catch (error) {
        console.error('Failed to start typing indicator:', error);
      }
    }

    // Reset typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      handleTypingStop();
    }, 3000);
  }, [isTyping, startTypingMutation, logId, userId, userRole]);

  const handleTypingStop = useCallback(async () => {
    console.log('🔴 EmbeddedChat - handleTypingStop called', { logId, userId });
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    setIsTyping(false);

    try {
      console.log('🔴 EmbeddedChat - Calling stopTypingMutation');
      await stopTypingMutation({
        logId,
        userId
      });
      console.log('🔴 EmbeddedChat - stopTypingMutation success');
    } catch (error) {
      console.error('Failed to stop typing indicator:', error);
    }
  }, [stopTypingMutation, logId, userId]);

  // Merge optimistic messages with real messages
  const mergedMessages = useMemo(() => {
    if (!messagesData?.messages) return [];

    const realMessages = messagesData.messages;



    const optimisticAsDisplay: MessageWithDisplayInfo[] = optimisticMessages.map(opt => ({
      ...opt,
      senderDisplayName: userRole === 'contractor' ? 'Leverandør' : 'Kunde',
      isOwnMessage: true,
      replies: []
    }));

    return [...realMessages, ...optimisticAsDisplay];
  }, [messagesData?.messages, optimisticMessages, userRole, userId]);

  // Mark messages as read when messages are loaded (always visible now)
  useEffect(() => {
    if (messagesData?.messages.length) {
      markAsRead({ logId, userId, userRole }).catch(console.error);
    }
  }, [markAsRead, logId, userId, userRole, messagesData?.messages.length]);

  // Auto-scroll to bottom when new messages arrive (always visible now)
  useEffect(() => {
    if (messagesEndRef.current && showCommentInput) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [mergedMessages, showCommentInput]);

  // Toggle comment input visibility
  const toggleCommentInput = useCallback(() => {
    setShowCommentInput(prev => !prev);
  }, []);

  // Get message count for display
  const messageCount = mergedMessages.length;
  const hasMessages = messageCount > 0;

  // Priority emoji reactions for the summary bar (as specified in requirements)
  const priorityEmojis = ['👍', '❤️', '😊', '😢', '😮', '😡'];

  // Aggregate reactions from all messages in the conversation
  const aggregatedReactions = useMemo(() => {
    const reactionMap = new Map<string, { emoji: string; count: number; userNames: string[] }>();

    mergedMessages.forEach(message => {
      if (message.reactions) {
        message.reactions.forEach(reaction => {
          const existing = reactionMap.get(reaction.emoji);
          if (existing) {
            existing.count += reaction.count;
            // Add unique user names (simplified - in real implementation, you'd get actual names)
            const newUserNames = reaction.userIds.map((_, index) => `Bruker ${index + 1}`);
            existing.userNames = [...new Set([...existing.userNames, ...newUserNames])];
          } else {
            reactionMap.set(reaction.emoji, {
              emoji: reaction.emoji,
              count: reaction.count,
              userNames: reaction.userIds.map((_, index) => `Bruker ${index + 1}`)
            });
          }
        });
      }
    });

    // Sort by priority emojis first, then by count
    return Array.from(reactionMap.values()).sort((a, b) => {
      const aPriority = priorityEmojis.indexOf(a.emoji);
      const bPriority = priorityEmojis.indexOf(b.emoji);

      if (aPriority !== -1 && bPriority !== -1) {
        return aPriority - bPriority; // Both are priority, sort by priority order
      } else if (aPriority !== -1) {
        return -1; // a is priority, b is not
      } else if (bPriority !== -1) {
        return 1; // b is priority, a is not
      } else {
        return b.count - a.count; // Neither is priority, sort by count
      }
    });
  }, [mergedMessages]);

  // Format reaction display text
  const formatReactionText = (reaction: { emoji: string; count: number; userNames: string[] }) => {
    if (reaction.count === 1) {
      return `${reaction.emoji} 1 (${reaction.userNames[0]})`;
    } else if (reaction.count <= 3) {
      return `${reaction.emoji} ${reaction.count} (${reaction.userNames.join(', ')})`;
    } else {
      const displayNames = reaction.userNames.slice(0, 2).join(', ');
      const remaining = reaction.count - 2;
      return `${reaction.emoji} ${reaction.count} (${displayNames}, +${remaining} ${remaining === 1 ? 'annen' : 'andre'})`;
    }
  };

  return (
    <div className={`jobblogg-comment-section bg-jobblogg-card-bg rounded-lg ${className}`}>
      {/* Reaction Summary Bar - Mobile-responsive */}
      {(aggregatedReactions.length > 0 || hasMessages) && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 px-3 sm:px-4 py-3 border-b border-jobblogg-border bg-jobblogg-card-bg">
          <div className="flex items-center gap-2 sm:gap-3 flex-wrap">
            {aggregatedReactions.slice(0, 4).map((reaction, index) => (
              <span key={index} className="text-xs sm:text-sm text-jobblogg-text-muted">
                {formatReactionText(reaction)}
              </span>
            ))}
            {aggregatedReactions.length > 4 && (
              <span className="text-xs sm:text-sm text-jobblogg-text-muted">
                +{aggregatedReactions.length - 4} flere
              </span>
            )}
          </div>
          <div className="text-xs sm:text-sm text-jobblogg-text-muted font-medium">
            {messageCount} {messageCount === 1 ? 'kommentar' : 'kommentarer'}
          </div>
        </div>
      )}

      {/* Action Buttons Section - Mobile-first responsive design */}
      <div className="flex items-center justify-center gap-4 sm:gap-8 px-3 sm:px-4 py-3 sm:py-4 border-b border-jobblogg-border bg-white">
        {/* Like Button - Integrated with existing like system */}
        {showLikeButton && (
          <button
            onClick={onLike}
            className={`flex items-center gap-2 px-4 sm:px-6 py-3 sm:py-2.5 rounded-lg font-medium transition-all duration-200 min-w-[100px] sm:min-w-[120px] justify-center min-h-[44px] ${
              hasUserLiked
                ? 'bg-jobblogg-error text-white shadow-md hover:bg-jobblogg-error/90 hover:shadow-lg active:scale-95'
                : 'bg-jobblogg-neutral-secondary text-jobblogg-text-medium hover:bg-jobblogg-neutral hover:text-jobblogg-text-strong border border-jobblogg-border hover:border-jobblogg-error/30 active:scale-95'
            }`}
            aria-label={hasUserLiked ? 'Fjern like' : 'Lik denne oppføringen'}
          >
            <svg
              className={`w-4 h-4 transition-transform duration-200 ${hasUserLiked ? 'scale-110' : ''}`}
              fill={hasUserLiked ? 'currentColor' : 'none'}
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={hasUserLiked ? 0 : 2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
            <span className="text-sm">Liker</span>
            {likeCount > 0 && (
              <span className="text-xs sm:text-sm bg-white/20 px-1.5 py-0.5 rounded-full min-w-[18px] sm:min-w-[20px] text-center">
                {likeCount}
              </span>
            )}
          </button>
        )}

        {/* Comment Button */}
        <button
          onClick={toggleCommentInput}
          className={`flex items-center gap-2 px-4 sm:px-6 py-3 sm:py-2.5 rounded-lg font-medium transition-all duration-200 min-w-[100px] sm:min-w-[120px] justify-center min-h-[44px] ${
            showCommentInput
              ? 'bg-jobblogg-primary text-white shadow-md hover:bg-jobblogg-primary/90 active:scale-95'
              : 'bg-jobblogg-neutral-secondary text-jobblogg-text-medium hover:bg-jobblogg-neutral hover:text-jobblogg-text-strong border border-jobblogg-border hover:border-jobblogg-primary/30 active:scale-95'
          }`}
          aria-label={showCommentInput ? 'Skjul kommentarfelt' : 'Vis kommentarfelt'}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          <span className="text-sm">Kommenter</span>
        </button>
      </div>

      {/* Enhanced Message Thread Display - Always visible */}
      <div className="bg-white">
        {/* Messages Area - Always visible structured comments with mobile-first spacing */}
        <div
          className="overflow-y-auto px-3 sm:px-4 py-3 sm:py-4 space-y-3 sm:space-y-4"
          style={{ maxHeight }}
        >
          {mergedMessages.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-jobblogg-neutral-secondary flex items-center justify-center">
                <svg className="w-8 h-8 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <div className="text-jobblogg-text-muted text-sm font-medium mb-1">
                Ingen kommentarer ennå
              </div>
              <TextMuted className="text-xs">
                {userRole === 'contractor'
                  ? 'Vær den første til å kommentere!'
                  : 'Start en samtale med kontraktøren!'}
              </TextMuted>
            </div>
          ) : (
            mergedMessages.map((message) => (
              <div key={message._id} className="flex gap-2 sm:gap-3">
                {/* Profile Avatar - Responsive sizing */}
                <div className="flex-shrink-0">
                  <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center text-xs sm:text-sm font-semibold shadow-sm ${
                    message.senderRole === 'contractor'
                      ? 'bg-jobblogg-primary text-white'
                      : 'bg-jobblogg-success text-white'
                  }`}>
                    {message.senderRole === 'contractor' ? 'L' : 'K'}
                  </div>
                </div>

                {/* Message Content */}
                <div className="flex-1 min-w-0">
                  {/* User Identification - Mobile-responsive layout */}
                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mb-2">
                    <div className="flex items-center gap-2">
                      <TextStrong className="text-sm font-semibold">
                        {message.senderDisplayName}
                      </TextStrong>
                      <span className={`text-xs px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full font-medium ${
                        message.senderRole === 'contractor'
                          ? 'bg-jobblogg-primary/10 text-jobblogg-primary border border-jobblogg-primary/20'
                          : 'bg-jobblogg-success/10 text-jobblogg-success border border-jobblogg-success/20'
                      }`}>
                        {message.senderRole === 'contractor' ? 'Leverandør' : 'Kunde'}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <TextMuted className="text-xs">
                        {new Date(message.createdAt).toLocaleString('nb-NO', {
                          day: '2-digit',
                          month: '2-digit',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </TextMuted>
                      {message.isOptimistic && message.isSending && (
                        <span className="text-xs text-jobblogg-warning">• Sender...</span>
                      )}
                      {message.isOptimistic && message.sendError && (
                        <span className="text-xs text-jobblogg-error">• Feil</span>
                      )}
                    </div>
                  </div>

                  {/* Message Content Card - Mobile-responsive padding */}
                  <div className="bg-jobblogg-card-bg rounded-lg p-3 sm:p-4 border border-jobblogg-border shadow-sm">
                    {/* Message Text */}
                    {message.text && (
                      <div className="text-sm text-jobblogg-text-strong leading-relaxed whitespace-pre-wrap break-words mb-3">
                        {message.text}
                      </div>
                    )}

                    {/* File Attachment */}
                    {message.file && (
                      <div className="mb-3">
                        <a
                          href={message.file.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center gap-2 text-sm text-jobblogg-primary hover:text-jobblogg-primary-dark underline min-h-[44px] py-2"
                        >
                          <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                          </svg>
                          <span className="break-all">{message.file.name}</span>
                        </a>
                      </div>
                    )}

                    {/* Message Reactions */}
                    {message.reactions && message.reactions.length > 0 && (
                      <div className="pt-3 border-t border-jobblogg-border">
                        <EmojiReactions
                          reactions={message.reactions}
                          messageId={message._id}
                          userId={userId}
                          onReaction={handleReaction}
                          className="text-xs"
                        />
                      </div>
                    )}
                  </div>

                  {/* Message Actions */}
                  {message.isOwnMessage && (
                    <div className="flex items-center gap-2 mt-2">
                      <button className="text-jobblogg-text-muted hover:text-jobblogg-text-strong text-xs">
                        ⋯
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}

          {/* Smooth Typing Indicators - Mobile-responsive */}
          <div
            className={`transition-all duration-300 ease-in-out overflow-hidden ${
              isShowingTyping ? 'max-h-16 opacity-100' : 'max-h-0 opacity-0'
            }`}
          >
            <div className="flex gap-2 sm:gap-3 px-3 sm:px-4 py-2 sm:py-3 border-t border-jobblogg-border bg-jobblogg-neutral-secondary/30">
              <div className="flex-shrink-0">
                <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-jobblogg-text-muted/20 flex items-center justify-center">
                  <div className="flex space-x-0.5 sm:space-x-1">
                    <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-jobblogg-text-muted rounded-full animate-bounce"></div>
                    <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-jobblogg-text-muted rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-jobblogg-text-muted rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
              <div className="flex-1">
                <div className="text-xs sm:text-sm text-jobblogg-text-muted italic transition-opacity duration-200">
                  {smoothTypingIndicators.length === 1
                    ? `${smoothTypingIndicators[0].displayName} skriver...`
                    : `${smoothTypingIndicators.length} personer skriver...`
                  }
                </div>
              </div>
            </div>
          </div>

          <div ref={messagesEndRef} />
        </div>

        {/* Error Display */}
        {error && (
          <div className="px-3 sm:px-4 py-3 bg-jobblogg-error-soft border-l-4 border-jobblogg-error">
            <TextMuted className="text-sm text-jobblogg-error">
              {error.message}
            </TextMuted>
          </div>
        )}

        {/* Message Input - Only visible when comment button is clicked */}
        {showCommentInput && (
          <div className="border-t border-jobblogg-border bg-white">
            <MessageInput
              logId={logId}
              userId={userId}
              userRole={userRole}
              onSend={handleSendMessage}
              placeholder="Skriv en kommentar..."
              className="border-0 rounded-none"
              onTypingStart={handleTypingStart}
              onTypingStop={handleTypingStop}
            />
          </div>
        )}
      </div>
    </div>
  );
};
