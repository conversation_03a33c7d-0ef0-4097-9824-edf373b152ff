import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import type { Id } from '../../../convex/_generated/dataModel';
import { ChatContainerProps, MessageFormData, ChatError, OptimisticMessage, MessageWithDisplayInfo } from '../../types/chat';
import { MessageInput } from './MessageInput';
import { TextMuted, TextMedium } from '../ui';

// Embedded chat props with additional configuration
export interface EmbeddedChatContainerProps extends ChatContainerProps {
  maxHeight?: string;
  showHeader?: boolean;
  headerText?: string;
  compact?: boolean;
}

export const EmbeddedChatContainer: React.FC<EmbeddedChatContainerProps> = ({
  logId,
  userId,
  userRole,
  className = '',
  maxHeight = '300px',
  showHeader = true,
  headerText,
  compact = true
}) => {
  const [error, setError] = useState<ChatError | null>(null);
  const [optimisticMessages, setOptimisticMessages] = useState<OptimisticMessage[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Convex hooks - only fetch when expanded to optimize performance
  const messagesData = useQuery(
    api.messages.getMessagesWithDisplayNames,
    isExpanded ? {
      logId,
      userId,
      userRole,
      limit: 20, // Smaller limit for embedded view
      cursor: undefined
    } : "skip"
  );

  const sendMessage = useMutation(api.messages.sendMessage);

  // Handle sending messages with optimistic updates
  const handleSendMessage = useCallback(async (data: MessageFormData) => {
    try {
      setError(null);

      // Create optimistic message for immediate UI feedback
      const optimisticId = `optimistic-${Date.now()}-${Math.random()}`;
      const optimisticMessage: OptimisticMessage = {
        _id: optimisticId,
        _creationTime: Date.now(),
        logId,
        parentId: data.parentId,
        senderId: userId,
        senderRole: userRole,
        text: data.text,
        file: data.file ? {
          url: data.file.url || '',
          name: data.file.name,
          size: data.file.size,
          type: data.file.type
        } : undefined,
        createdAt: Date.now(),
        isOptimistic: true,
        isSending: true
      };

      setOptimisticMessages(prev => [...prev, optimisticMessage]);

      // Send the actual message
      await sendMessage({
        logId,
        parentId: data.parentId,
        senderId: userId,
        senderRole: userRole,
        text: data.text,
        file: data.file
      });

      // Remove optimistic message after successful send
      setOptimisticMessages(prev => prev.filter(msg => msg._id !== optimisticId));

    } catch (err) {
      // Mark optimistic message as failed
      setOptimisticMessages(prev =>
        prev.map(msg =>
          msg._id === optimisticId
            ? { ...msg, isSending: false, sendError: err instanceof Error ? err.message : 'Kunne ikke sende melding' }
            : msg
        )
      );
      setError({
        type: 'network',
        message: err instanceof Error ? err.message : 'Kunne ikke sende melding',
        timestamp: Date.now()
      });
    }
  }, [logId, userId, userRole, sendMessage]);

  // Merge optimistic messages with real messages
  const mergedMessages = useMemo(() => {
    if (!messagesData?.messages) return [];

    const realMessages = messagesData.messages;
    const optimisticAsDisplay: MessageWithDisplayInfo[] = optimisticMessages.map(opt => ({
      ...opt,
      senderDisplayName: userRole === 'contractor' ? 'Leverandør' : 'Kunde',
      isOwnMessage: true,
      replies: []
    }));

    return [...realMessages, ...optimisticAsDisplay];
  }, [messagesData?.messages, optimisticMessages, userRole]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (isExpanded && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [mergedMessages, isExpanded]);

  // Toggle expanded state
  const toggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);

  // Get message count for display
  const messageCount = mergedMessages.length;
  const hasMessages = messageCount > 0;

  return (
    <div className={`jobblogg-embedded-chat border-t border-jobblogg-border ${className}`}>
      {/* Chat Header - Always visible */}
      {showHeader && (
        <div 
          className="flex items-center justify-between p-3 bg-jobblogg-neutral-secondary cursor-pointer hover:bg-jobblogg-accent-soft transition-colors"
          onClick={toggleExpanded}
        >
          <div className="flex items-center gap-2">
            <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <TextMedium className="text-sm">
              {headerText || (userRole === 'contractor' ? 'Diskuter med kunde' : 'Diskuter med kontraktør')}
            </TextMedium>
            {hasMessages && (
              <span className="bg-jobblogg-primary text-white text-xs px-2 py-1 rounded-full">
                {messageCount}
              </span>
            )}
          </div>
          <svg 
            className={`w-4 h-4 text-jobblogg-text-muted transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      )}

      {/* Chat Content - Only visible when expanded */}
      {isExpanded && (
        <div className="bg-white">
          {/* Messages Area */}
          <div 
            className="overflow-y-auto px-3 py-2 space-y-2"
            style={{ maxHeight }}
          >
            {mergedMessages.length === 0 ? (
              <div className="text-center py-4">
                <TextMuted className="text-sm">
                  {userRole === 'contractor' 
                    ? 'Start en samtale med kunden' 
                    : 'Start en samtale med kontraktøren'}
                </TextMuted>
              </div>
            ) : (
              mergedMessages.map((message) => (
                <div
                  key={message._id}
                  className={`flex ${message.isOwnMessage ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] px-3 py-2 rounded-lg ${
                      message.isOwnMessage
                        ? 'bg-jobblogg-primary text-white'
                        : 'bg-jobblogg-neutral-secondary text-jobblogg-text-strong'
                    }`}
                  >
                    {message.text && (
                      <div className="text-sm whitespace-pre-wrap break-words">
                        {message.text}
                      </div>
                    )}
                    {message.file && (
                      <div className="mt-1">
                        <a
                          href={message.file.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs underline opacity-90 hover:opacity-100"
                        >
                          📎 {message.file.name}
                        </a>
                      </div>
                    )}
                    <div className={`text-xs mt-1 opacity-70 ${message.isOwnMessage ? 'text-white' : 'text-jobblogg-text-muted'}`}>
                      {new Date(message.createdAt).toLocaleTimeString('nb-NO', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                      {message.isOptimistic && message.isSending && ' • Sender...'}
                      {message.isOptimistic && message.sendError && ' • Feil'}
                    </div>
                  </div>
                </div>
              ))
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Error Display */}
          {error && (
            <div className="px-3 py-2 bg-jobblogg-error-soft border-l-4 border-jobblogg-error">
              <TextMuted className="text-sm text-jobblogg-error">
                {error.message}
              </TextMuted>
            </div>
          )}

          {/* Message Input */}
          <div className="border-t border-jobblogg-border">
            <MessageInput
              logId={logId}
              userId={userId}
              userRole={userRole}
              onSend={handleSendMessage}
              placeholder="Skriv en melding..."
              className="border-0 rounded-none"
            />
          </div>
        </div>
      )}
    </div>
  );
};
