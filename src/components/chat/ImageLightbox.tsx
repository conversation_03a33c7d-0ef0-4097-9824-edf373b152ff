import React, { useEffect, useCallback } from 'react';
import { FileAttachmentProps } from '../../types/chat';

interface ImageLightboxProps {
  file: NonNullable<FileAttachmentProps['file']>;
  isOpen: boolean;
  onClose: () => void;
}

export const ImageLightbox: React.FC<ImageLightboxProps> = ({
  file,
  isOpen,
  onClose
}) => {
  // Memoize event handlers to prevent unnecessary re-renders
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      onClose();
    }
  }, [onClose]);

  const handleBackdropClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  }, [onClose]);

  const handleDownload = useCallback(() => {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [file.url, file.name]);

  // Handle keyboard events and body scroll
  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, handleKeyDown]);

  // Memoize file size calculation
  const fileSize = React.useMemo(() => {
    if (file.size === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(file.size) / Math.log(k));
    return parseFloat((file.size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, [file.size]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 bg-black/80"
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="lightbox-title"
    >
      {/* Image Container - Full viewport with proper aspect ratio handling */}
      <div className="absolute inset-0 flex items-center justify-center p-4">
        <div className="relative max-w-[90vw] max-h-[90vh] flex items-center justify-center">
          <img
            src={file.url}
            alt={file.name}
            className="max-w-full max-h-full object-contain shadow-2xl"
            style={{
              maxWidth: '90vw',
              maxHeight: '90vh',
            }}
          />
        </div>
      </div>

      {/* Floating Header - Positioned absolutely to not interfere with image sizing */}
      <div className="absolute top-4 left-4 right-4 z-10">
        <div className="flex items-center justify-between p-3 bg-black/70 rounded-lg">
          <div className="flex-1 min-w-0 mr-4">
            <h2
              id="lightbox-title"
              className="text-white font-medium truncate text-sm md:text-base"
            >
              {file.name}
            </h2>
            <p className="text-white/70 text-xs md:text-sm">
              {fileSize}
            </p>
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-1">
            {/* Download button */}
            <button
              onClick={handleDownload}
              className="p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-full transition-colors duration-200 min-h-[44px] min-w-[44px] flex items-center justify-center will-change-auto"
              title="Last ned bilde"
              aria-label="Last ned bilde"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </button>

            {/* Close button */}
            <button
              onClick={onClose}
              className="p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-full transition-colors duration-200 min-h-[44px] min-w-[44px] flex items-center justify-center will-change-auto"
              title="Lukk"
              aria-label="Lukk bildegalleri"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile-friendly close hint */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 md:hidden">
        <p className="text-white/60 text-sm bg-black/70 px-3 py-1 rounded-full">
          Trykk utenfor bildet for å lukke
        </p>
      </div>
    </div>
  );
};
