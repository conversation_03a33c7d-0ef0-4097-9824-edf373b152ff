import React, { useState } from 'react';
import { MessageItemProps } from '../../types/chat';
import { EmojiReactions } from './EmojiReactions';
import { FileAttachment } from './FileAttachment';
import { DeliveryStatus } from './DeliveryStatus';
import { MessageThreadDescription } from './ScreenReaderAnnouncer';

export const MessageItem: React.FC<MessageItemProps> = ({
  message,
  userId,
  level = 0,
  onReply,
  onReaction,
  onEdit,
  onDelete,
  onRetry,
  className = ''
}) => {
  const [showActions, setShowActions] = useState(false);

  const isOwnMessage = message.isOwnMessage;
  const isReply = level > 0;

  // Check if this is an optimistic message
  const isOptimistic = 'isOptimistic' in message && message.isOptimistic;
  const isSending = 'isSending' in message && message.isSending;
  const sendError = 'sendError' in message ? message.sendError : undefined;

  // Format timestamp
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('nb-NO', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString('nb-NO', {
        weekday: 'short',
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      return date.toLocaleDateString('nb-NO', {
        day: '2-digit',
        month: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  return (
    <div
      className={`jobblogg-message-item group ${className}`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
      role="article"
      aria-label={`Melding fra ${message.senderDisplayName}`}
    >
      {/* Screen reader thread description */}
      <MessageThreadDescription
        messageCount={1}
        replyCount={message.replies?.length || 0}
        level={level}
      />
      <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
        <div
          className={`max-w-[70%] ${
            isOwnMessage
              ? 'bg-jobblogg-primary text-white'
              : 'bg-jobblogg-neutral-secondary text-jobblogg-text-strong'
          } rounded-lg px-4 py-3 shadow-sm`}
        >
          {/* Message header */}
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              {/* Role indicator badge */}
              <div
                className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                  message.senderRole === 'contractor'
                    ? 'bg-jobblogg-primary/10 text-jobblogg-primary border border-jobblogg-primary/20'
                    : 'bg-jobblogg-success/10 text-jobblogg-success border border-jobblogg-success/20'
                } ${isOwnMessage ? 'bg-white/20 text-white border-white/30' : ''}`}
                role="badge"
                aria-label={`Rolle: ${message.senderDisplayName}`}
              >
                {message.senderDisplayName}
              </div>

              {/* Own message indicator */}
              {isOwnMessage && (
                <span
                  className="text-xs text-white/60 font-medium"
                  aria-label="Din melding"
                >
                  (Du)
                </span>
              )}

              {/* Edit indicator */}
              {message.isEdited && (
                <span
                  className={`text-xs ${
                    isOwnMessage ? 'text-white/60' : 'text-jobblogg-text-muted'
                  }`}
                  aria-label="Melding er redigert"
                >
                  (redigert)
                </span>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <time
                className={`text-xs ${
                  isOwnMessage ? 'text-white/60' : 'text-jobblogg-text-muted'
                }`}
                dateTime={new Date(message.createdAt).toISOString()}
                aria-label={`Sendt ${formatTime(message.createdAt)}`}
              >
                {formatTime(message.createdAt)}
              </time>

              {/* Optimistic status indicators */}
              {isOptimistic && (
                <div className="flex items-center space-x-1">
                  {isSending ? (
                    <div className="flex items-center space-x-1">
                      <div className="animate-spin rounded-full h-3 w-3 border border-current border-t-transparent opacity-60"></div>
                      <span className={`text-xs ${isOwnMessage ? 'text-white/60' : 'text-jobblogg-text-muted'}`}>
                        Sender...
                      </span>
                    </div>
                  ) : sendError ? (
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center space-x-1">
                        <svg className="w-3 h-3 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        <span className="text-xs text-jobblogg-error" title={sendError}>
                          Feil
                        </span>
                      </div>
                      {onRetry && (
                        <button
                          onClick={() => onRetry(message._id)}
                          className="text-xs text-jobblogg-primary hover:text-jobblogg-primary-dark underline focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20 rounded px-1"
                          title="Prøv igjen"
                        >
                          Prøv igjen
                        </button>
                      )}
                    </div>
                  ) : null}
                </div>
              )}
            </div>
          </div>

          {/* Message content */}
          {message.text && (
            <div
              className={`text-sm leading-relaxed ${
                isOwnMessage ? 'text-white' : 'text-jobblogg-text-strong'
              }`}
            >
              {message.text}
            </div>
          )}

          {/* File attachment */}
          {message.file && (
            <div className="mt-3">
              <FileAttachment file={message.file} />
            </div>
          )}

          {/* Emoji reactions */}
          {message.reactions && message.reactions.length > 0 && (
            <div className="mt-3">
              <EmojiReactions
                reactions={message.reactions}
                messageId={message._id}
                userId={userId}
                onReaction={onReaction}
              />
            </div>
          )}

          {/* Delivery status */}
          <div className={`mt-2 ${isOwnMessage ? 'text-right' : 'text-left'}`}>
            <DeliveryStatus
              message={message}
              userId={userId}
            />
          </div>
        </div>
      </div>

      {/* Message actions */}
      {showActions && (
        <div
          className={`flex items-center space-x-2 mt-2 ${
            isOwnMessage ? 'justify-end' : 'justify-start'
          }`}
          role="toolbar"
          aria-label="Meldingshandlinger"
        >
          {/* Reply button */}
          {!isReply && (
            <button
              onClick={() => onReply(message._id)}
              className="text-xs text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors px-2 py-1 rounded hover:bg-jobblogg-card focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20"
              aria-label="Svar på melding"
            >
              Svar
            </button>
          )}

          {/* Reaction button */}
          <button
            onClick={() => onReaction(message._id, '👍')}
            className="text-xs text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors px-2 py-1 rounded hover:bg-jobblogg-neutral-secondary focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20"
            aria-label="Legg til tommel opp reaksjon"
          >
            👍
          </button>

          {/* Edit button (own messages only) */}
          {isOwnMessage && (
            <button
              onClick={() => onEdit(message._id)}
              className="text-xs text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors px-2 py-1 rounded hover:bg-jobblogg-neutral-secondary focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20"
              aria-label="Rediger melding"
            >
              Rediger
            </button>
          )}

          {/* Delete button (own messages only) */}
          {isOwnMessage && (
            <button
              onClick={() => onDelete(message._id)}
              className="text-xs text-jobblogg-text-muted hover:text-jobblogg-error transition-colors px-2 py-1 rounded hover:bg-jobblogg-neutral-secondary focus:outline-none focus:ring-2 focus:ring-jobblogg-error/20"
              aria-label="Slett melding"
            >
              Slett
            </button>
          )}
        </div>
      )}
    </div>
  );
};
