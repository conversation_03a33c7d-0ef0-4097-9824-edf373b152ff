import React from 'react';
import { Message } from '../../types/chat';

export interface DeliveryStatusProps {
  message: Message;
  userId: string;
  className?: string;
}

export const DeliveryStatus: React.FC<DeliveryStatusProps> = ({
  message,
  userId,
  className = ''
}) => {
  // Only show delivery status for own messages
  if (message.senderId !== userId) {
    return null;
  }

  // Don't show status for very old messages (older than 24 hours)
  const messageAge = Date.now() - message.createdAt;
  const isOldMessage = messageAge > 24 * 60 * 60 * 1000;
  if (isOldMessage && message.deliveryStatus === 'delivered') {
    return null;
  }

  const getStatusConfig = () => {
    switch (message.deliveryStatus) {
      case 'sending':
        return {
          icon: (
            <div className="animate-spin rounded-full h-3 w-3 border border-current border-t-transparent opacity-60"></div>
          ),
          text: 'Sender...',
          color: 'text-jobblogg-text-muted'
        };
      case 'sent':
        return {
          icon: (
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          ),
          text: 'Sendt',
          color: 'text-jobblogg-text-muted'
        };
      case 'delivered':
        const deliveredCount = message.deliveredTo ? Object.keys(message.deliveredTo).length : 0;
        return {
          icon: (
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7M13 17l4-4" />
            </svg>
          ),
          text: deliveredCount > 1 ? `Levert (${deliveredCount})` : 'Levert',
          color: 'text-jobblogg-success'
        };
      case 'failed':
        return {
          icon: (
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          ),
          text: 'Feil',
          color: 'text-jobblogg-error'
        };
      default:
        return null;
    }
  };

  const config = getStatusConfig();
  if (!config) return null;

  // Show read status if available
  const readByCount = message.readBy ? Object.keys(message.readBy).filter(id => id !== userId).length : 0;
  const showReadStatus = readByCount > 0 && message.deliveryStatus === 'delivered';

  return (
    <div className={`jobblogg-delivery-status flex items-center space-x-1 ${className}`}>
      <div className={`flex items-center space-x-1 ${config.color}`}>
        {config.icon}
        <span className="text-xs">
          {config.text}
        </span>
      </div>
      
      {showReadStatus && (
        <div className="flex items-center space-x-1 text-jobblogg-primary">
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
          <span className="text-xs">
            {readByCount === 1 ? 'Lest' : `Lest (${readByCount})`}
          </span>
        </div>
      )}
      
      {message.deliveryStatus === 'failed' && message.failureReason && (
        <span className="text-xs text-jobblogg-error opacity-80" title={message.failureReason}>
          - {message.failureReason}
        </span>
      )}
    </div>
  );
};
