import React, { useMemo } from 'react';
import { MessageListProps } from '../../types/chat';
import { MessageItem } from './MessageItem';
import { useVirtualScroll, useElementSize } from '../../hooks/useVirtualScroll';

export interface VirtualizedMessageListProps extends MessageListProps {
  itemHeight?: number; // Average height per message item
  enableVirtualization?: boolean; // Toggle virtualization on/off
}

export const VirtualizedMessageList: React.FC<VirtualizedMessageListProps> = ({
  messages,
  userId,
  onReply,
  onReaction,
  onEdit,
  onDelete,
  onRetry,
  className = '',
  itemHeight = 120, // Estimated average message height
  enableVirtualization = true
}) => {
  const { size, elementRef } = useElementSize();
  
  // Flatten messages for virtualization (root messages + replies)
  const flattenedMessages = useMemo(() => {
    const flattened: Array<{ message: any; level: number; isReply: boolean }> = [];
    
    messages.forEach(rootMessage => {
      // Add root message
      flattened.push({
        message: rootMessage,
        level: 0,
        isReply: false
      });
      
      // Add replies
      if (rootMessage.replies && rootMessage.replies.length > 0) {
        rootMessage.replies.forEach(reply => {
          flattened.push({
            message: reply,
            level: 1,
            isReply: true
          });
        });
      }
    });
    
    return flattened;
  }, [messages]);

  const virtualScroll = useVirtualScroll(flattenedMessages, {
    itemHeight,
    containerHeight: size.height || 400,
    overscan: 3
  });

  // If virtualization is disabled or container is small, render normally
  if (!enableVirtualization || flattenedMessages.length < 20 || size.height < 300) {
    return (
      <div 
        ref={elementRef}
        className={`jobblogg-message-list space-y-4 p-4 overflow-y-auto ${className}`}
      >
        {messages.map((message) => (
          <div key={message._id}>
            {/* Root message */}
            <MessageItem
              message={message}
              userId={userId}
              level={0}
              onReply={onReply}
              onReaction={onReaction}
              onEdit={onEdit}
              onDelete={onDelete}
              onRetry={onRetry}
            />
            
            {/* Replies */}
            {message.replies && message.replies.length > 0 && (
              <div className="ml-8 mt-4 space-y-3 border-l-2 border-jobblogg-border pl-4">
                {message.replies.map((reply) => (
                  <MessageItem
                    key={reply._id}
                    message={reply}
                    userId={userId}
                    level={1}
                    onReply={onReply}
                    onReaction={onReaction}
                    onEdit={onEdit}
                    onDelete={onDelete}
                    onRetry={onRetry}
                  />
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    );
  }

  // Virtualized rendering for large lists
  const visibleItems = flattenedMessages.slice(
    virtualScroll.startIndex,
    virtualScroll.endIndex + 1
  );

  return (
    <div 
      ref={elementRef}
      className={`jobblogg-virtualized-message-list relative overflow-hidden ${className}`}
    >
      <div
        ref={virtualScroll.scrollElementRef}
        className="h-full overflow-y-auto"
        onScroll={virtualScroll.handleScroll}
      >
        {/* Total height spacer */}
        <div style={{ height: virtualScroll.totalHeight, position: 'relative' }}>
          {/* Visible items container */}
          <div
            style={{
              transform: `translateY(${virtualScroll.offsetY}px)`,
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0
            }}
          >
            <div className="space-y-4 p-4">
              {visibleItems.map((item, index) => {
                const actualIndex = virtualScroll.startIndex + index;
                const { message, level, isReply } = item;
                
                return (
                  <div
                    key={`${message._id}-${actualIndex}`}
                    className={isReply ? 'ml-8 border-l-2 border-jobblogg-border pl-4' : ''}
                  >
                    <MessageItem
                      message={message}
                      userId={userId}
                      level={level}
                      onReply={onReply}
                      onReaction={onReaction}
                      onEdit={onEdit}
                      onDelete={onDelete}
                      onRetry={onRetry}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
      
      {/* Scroll indicators */}
      {virtualScroll.startIndex > 0 && (
        <div className="absolute top-0 left-0 right-0 bg-gradient-to-b from-jobblogg-background to-transparent h-8 pointer-events-none" />
      )}
      {virtualScroll.endIndex < flattenedMessages.length - 1 && (
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-jobblogg-background to-transparent h-8 pointer-events-none" />
      )}
    </div>
  );
};
