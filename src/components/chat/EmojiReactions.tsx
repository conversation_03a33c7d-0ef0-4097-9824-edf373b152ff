import React, { useState } from 'react';
import { EmojiReactionProps } from '../../types/chat';

const EMOJI_CATEGORIES = {
  reactions: ['👍', '👎', '❤️', '🔥', '💯', '✅', '❌', '⭐'],
  emotions: ['😊', '😂', '😢', '😮', '😡', '🤔', '😍', '🙄'],
  gestures: ['👏', '🙌', '👌', '✋', '🤝', '💪', '🙏', '👋'],
  objects: ['🎉', '🎊', '💡', '⚡', '🔧', '📝', '📋', '✨']
};

const ALL_EMOJIS = Object.values(EMOJI_CATEGORIES).flat();

export const EmojiReactions: React.FC<EmojiReactionProps> = ({
  reactions = [],
  messageId,
  userId,
  onReaction,
  className = ''
}) => {
  const [showPicker, setShowPicker] = useState(false);

  // Handle emoji click
  const handleEmojiClick = (emoji: string) => {
    onReaction(messageId, emoji);
    setShowPicker(false);
  };

  // Check if user has reacted with specific emoji
  const hasUserReacted = (emoji: string) => {
    const reaction = reactions.find(r => r.emoji === emoji);
    return reaction?.userIds.includes(userId) || false;
  };

  return (
    <div className={`jobblogg-emoji-reactions ${className}`}>
      <div className="flex items-center flex-wrap gap-2">
        {/* Existing reactions */}
        {reactions.map((reaction) => (
          <button
            key={reaction.emoji}
            onClick={() => handleEmojiClick(reaction.emoji)}
            className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs transition-colors ${
              hasUserReacted(reaction.emoji)
                ? 'bg-jobblogg-primary/20 text-jobblogg-primary border border-jobblogg-primary/30'
                : 'bg-jobblogg-neutral-secondary hover:bg-jobblogg-neutral text-jobblogg-text-muted hover:text-jobblogg-text-strong border border-jobblogg-border'
            }`}
          >
            <span>{reaction.emoji}</span>
            <span className="font-medium">{reaction.count}</span>
          </button>
        ))}

        {/* Add reaction button */}
        <div className="relative">
          <button
            onClick={() => setShowPicker(!showPicker)}
            className="inline-flex items-center justify-center w-6 h-6 rounded-full text-jobblogg-text-muted hover:text-jobblogg-primary hover:bg-jobblogg-card transition-colors"
            title="Legg til reaksjon"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </button>

          {/* Enhanced Emoji picker */}
          {showPicker && (
            <div className="absolute bottom-full left-0 mb-2 bg-white border border-jobblogg-border rounded-lg shadow-lg p-3 z-10 w-64">
              <div className="space-y-3">
                {/* Quick reactions */}
                <div>
                  <h4 className="text-xs font-medium text-jobblogg-text-muted mb-2">Hurtigreaksjoner</h4>
                  <div className="grid grid-cols-8 gap-1">
                    {EMOJI_CATEGORIES.reactions.map((emoji) => (
                      <button
                        key={emoji}
                        onClick={() => handleEmojiClick(emoji)}
                        className="w-7 h-7 flex items-center justify-center rounded hover:bg-jobblogg-neutral-secondary transition-colors text-sm"
                        title={`Reager med ${emoji}`}
                      >
                        {emoji}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Emotions */}
                <div>
                  <h4 className="text-xs font-medium text-jobblogg-text-muted mb-2">Følelser</h4>
                  <div className="grid grid-cols-8 gap-1">
                    {EMOJI_CATEGORIES.emotions.map((emoji) => (
                      <button
                        key={emoji}
                        onClick={() => handleEmojiClick(emoji)}
                        className="w-7 h-7 flex items-center justify-center rounded hover:bg-jobblogg-card transition-colors text-sm"
                        title={`Reager med ${emoji}`}
                      >
                        {emoji}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Gestures */}
                <div>
                  <h4 className="text-xs font-medium text-jobblogg-text-muted mb-2">Gester</h4>
                  <div className="grid grid-cols-8 gap-1">
                    {EMOJI_CATEGORIES.gestures.map((emoji) => (
                      <button
                        key={emoji}
                        onClick={() => handleEmojiClick(emoji)}
                        className="w-7 h-7 flex items-center justify-center rounded hover:bg-jobblogg-card transition-colors text-sm"
                        title={`Reager med ${emoji}`}
                      >
                        {emoji}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Objects */}
                <div>
                  <h4 className="text-xs font-medium text-jobblogg-text-muted mb-2">Objekter</h4>
                  <div className="grid grid-cols-8 gap-1">
                    {EMOJI_CATEGORIES.objects.map((emoji) => (
                      <button
                        key={emoji}
                        onClick={() => handleEmojiClick(emoji)}
                        className="w-7 h-7 flex items-center justify-center rounded hover:bg-jobblogg-card transition-colors text-sm"
                        title={`Reager med ${emoji}`}
                      >
                        {emoji}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Click outside to close picker */}
      {showPicker && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowPicker(false)}
        />
      )}
    </div>
  );
};
