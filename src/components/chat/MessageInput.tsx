import React, { useState, useRef, KeyboardEvent, useEffect } from 'react';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { MessageInputProps, MessageFormData } from '../../types/chat';
import { FilePreview } from './FilePreview';
import { useDebounce } from '../../hooks/useDebounce';
import { useKeyboardNavigation } from '../../hooks/useKeyboardNavigation';

export const MessageInput: React.FC<MessageInputProps> = ({
  logId,
  userId,
  userRole,
  parentId,
  placeholder = "Skriv en melding...",
  onSend,
  onCancel,
  onTypingStart,
  onTypingStop,
  className = ''
}) => {
  const [text, setText] = useState('');
  const [file, setFile] = useState<File | null>(null);
  const [uploadedFileData, setUploadedFileData] = useState<{
    url: string;
    name: string;
    size: number;
    type: string;
    thumbnailUrl?: string;
  } | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debounced typing indicator
  const debouncedText = useDebounce(text, 300);
  const [isTypingActive, setIsTypingActive] = useState(false);

  // Handle typing start/stop with debouncing
  useEffect(() => {
    console.log('🟢 MessageInput - useEffect triggered', { textLength: text.length, isTypingActive });
    if (text.length > 0 && !isTypingActive) {
      console.log('🟢 MessageInput - Starting typing indicator');
      setIsTypingActive(true);
      onTypingStart?.();
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    if (text.length > 0) {
      typingTimeoutRef.current = setTimeout(() => {
        console.log('🟢 MessageInput - Stopping typing indicator (timeout)');
        setIsTypingActive(false);
        onTypingStop?.();
      }, 1000); // Stop typing indicator after 1 second of inactivity
    } else if (isTypingActive) {
      console.log('🟢 MessageInput - Stopping typing indicator (empty text)');
      setIsTypingActive(false);
      onTypingStop?.();
    }

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [text, isTypingActive, onTypingStart, onTypingStop]);

  // Convex mutations for file upload
  const generateUploadUrl = useMutation(api.messages.generateChatUploadUrl);
  const storeChatFile = useMutation(api.messages.storeChatFile);

  // Auto-resize textarea
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  };

  // Define allowed file types for chat uploads
  const allowedFileTypes = {
    // Images
    'image/jpeg': { extension: '.jpg', category: 'image' },
    'image/jpg': { extension: '.jpg', category: 'image' },
    'image/png': { extension: '.png', category: 'image' },
    'image/webp': { extension: '.webp', category: 'image' },
    // Documents
    'application/pdf': { extension: '.pdf', category: 'document' },
    'application/msword': { extension: '.doc', category: 'document' },
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { extension: '.docx', category: 'document' },
    'text/plain': { extension: '.txt', category: 'document' },
    // Spreadsheets (useful for project data)
    'application/vnd.ms-excel': { extension: '.xls', category: 'document' },
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': { extension: '.xlsx', category: 'document' }
  };

  // Validate file type
  const validateFileType = (file: File): boolean => {
    return file.type in allowedFileTypes;
  };

  // Handle file upload
  const handleFileUpload = async (selectedFile: File): Promise<{ url: string; name: string; size: number; type: string; thumbnailUrl?: string } | null> => {
    try {
      setIsUploading(true);
      setUploadError(null); // Clear previous errors

      // Validate file type
      if (!validateFileType(selectedFile)) {
        const allowedExtensions = Object.values(allowedFileTypes).map(t => t.extension).join(', ');
        throw new Error(`Filtypen støttes ikke. Tillatte filtyper: ${allowedExtensions}`);
      }

      // Validate file size
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (selectedFile.size > maxSize) {
        throw new Error('Filen er for stor. Maksimal størrelse er 10MB.');
      }

      // Get upload URL
      const uploadUrl = await generateUploadUrl();

      // Upload file to Convex storage
      const result = await fetch(uploadUrl, {
        method: 'POST',
        headers: { 'Content-Type': selectedFile.type },
        body: selectedFile,
      });

      if (!result.ok) {
        throw new Error('Kunne ikke laste opp fil');
      }

      const { storageId } = await result.json();

      // Store file metadata
      const fileData = await storeChatFile({
        storageId,
        fileName: selectedFile.name,
        fileType: selectedFile.type,
        fileSize: selectedFile.size,
        userId
      });

      return fileData;
    } catch (error) {
      console.error('File upload error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Kunne ikke laste opp fil';
      setUploadError(errorMessage);
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  // Handle file selection
  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      const uploadedFile = await handleFileUpload(selectedFile);
      if (uploadedFile) {
        setFile(selectedFile);
        setUploadedFileData(uploadedFile);
        setUploadError(null); // Clear any previous errors

        // If no text is entered, set a user-friendly filename as text
        if (!text.trim()) {
          setText(selectedFile.name);
        }
      }
    }
  };

  // Handle drag and drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile) {
      const uploadedFile = await handleFileUpload(droppedFile);
      if (uploadedFile) {
        setFile(droppedFile);
        setUploadedFileData(uploadedFile);
        setUploadError(null); // Clear any previous errors

        // If no text is entered, set a user-friendly filename as text
        if (!text.trim()) {
          setText(droppedFile.name);
        }
      }
    }
  };

  // Handle text change with typing indicators
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
    adjustTextareaHeight();

    // Trigger typing indicator
    if (onTypingStart && e.target.value.trim()) {
      onTypingStart();
    } else if (onTypingStop && !e.target.value.trim()) {
      onTypingStop();
    }
  };

  // Remove selected file
  const handleRemoveFile = () => {
    setFile(null);
    setUploadedFileData(null);
    setUploadError(null); // Clear any upload errors
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle form submission
  const handleSubmit = async (e?: React.FormEvent) => {
    e?.preventDefault();
    
    if (isSubmitting) return;
    
    const trimmedText = text.trim();
    if (!trimmedText && !file) return;

    setIsSubmitting(true);

    try {
      const formData: MessageFormData = {
        text: trimmedText || undefined,
        file: uploadedFileData || undefined,
        parentId
      };

      await onSend(formData);

      // Reset form and stop typing indicator
      setText('');
      setFile(null);
      setUploadedFileData(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      adjustTextareaHeight();

      // Stop typing indicator after sending
      if (onTypingStop) {
        onTypingStop();
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    // Enter to send (without Shift)
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
      return;
    }

    // Escape to cancel
    if (e.key === 'Escape' && onCancel) {
      e.preventDefault();
      onCancel();
      return;
    }

    // Ctrl/Cmd + Enter to send (alternative)
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSubmit();
      return;
    }

    // Tab to navigate to send button
    if (e.key === 'Tab' && !e.shiftKey) {
      // Let default tab behavior handle this
      return;
    }
  };



  return (
    <div
      className={`jobblogg-message-input ${className} ${isDragOver ? 'drag-over' : ''}`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {isDragOver && (
        <div className="absolute inset-0 bg-jobblogg-primary/10 border-2 border-dashed border-jobblogg-primary rounded-lg flex items-center justify-center z-10">
          <div className="text-center">
            <svg className="w-8 h-8 mx-auto mb-2 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            <p className="text-sm font-medium text-jobblogg-primary">Slipp filen her</p>
          </div>
        </div>
      )}
      <form onSubmit={handleSubmit} className="space-y-3 relative">
        {/* File preview */}
        {file && (
          <FilePreview
            file={file}
            onRemove={handleRemoveFile}
            isUploading={isUploading}
          />
        )}

        {/* Upload error display */}
        {uploadError && (
          <div className="bg-jobblogg-error/10 border border-jobblogg-error/20 rounded-lg p-3 flex items-start space-x-2">
            <svg className="w-5 h-5 text-jobblogg-error flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="flex-1">
              <p className="text-sm text-jobblogg-error font-medium">Filopplasting feilet</p>
              <p className="text-sm text-jobblogg-error/80 mt-1">{uploadError}</p>
            </div>
            <button
              type="button"
              onClick={() => setUploadError(null)}
              className="text-jobblogg-error/60 hover:text-jobblogg-error transition-colors"
              aria-label="Lukk feilmelding"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        )}

        {/* Input area */}
        <div className="flex items-end space-x-3">
          <div className="flex-1">
            <textarea
              ref={textareaRef}
              value={text}
              onChange={handleTextChange}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              rows={1}
              className="w-full px-4 py-3 border border-jobblogg-border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:border-transparent transition-colors"
              disabled={isSubmitting}
              aria-label="Skriv melding"
              aria-describedby={parentId ? "reply-context" : undefined}
            />
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-2">
            {/* File upload button */}
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="p-2 text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors rounded-lg hover:bg-jobblogg-card focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20"
              disabled={isSubmitting || isUploading}
              aria-label={isUploading ? "Laster opp fil..." : "Last opp fil"}
            >
              {isUploading ? (
                <svg className="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
                  />
                </svg>
              )}
            </button>

            {/* Send button */}
            <button
              type="submit"
              disabled={(!text.trim() && !file) || isSubmitting}
              className="px-4 py-2 bg-jobblogg-primary text-white rounded-lg hover:bg-jobblogg-primary-hover disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20"
              aria-label={isSubmitting ? "Sender melding..." : "Send melding"}
            >
              {isSubmitting ? (
                <div
                  className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"
                  aria-hidden="true"
                ></div>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              )}
              <span>Send</span>
            </button>
          </div>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileSelect}
          className="hidden"
          accept=".jpg,.jpeg,.png,.webp,.pdf,.doc,.docx,.txt,.xls,.xlsx"
          aria-label="Velg fil å laste opp"
        />
      </form>

      {/* Help text */}
      <div className="mt-2 space-y-1">
        <p className="text-xs text-jobblogg-text-muted">
          Trykk Enter for å sende, Shift+Enter for ny linje
          {onCancel && ', Escape for å avbryte'}
        </p>
        <p className="text-xs text-jobblogg-text-muted">
          Tillatte filtyper: JPG, PNG, WebP, PDF, DOC, DOCX, TXT, XLS, XLSX (maks 10MB)
        </p>
      </div>
    </div>
  );
};
