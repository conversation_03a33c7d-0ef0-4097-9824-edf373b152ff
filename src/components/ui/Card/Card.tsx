import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  variant?: 'default' | 'elevated' | 'outlined';
}

/**
 * Generic Card Component
 * A flexible container component following JobbLogg design system
 */
const Card: React.FC<CardProps> = ({
  children,
  className = '',
  padding = 'md',
  variant = 'default'
}) => {
  const baseClasses = 'bg-white rounded-xl border border-jobblogg-border';
  
  const paddingClasses = {
    none: '',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6'
  };

  const variantClasses = {
    default: '',
    elevated: 'shadow-sm hover:shadow-md transition-shadow duration-200',
    outlined: 'border-2'
  };

  const classes = [
    baseClasses,
    paddingClasses[padding],
    variantClasses[variant],
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={classes}>
      {children}
    </div>
  );
};

export default Card;
