import React, { useEffect } from 'react';

interface ConfirmDialogProps {
  /** Whether the dialog is open */
  isOpen: boolean;
  /** Dialog title */
  title: string;
  /** Dialog message/description */
  message: string;
  /** Text for the confirm button */
  confirmText?: string;
  /** Text for the cancel button */
  cancelText?: string;
  /** Whether the confirm action is destructive (uses danger styling) */
  isDestructive?: boolean;
  /** Whether the confirm action is loading */
  isLoading?: boolean;
  /** Callback when confirm button is clicked */
  onConfirm: () => void;
  /** Callback when cancel button is clicked or dialog is dismissed */
  onCancel: () => void;
}

/**
 * Confirmation dialog component following JobbLogg design system
 * Provides accessible modal dialog for confirming destructive actions
 * 
 * @example
 * ```tsx
 * <ConfirmDialog
 *   isOpen={showDeleteDialog}
 *   title="Slett prosjekt"
 *   message="Er du sikker på at du vil slette dette prosjektet? Denne handlingen kan ikke angres."
 *   confirmText="Slett prosjekt"
 *   cancelText="Avbryt"
 *   isDestructive={true}
 *   isLoading={isDeleting}
 *   onConfirm={handleDelete}
 *   onCancel={() => setShowDeleteDialog(false)}
 * />
 * ```
 */
export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  title,
  message,
  confirmText = 'Bekreft',
  cancelText = 'Avbryt',
  isDestructive = false,
  isLoading = false,
  onConfirm,
  onCancel,
}) => {
  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onCancel();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when dialog is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onCancel]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm animate-fade-in"
        onClick={onCancel}
        aria-hidden="true"
      />
      
      {/* Dialog */}
      <div 
        className="
          relative bg-white rounded-xl shadow-xl border border-jobblogg-border
          w-full max-w-md mx-4 p-6
          animate-scale-in
        "
        role="dialog"
        aria-modal="true"
        aria-labelledby="dialog-title"
        aria-describedby="dialog-description"
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h2 
            id="dialog-title"
            className="text-xl font-semibold text-jobblogg-text-strong"
          >
            {title}
          </h2>
          <button
            onClick={onCancel}
            className="
              p-2 rounded-lg text-jobblogg-text-muted hover:text-jobblogg-text-strong
              hover:bg-jobblogg-neutral transition-colors duration-200
              focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/30
            "
            aria-label="Lukk dialog"
            disabled={isLoading}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="mb-6">
          <p 
            id="dialog-description"
            className="text-jobblogg-text-medium leading-relaxed"
          >
            {message}
          </p>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end gap-3">
          <button
            onClick={onCancel}
            disabled={isLoading}
            className="
              px-4 py-2 rounded-lg font-medium
              text-jobblogg-text-medium hover:text-jobblogg-text-strong
              hover:bg-jobblogg-neutral
              transition-colors duration-200
              focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/30
              disabled:opacity-50 disabled:cursor-not-allowed
            "
          >
            {cancelText}
          </button>
          
          <button
            onClick={onConfirm}
            disabled={isLoading}
            className={`
              px-4 py-2 rounded-lg font-medium
              transition-all duration-200
              focus:outline-none focus:ring-2 focus:ring-offset-2
              disabled:opacity-50 disabled:cursor-not-allowed
              flex items-center gap-2
              ${isDestructive 
                ? 'bg-jobblogg-error text-white hover:bg-jobblogg-error-dark focus:ring-jobblogg-error/30' 
                : 'bg-jobblogg-primary text-white hover:bg-jobblogg-primary-dark focus:ring-jobblogg-primary/30'
              }
            `}
          >
            {isLoading && (
              <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
            )}
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};
