import React, { useState, useEffect } from 'react';
import { getPostalCodeInfo } from '../../../utils/addressApi';
import type { PostalCodeResult } from '../../../utils/addressApi';

export interface PostalCodeInputProps {
  label: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  onCityChange?: (city: string) => void;
  onPostalCodeInfo?: (info: PostalCodeResult | null) => void;
  error?: string;
  helperText?: string;
  required?: boolean;
  fullWidth?: boolean;
  className?: string;
}

export const PostalCodeInput: React.FC<PostalCodeInputProps> = ({
  label,
  placeholder = 'F.eks. 0123',
  value,
  onChange,
  onCityChange,
  onPostalCodeInfo,
  error,
  helperText = '4 siffer',
  required = false,
  fullWidth = false,
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [postalCodeInfo, setPostalCodeInfo] = useState<PostalCodeResult | null>(null);
  const [validationError, setValidationError] = useState<string>('');

  // Handle postal code lookup when value changes
  useEffect(() => {
    const lookupPostalCode = async () => {
      // Reset states
      setValidationError('');
      setPostalCodeInfo(null);
      
      // Don't lookup if empty or invalid format
      if (!value || !/^\d{4}$/.test(value)) {
        if (onCityChange) onCityChange('');
        if (onPostalCodeInfo) onPostalCodeInfo(null);
        return;
      }

      setIsLoading(true);
      
      try {
        const info = await getPostalCodeInfo(value);
        
        if (info) {
          setPostalCodeInfo(info);
          if (onCityChange) onCityChange(info.city);
          if (onPostalCodeInfo) onPostalCodeInfo(info);
        } else {
          setValidationError('Ugyldig postnummer');
          if (onCityChange) onCityChange('');
          if (onPostalCodeInfo) onPostalCodeInfo(null);
        }
      } catch (error) {
        console.error('Postal code lookup failed:', error);
        setValidationError('Kunne ikke slå opp postnummer');
        if (onCityChange) onCityChange('');
        if (onPostalCodeInfo) onPostalCodeInfo(null);
      } finally {
        setIsLoading(false);
      }
    };

    // Debounce the lookup to avoid too many API calls
    const timeoutId = setTimeout(lookupPostalCode, 500);
    return () => clearTimeout(timeoutId);
  }, [value, onCityChange, onPostalCodeInfo]);

  // Handle input change with validation
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    
    // Only allow digits and limit to 4 characters
    const sanitizedValue = newValue.replace(/\D/g, '').slice(0, 4);
    onChange(sanitizedValue);
  };

  const displayError = error || validationError;

  const baseClasses = `
    relative block w-full px-3 py-2 text-base
    bg-white border rounded-lg
    placeholder-jobblogg-text-muted
    focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20 focus:border-jobblogg-primary
    transition-colors duration-200
    ${displayError 
      ? 'border-jobblogg-error text-jobblogg-error focus:border-jobblogg-error focus:ring-jobblogg-error/20' 
      : 'border-jobblogg-border text-jobblogg-text-strong hover:border-jobblogg-border-hover'
    }
    ${fullWidth ? 'w-full' : ''}
  `.trim();

  return (
    <div className={`relative ${fullWidth ? 'w-full' : ''} ${className}`}>
      {/* Label */}
      <label className="block text-sm font-medium text-jobblogg-text-strong mb-2">
        {label}
        {required && <span className="text-jobblogg-error ml-1">*</span>}
      </label>

      {/* Input with status indicators */}
      <div className="relative">
        <input
          type="text"
          value={value}
          onChange={handleInputChange}
          placeholder={placeholder}
          className={baseClasses}
          maxLength={4}
          inputMode="numeric"
          pattern="\d{4}"
        />
        
        {/* Status indicator */}
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
          {isLoading && (
            <div className="w-4 h-4 border-2 border-jobblogg-primary/20 border-t-jobblogg-primary rounded-full animate-spin"></div>
          )}
          
          {!isLoading && postalCodeInfo && (
            <div className="w-4 h-4 text-jobblogg-success">
              <svg fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
          )}
          
          {!isLoading && validationError && value.length === 4 && (
            <div className="w-4 h-4 text-jobblogg-error">
              <svg fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
          )}
        </div>
      </div>



      {/* Helper text or error */}
      {(helperText || displayError) && !postalCodeInfo && (
        <p className={`mt-2 text-sm ${displayError ? 'text-jobblogg-error' : 'text-jobblogg-text-muted'}`}>
          {displayError || helperText}
        </p>
      )}
    </div>
  );
};
