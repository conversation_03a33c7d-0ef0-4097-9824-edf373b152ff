import React from 'react';

interface ArchiveStatusBadgeProps {
  isArchived?: boolean;
  archivedAt?: number;
  className?: string;
}

export const ArchiveStatusBadge: React.FC<ArchiveStatusBadgeProps> = ({
  isArchived = false,
  archivedAt,
  className = ''
}) => {
  if (!isArchived) {
    return null;
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className={`inline-flex items-center gap-2 ${className}`}>
      <span className="
        inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium
        bg-jobblogg-warning/10 text-jobblogg-warning border border-jobblogg-warning/20
      ">
        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H7a2 2 0 01-2-2V8z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6" />
        </svg>
        Arkivert
      </span>
      {archivedAt && (
        <span className="text-xs text-jobblogg-text-muted">
          {formatDate(archivedAt)}
        </span>
      )}
    </div>
  );
};
