import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  PrimaryButton, 
  ProjectCard, 
  TextStrong, 
  TextMedium, 
  TextMuted, 
  PageLayout, 
  EmptyState 
} from './index';

/**
 * Demonstration component showing all UI components in action
 * This file serves as a visual reference and testing ground for the component library
 * 
 * To view this demo, temporarily add a route in App.tsx:
 * <Route path="/demo" element={<ComponentDemo />} />
 */
export const ComponentDemo: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleButtonClick = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  };

  const mockProjects = [
    {
      id: '1',
      title: 'Kjøkkenrenovering',
      description: 'Komplett renovering av kjøkken med nye skap, benkeplate og hvitevarer. Prosjektet inkluderer også ny flislegging og maling.',
      updatedAt: '2025-01-26',
    },
    {
      id: '2', 
      title: 'Terrasse bygging',
      description: 'Bygging av ny terrasse i tre med rekkverk og trapp. Inkluderer grunnarbeid og drenering.',
      updatedAt: '2025-01-25',
    },
    {
      id: '3',
      title: 'Baderomsoppussing',
      description: 'Fullstendig oppussing av hovedbad med nye fliser, sanitærutstyr og belysning.',
      updatedAt: '2025-01-24',
    },
  ];

  const PlusIcon = (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
    </svg>
  );

  return (
    <PageLayout 
      title="UI Component Demo" 
      showBackButton 
      backUrl="/"
      headerActions={
        <div className="text-jobblogg-text-muted text-sm">
          Komponentbibliotek demonstrasjon
        </div>
      }
    >
      <div className="space-y-12">
        
        {/* Typography Section */}
        <section>
          <TextStrong as="h2" className="text-2xl mb-6">Typography Components</TextStrong>
          <div className="space-y-4 bg-jobblogg-blue-50 p-6 rounded-xl">
            <TextStrong as="h3" className="text-xl">TextStrong - Overskrifter og viktig tekst</TextStrong>
            <TextMedium>TextMedium - Brødtekst og beskrivelser med god lesbarhet</TextMedium>
            <TextMuted>TextMuted - Sekundær informasjon og metadata</TextMuted>
            
            <div className="mt-4 p-4 bg-white rounded-lg">
              <TextStrong>Kontrastforhold:</TextStrong>
              <ul className="mt-2 space-y-1">
                <li><TextMedium>• TextStrong: 16.8:1 (WCAG AA)</TextMedium></li>
                <li><TextMedium>• TextMedium: 7.3:1 (WCAG AA)</TextMedium></li>
                <li><TextMedium>• TextMuted: 4.9:1 (WCAG AA)</TextMedium></li>
              </ul>
            </div>
          </div>
        </section>

        {/* Button Section */}
        <section>
          <TextStrong as="h2" className="text-2xl mb-6">Button Components</TextStrong>
          <div className="space-y-4 bg-jobblogg-blue-50 p-6 rounded-xl">
            <div className="flex flex-wrap gap-4">
              <PrimaryButton onClick={handleButtonClick}>
                Standard knapp
              </PrimaryButton>
              
              <PrimaryButton 
                onClick={handleButtonClick} 
                loading={loading}
                icon={PlusIcon}
              >
                Med ikon og loading
              </PrimaryButton>
              
              <PrimaryButton disabled>
                Deaktivert knapp
              </PrimaryButton>
              
              <PrimaryButton className="w-full sm:w-auto">
                Responsiv knapp
              </PrimaryButton>
            </div>
            
            <TextMuted>
              Alle knapper støtter keyboard navigation (Tab, Enter, Space) og har synlige focus states.
            </TextMuted>
          </div>
        </section>

        {/* Project Cards Section */}
        <section>
          <TextStrong as="h2" className="text-2xl mb-6">Project Card Components</TextStrong>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockProjects.map((project, index) => (
              <ProjectCard
                key={project.id}
                title={project.title}
                description={project.description}
                projectId={project.id}
                updatedAt={project.updatedAt}
                onClick={() => console.log(`Clicked project: ${project.title}`)}
                animationDelay={`${index * 0.1}s`}
              />
            ))}
          </div>
          <TextMuted className="mt-4">
            Prosjektkort med gradient bakgrunn, hover-effekter og staggered animasjoner.
          </TextMuted>
        </section>

        {/* Empty State Section */}
        <section>
          <TextStrong as="h2" className="text-2xl mb-6">Empty State Component</TextStrong>
          <div className="space-y-6">
            <EmptyState
              title="Ingen prosjekter ennå"
              description="Opprett ditt første prosjekt for å komme i gang med dokumentering av arbeidsframgang"
              actionLabel="Opprett prosjekt"
              onAction={() => navigate('/create')}
            />
            
            <EmptyState
              title="Ingen loggoppføringer"
              description="Start dokumentering ved å legge til din første oppføring med bilder og beskrivelse"
              icon={
                <svg className="w-16 h-16 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              }
            />
          </div>
        </section>

        {/* Usage Examples */}
        <section>
          <TextStrong as="h2" className="text-2xl mb-6">Brukseksempler</TextStrong>
          <div className="bg-jobblogg-blue-50 p-6 rounded-xl">
            <TextMedium className="mb-4">
              Komponentene kan importeres og brukes på følgende måter:
            </TextMedium>
            
            <div className="bg-white p-4 rounded-lg font-mono text-sm overflow-x-auto">
              <pre>{`// Enkeltimport
import { PrimaryButton, ProjectCard, TextStrong } from '@/components/ui';

// Spesifikk import
import { PrimaryButton } from '@/components/ui/Button';
import { ProjectCard } from '@/components/ui/Card';

// Bruk i komponenter
<PrimaryButton onClick={handleClick} loading={isLoading}>
  Lagre endringer
</PrimaryButton>

<ProjectCard
  title="Prosjektnavn"
  description="Beskrivelse"
  projectId="123"
  updatedAt="2025-01-26"
  onClick={() => navigate('/project/123')}
/>`}</pre>
            </div>
          </div>
        </section>

      </div>
    </PageLayout>
  );
};

export default ComponentDemo;
