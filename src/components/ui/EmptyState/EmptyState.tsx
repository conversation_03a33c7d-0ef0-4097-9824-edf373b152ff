import React from 'react';

interface EmptyStateProps {
  /** Main title */
  title: string;
  /** Description text */
  description: string;
  /** Action button label */
  actionLabel?: string;
  /** Action button click handler */
  onAction?: () => void;
  /** Custom icon */
  icon?: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Empty state component for displaying when no content is available
 * Provides consistent styling and optional call-to-action
 * 
 * @example
 * ```tsx
 * <EmptyState
 *   title="Ingen prosjekter ennå"
 *   description="Opprett ditt første prosjekt for å komme i gang med dokumentering"
 *   actionLabel="Opprett prosjekt"
 *   onAction={() => navigate('/create')}
 * />
 * 
 * <EmptyState
 *   title="Ingen loggoppføringer"
 *   description="Start dokumentering ved å legge til din første oppføring"
 *   icon={<DocumentIcon />}
 * />
 * ```
 */
export const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  actionLabel,
  onAction,
  icon,
  className = '',
}) => {
  const defaultIcon = (
    <svg
      className="w-16 h-16 text-jobblogg-primary"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1}
        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
      />
    </svg>
  );

  return (
    <div
      className={`
        bg-jobblogg-blue-50 rounded-xl p-8 text-center animate-fade-in
        ${className}
      `.trim().replace(/\s+/g, ' ')}
    >
      {/* Icon */}
      <div className="flex justify-center mb-6">
        {icon || defaultIcon}
      </div>

      {/* Title */}
      <h3 className="text-xl font-semibold text-jobblogg-text-strong mb-3">
        {title}
      </h3>

      {/* Description */}
      <p className="text-jobblogg-text-medium mb-6 max-w-md mx-auto leading-relaxed">
        {description}
      </p>

      {/* Action Button */}
      {actionLabel && onAction && (
        <button
          onClick={onAction}
          className="btn-primary-solid inline-flex items-center gap-2"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 4v16m8-8H4"
            />
          </svg>
          {actionLabel}
        </button>
      )}
    </div>
  );
};

export default EmptyState;
