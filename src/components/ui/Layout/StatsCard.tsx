import React from 'react';

interface StatsCardProps {
  /** Card title */
  title: string;
  /** Main value to display */
  value: string | number;
  /** Optional subtitle or description */
  subtitle?: string;
  /** Icon element */
  icon?: React.ReactNode;
  /** Color variant */
  variant?: 'primary' | 'accent' | 'warning' | 'neutral';
  /** Additional CSS classes */
  className?: string;
  /** Animation delay */
  animationDelay?: string;
  /** Click handler */
  onClick?: () => void;
}

/**
 * Stats card component for displaying key metrics and statistics
 * Optimized for dashboard layouts with consistent styling
 * 
 * @example
 * ```tsx
 * <StatsCard
 *   title="Totalt prosjekter"
 *   value={12}
 *   subtitle="Aktive prosjekter"
 *   variant="primary"
 *   icon={<ProjectIcon />}
 * />
 * 
 * <StatsCard
 *   title="Denne måneden"
 *   value={3}
 *   variant="accent"
 *   onClick={() => navigate('/monthly')}
 * />
 * ```
 */
export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  variant = 'neutral',
  className = '',
  animationDelay = '0s',
  onClick,
}) => {
  // Get variant styles
  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return {
          iconBg: 'bg-jobblogg-primary-soft',
          iconColor: 'text-jobblogg-primary',
          valueColor: 'text-jobblogg-primary',
        };
      case 'accent':
        return {
          iconBg: 'bg-jobblogg-accent-soft',
          iconColor: 'text-jobblogg-accent',
          valueColor: 'text-jobblogg-accent',
        };
      case 'warning':
        return {
          iconBg: 'bg-jobblogg-warning-soft',
          iconColor: 'text-jobblogg-warning',
          valueColor: 'text-jobblogg-warning',
        };
      default:
        return {
          iconBg: 'bg-jobblogg-neutral',
          iconColor: 'text-jobblogg-text-medium',
          valueColor: 'text-jobblogg-text-strong',
        };
    }
  };

  const styles = getVariantStyles();
  const isClickable = !!onClick;

  return (
    <div
      className={`
        ${isClickable ? 'card-elevated cursor-pointer hover-lift' : 'card-modern shadow-medium'}
        animate-scale-in
        ${className}
      `.trim().replace(/\s+/g, ' ')}
      style={{ animationDelay }}
      onClick={onClick}
      role={isClickable ? 'button' : undefined}
      tabIndex={isClickable ? 0 : undefined}
      onKeyDown={isClickable ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick?.();
        }
      } : undefined}
    >
      <div className="flex items-center gap-4">
        {/* Icon */}
        {icon && (
          <div className={`p-3 ${styles.iconBg} rounded-xl flex-shrink-0`}>
            <div className={`w-6 h-6 ${styles.iconColor}`}>
              {icon}
            </div>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 min-w-0">
          <h3 className={`text-2xl font-bold ${styles.valueColor} mb-1`}>
            {value}
          </h3>
          <p className="text-sm text-jobblogg-text-strong font-medium mb-1">
            {title}
          </p>
          {subtitle && (
            <p className="text-xs text-jobblogg-text-muted">
              {subtitle}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default StatsCard;
