import React from 'react';

export interface Step {
  id: number;
  title: string;
  description?: string;
  isCompleted: boolean;
  isActive: boolean;
}

export interface StepProgressProps {
  steps: Step[];
  currentStep: number;
  className?: string;
}

export const StepProgress: React.FC<StepProgressProps> = ({
  steps,
  currentStep,
  className = ''
}) => {
  return (
    <div className={`w-full ${className}`} role="progressbar" aria-valuenow={currentStep} aria-valuemin={1} aria-valuemax={steps.length}>
      {/* Mobile Progress Bar */}
      <div className="block sm:hidden mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-jobblogg-text-strong">
            Steg {currentStep} av {steps.length}
          </span>
          <span className="text-sm text-jobblogg-text-muted">
            {Math.round((currentStep / steps.length) * 100)}%
          </span>
        </div>
        <div className="w-full bg-jobblogg-neutral rounded-full h-2">
          <div 
            className="bg-jobblogg-primary h-2 rounded-full transition-all duration-300 ease-in-out"
            style={{ width: `${(currentStep / steps.length) * 100}%` }}
          />
        </div>
        <div className="mt-2">
          <h3 className="text-lg font-semibold text-jobblogg-text-strong">
            {steps.find(step => step.id === currentStep)?.title}
          </h3>
          {steps.find(step => step.id === currentStep)?.description && (
            <p className="text-sm text-jobblogg-text-muted mt-1">
              {steps.find(step => step.id === currentStep)?.description}
            </p>
          )}
        </div>
      </div>

      {/* Desktop Step Indicator */}
      <div className="hidden sm:block">
        <nav aria-label="Fremgang" className="mb-8">
          <ol className="flex items-center justify-between">
            {steps.map((step, index) => {
              const isLast = index === steps.length - 1;
              
              return (
                <li key={step.id} className="flex items-center flex-1">
                  <div className="flex items-center">
                    {/* Step Circle */}
                    <div
                      className={`
                        flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200
                        ${step.isCompleted 
                          ? 'bg-jobblogg-primary border-jobblogg-primary text-white' 
                          : step.isActive 
                            ? 'bg-white border-jobblogg-primary text-jobblogg-primary ring-4 ring-jobblogg-primary/20' 
                            : 'bg-white border-jobblogg-border text-jobblogg-text-muted'
                        }
                      `}
                      aria-current={step.isActive ? 'step' : undefined}
                    >
                      {step.isCompleted ? (
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      ) : (
                        <span className="text-sm font-semibold">{step.id}</span>
                      )}
                    </div>

                    {/* Step Content */}
                    <div className="ml-4 min-w-0 flex-1">
                      <h3 
                        className={`
                          text-sm font-medium transition-colors duration-200
                          ${step.isActive 
                            ? 'text-jobblogg-primary' 
                            : step.isCompleted 
                              ? 'text-jobblogg-text-strong' 
                              : 'text-jobblogg-text-muted'
                          }
                        `}
                      >
                        {step.title}
                      </h3>
                      {step.description && (
                        <p className="text-xs text-jobblogg-text-muted mt-1">
                          {step.description}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Connector Line */}
                  {!isLast && (
                    <div 
                      className={`
                        flex-1 h-0.5 mx-4 transition-colors duration-200
                        ${step.isCompleted 
                          ? 'bg-jobblogg-primary' 
                          : 'bg-jobblogg-border'
                        }
                      `}
                      aria-hidden="true"
                    />
                  )}
                </li>
              );
            })}
          </ol>
        </nav>
      </div>
    </div>
  );
};

export default StepProgress;
