import React from 'react';

export interface WizardStepProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  isActive: boolean;
  className?: string;
}

export const WizardStep: React.FC<WizardStepProps> = ({
  children,
  title,
  description,
  isActive,
  className = ''
}) => {
  if (!isActive) {
    return null;
  }

  return (
    <div className={`animate-slide-up ${className}`}>
      <div className="text-center mb-8">
        <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
          <svg className="w-8 h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-jobblogg-text-strong mb-2">
          {title}
        </h2>
        {description && (
          <p className="text-jobblogg-text-muted">
            {description}
          </p>
        )}
      </div>
      
      <div className="space-y-6">
        {children}
      </div>
    </div>
  );
};

export default WizardStep;
