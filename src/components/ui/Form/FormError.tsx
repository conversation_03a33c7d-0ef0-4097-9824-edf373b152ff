import React from 'react';

/**
 * Props for the FormError component
 */
export interface FormErrorProps {
  /** Error message to display */
  message?: string;
  /** Single error message (alias for message) */
  error?: string;
  /** Array of error messages to display */
  messages?: string[];
  /** Whether to show the error icon */
  showIcon?: boolean;
  /** Custom className for styling */
  className?: string;
  /** ID for the error element (for aria-describedby) */
  id?: string;
}

/**
 * FormError component for displaying form validation errors
 * 
 * Features:
 * - WCAG AA compliant with proper contrast ratios
 * - Screen reader friendly with ARIA attributes
 * - Live region announcements for dynamic errors
 * - Support for single or multiple error messages
 * - Consistent styling with JobbLogg design system
 * - Proper semantic markup with role="alert"
 * 
 * @example
 * ```tsx
 * // Single error message
 * <FormError message="Dette feltet er påkrevd" />
 * 
 * // Multiple error messages
 * <FormError 
 *   messages={[
 *     "Passordet må være minst 8 tegn",
 *     "Passordet må inneholde minst én stor bokstav",
 *     "Passordet må inneholde minst ett tall"
 *   ]} 
 * />
 * 
 * // With custom ID for aria-describedby
 * <FormError 
 *   id="password-errors"
 *   message="Ugyldig passord format" 
 * />
 * ```
 */
export const FormError: React.FC<FormErrorProps> = ({
  message,
  error,
  messages,
  showIcon = true,
  className = '',
  id
}) => {
  // Don't render if no error messages
  if (!message && !error && (!messages || messages.length === 0)) {
    return null;
  }

  // Determine which messages to display
  const errorMessages = messages && messages.length > 0
    ? messages
    : (message || error)
      ? [message || error]
      : [];

  if (errorMessages.length === 0) {
    return null;
  }

  // Generate unique ID if not provided
  const errorId = id || `form-error-${Math.random().toString(36).substr(2, 9)}`;

  // Error icon
  const ErrorIcon = () => (
    <svg 
      className="w-4 h-4 flex-shrink-0" 
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={2} 
        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" 
      />
    </svg>
  );

  // Single error message
  if (errorMessages.length === 1) {
    return (
      <div 
        id={errorId}
        className={`
          text-sm text-jobblogg-error flex items-start gap-2 mt-1
          ${className}
        `}
        role="alert"
        aria-live="polite"
      >
        {showIcon && <ErrorIcon />}
        <span className="flex-1">{errorMessages[0]}</span>
      </div>
    );
  }

  // Multiple error messages
  return (
    <div 
      id={errorId}
      className={`
        text-sm text-jobblogg-error mt-1
        ${className}
      `}
      role="alert"
      aria-live="polite"
    >
      <div className="flex items-start gap-2 mb-1">
        {showIcon && <ErrorIcon />}
        <span className="font-medium">Følgende feil må rettes:</span>
      </div>
      <ul className="list-disc list-inside space-y-1 ml-6">
        {errorMessages.map((errorMessage, index) => (
          <li key={index} className="text-jobblogg-error">
            {errorMessage}
          </li>
        ))}
      </ul>
    </div>
  );
};

/**
 * FormFieldError component for individual field errors
 * Simplified version of FormError for single field validation
 */
export interface FormFieldErrorProps {
  /** Error message to display */
  error?: string;
  /** ID for the error element (for aria-describedby) */
  id?: string;
  /** Custom className for styling */
  className?: string;
}

export const FormFieldError: React.FC<FormFieldErrorProps> = ({
  error,
  id,
  className = ''
}) => {
  if (!error) {
    return null;
  }

  const errorId = id || `field-error-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <p 
      id={errorId}
      className={`
        mt-1 text-sm text-jobblogg-error flex items-center gap-1
        ${className}
      `}
      role="alert"
      aria-live="polite"
    >
      <svg 
        className="w-4 h-4 flex-shrink-0" 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
        aria-hidden="true"
      >
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth={2} 
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" 
        />
      </svg>
      {error}
    </p>
  );
};
