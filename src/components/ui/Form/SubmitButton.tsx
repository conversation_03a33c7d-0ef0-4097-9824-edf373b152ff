import React from 'react';

/**
 * Props for the SubmitButton component
 */
export interface SubmitButtonProps extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'type'> {
  /** Button text content */
  children: React.ReactNode;
  /** Whether the button is in loading state */
  loading?: boolean;
  /** Loading text to display when loading is true */
  loadingText?: string;
  /** Whether the button is disabled */
  disabled?: boolean;
  /** Size variant of the button */
  size?: 'small' | 'medium' | 'large';
  /** Whether to show the button in full width */
  fullWidth?: boolean;
  /** Icon to display before the text */
  icon?: React.ReactNode;
  /** Custom loading spinner */
  loadingSpinner?: React.ReactNode;
}

/**
 * SubmitButton component specifically designed for form submissions
 * 
 * Features:
 * - WCAG AA compliant with proper contrast ratios
 * - Keyboard navigation support (Enter and Space)
 * - Screen reader friendly with ARIA attributes
 * - Loading state with accessible announcements
 * - Focus management and visual indicators
 * - Consistent styling with JobbLogg design system
 * - Automatic type="submit" for form submission
 * - Prevents double submission when loading
 * 
 * @example
 * ```tsx
 * <SubmitButton
 *   loading={isSubmitting}
 *   loadingText="Lagrer prosjekt..."
 *   disabled={!isValid}
 *   fullWidth
 *   icon={<SaveIcon />}
 * >
 *   Opprett prosjekt
 * </SubmitButton>
 * ```
 */
export const SubmitButton: React.FC<SubmitButtonProps> = ({
  children,
  loading = false,
  loadingText,
  disabled = false,
  size = 'medium',
  fullWidth = false,
  icon,
  loadingSpinner,
  className = '',
  ...props
}) => {
  // Determine if button should be disabled
  const isDisabled = disabled || loading;

  // Size classes
  const sizeClasses = {
    small: 'h-8 px-3 text-sm',
    medium: 'h-10 px-4 text-base',
    large: 'h-12 px-6 text-lg'
  };

  // Base button classes with WCAG AA compliance
  const baseClasses = `
    inline-flex items-center justify-center gap-2 rounded-lg font-medium
    transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2
    bg-jobblogg-primary text-white
    hover:bg-jobblogg-primary-hover active:bg-jobblogg-primary-active
    focus:ring-jobblogg-primary focus:ring-offset-white
    disabled:bg-jobblogg-neutral-secondary disabled:text-jobblogg-text-muted 
    disabled:cursor-not-allowed disabled:shadow-none
    shadow-sm hover:shadow-md active:shadow-sm
    ${sizeClasses[size]}
    ${fullWidth ? 'w-full' : ''}
  `.trim().replace(/\s+/g, ' ');

  // Default loading spinner
  const defaultSpinner = (
    <svg 
      className="w-4 h-4 animate-spin" 
      fill="none" 
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <circle 
        className="opacity-25" 
        cx="12" 
        cy="12" 
        r="10" 
        stroke="currentColor" 
        strokeWidth="4"
      />
      <path 
        className="opacity-75" 
        fill="currentColor" 
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );

  // Content to display
  const buttonContent = loading ? (
    <>
      {loadingSpinner || defaultSpinner}
      <span>{loadingText || 'Laster...'}</span>
    </>
  ) : (
    <>
      {icon && <span className="flex-shrink-0">{icon}</span>}
      <span>{children}</span>
    </>
  );

  return (
    <button
      type="submit"
      className={`${baseClasses} ${className}`}
      disabled={isDisabled}
      aria-disabled={isDisabled}
      aria-describedby={loading ? 'submit-loading-status' : undefined}
      {...props}
    >
      {buttonContent}
      
      {/* Screen reader loading announcement */}
      {loading && (
        <span 
          id="submit-loading-status" 
          className="sr-only" 
          aria-live="polite"
        >
          {loadingText || 'Sender inn skjema...'}
        </span>
      )}
    </button>
  );
};

/**
 * FormSubmitButton - Alias for SubmitButton for backward compatibility
 */
export const FormSubmitButton = SubmitButton;
