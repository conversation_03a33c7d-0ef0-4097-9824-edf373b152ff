import React from 'react';

interface SwitchProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label: string;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const Switch: React.FC<SwitchProps> = ({
  checked,
  onChange,
  label,
  disabled = false,
  size = 'md',
  className = ''
}) => {
  const sizeClasses = {
    sm: {
      switch: 'w-8 h-4',
      thumb: 'w-3 h-3',
      translate: 'translate-x-4'
    },
    md: {
      switch: 'w-10 h-5',
      thumb: 'w-4 h-4',
      translate: 'translate-x-5'
    },
    lg: {
      switch: 'w-12 h-6',
      thumb: 'w-5 h-5',
      translate: 'translate-x-6'
    }
  };

  const currentSize = sizeClasses[size];

  return (
    <label className={`inline-flex items-center cursor-pointer ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`}>
      <div className="relative">
        <input
          type="checkbox"
          checked={checked}
          onChange={(e) => onChange(e.target.checked)}
          disabled={disabled}
          className="sr-only"
          aria-label={label}
        />
        <div
          className={`
            ${currentSize.switch}
            ${checked 
              ? 'bg-jobblogg-primary border-jobblogg-primary' 
              : 'bg-jobblogg-neutral border-jobblogg-border'
            }
            border-2 rounded-full transition-all duration-200 ease-in-out
            ${disabled ? '' : 'hover:shadow-md focus-within:ring-2 focus-within:ring-jobblogg-primary focus-within:ring-opacity-50'}
          `}
        >
          <div
            className={`
              ${currentSize.thumb}
              ${checked 
                ? `${currentSize.translate} bg-white` 
                : 'translate-x-0 bg-jobblogg-text-muted'
              }
              rounded-full transition-all duration-200 ease-in-out transform
              shadow-sm
            `}
          />
        </div>
      </div>
      <span className="sr-only">{label}</span>
    </label>
  );
};
