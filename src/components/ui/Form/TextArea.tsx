import React, { forwardRef } from 'react';

/**
 * Props for the TextArea component
 */
export interface TextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  /** Textarea label text */
  label?: string;
  /** Error message to display */
  error?: string;
  /** Helper text to display below the textarea */
  helperText?: string;
  /** Whether the textarea is required */
  required?: boolean;
  /** Whether the textarea is disabled */
  disabled?: boolean;
  /** Size variant of the textarea */
  size?: 'small' | 'medium' | 'large';
  /** Whether to show the textarea in full width */
  fullWidth?: boolean;
  /** Whether to show character count */
  showCharCount?: boolean;
  /** Whether to show character count (alias for showCharCount) */
  showCharacterCount?: boolean;
  /** Maximum character count */
  maxLength?: number;
}

/**
 * TextArea component with WCAG AA accessibility compliance
 * 
 * Features:
 * - WCAG AA compliant with proper contrast ratios
 * - Keyboard navigation support
 * - Screen reader friendly with ARIA attributes
 * - Error state handling with proper announcements
 * - Focus management and visual indicators
 * - Character count with accessibility support
 * - Consistent styling with JobbLogg design system
 * 
 * @example
 * ```tsx
 * <TextArea
 *   label="Prosjektbeskrivelse"
 *   placeholder="Beskriv prosjektet ditt..."
 *   required
 *   error={errors.description}
 *   helperText="Gi en detaljert beskrivelse av prosjektet"
 *   showCharCount
 *   maxLength={500}
 * />
 * ```
 */
export const TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(
  (
    {
      label,
      error,
      helperText,
      required = false,
      disabled = false,
      size = 'medium',
      fullWidth = false,
      showCharCount = false,
      showCharacterCount,
      maxLength,
      className = '',
      id,
      value,
      'aria-describedby': ariaDescribedBy,
      ...props
    },
    ref
  ) => {
    // Generate unique IDs for accessibility
    const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;
    const errorId = error ? `${textareaId}-error` : undefined;
    const helperTextId = helperText ? `${textareaId}-helper` : undefined;
    const shouldShowCharCount = showCharCount || showCharacterCount;
    const charCountId = shouldShowCharCount ? `${textareaId}-charcount` : undefined;
    
    // Build aria-describedby attribute
    const describedBy = [
      ariaDescribedBy,
      errorId,
      helperTextId,
      charCountId
    ].filter(Boolean).join(' ') || undefined;

    // Calculate character count
    const currentLength = typeof value === 'string' ? value.length : 0;
    const isNearLimit = maxLength && currentLength > maxLength * 0.8;
    const isOverLimit = maxLength && currentLength > maxLength;

    // Size classes
    const sizeClasses = {
      small: 'min-h-[80px] text-sm p-3',
      medium: 'min-h-[100px] text-base p-4',
      large: 'min-h-[120px] text-lg p-4'
    };

    // Modern textarea classes with enhanced flat design
    const baseClasses = `
      textarea-modern
      ${error
        ? 'border-jobblogg-error focus:ring-jobblogg-error focus:border-jobblogg-error'
        : ''
      }
      ${sizeClasses[size]}
      ${fullWidth ? 'w-full' : ''}
    `.trim().replace(/\s+/g, ' ');

    return (
      <div className={`${fullWidth ? 'w-full' : ''} ${className}`}>
        {/* Label */}
        {label && (
          <label 
            htmlFor={textareaId}
            className={`
              block text-sm font-medium mb-2
              ${error ? 'text-jobblogg-error' : 'text-jobblogg-text-strong'}
              ${disabled ? 'text-jobblogg-text-muted' : ''}
            `}
          >
            {label}
            {required && (
              <span className="text-jobblogg-error ml-1" aria-label="required">
                *
              </span>
            )}
          </label>
        )}

        {/* Textarea */}
        <textarea
          ref={ref}
          id={textareaId}
          className={baseClasses}
          disabled={disabled}
          required={required}
          maxLength={maxLength}
          value={value}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={describedBy}
          {...props}
        />

        {/* Footer with helper text and character count */}
        <div className="mt-1 flex justify-between items-start gap-2">
          <div className="flex-1">
            {/* Helper Text */}
            {helperText && !error && (
              <p 
                id={helperTextId}
                className="text-sm text-jobblogg-text-muted"
              >
                {helperText}
              </p>
            )}

            {/* Error Message */}
            {error && (
              <p 
                id={errorId}
                className="text-sm text-jobblogg-error flex items-center gap-1"
                role="alert"
                aria-live="polite"
              >
                <svg 
                  className="w-4 h-4 flex-shrink-0" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" 
                  />
                </svg>
                {error}
              </p>
            )}
          </div>

          {/* Character Count */}
          {shouldShowCharCount && maxLength && (
            <p 
              id={charCountId}
              className={`
                text-sm flex-shrink-0 tabular-nums
                ${isOverLimit 
                  ? 'text-jobblogg-error font-medium' 
                  : isNearLimit 
                    ? 'text-jobblogg-warning' 
                    : 'text-jobblogg-text-muted'
                }
              `}
              aria-live="polite"
              aria-label={`${currentLength} av ${maxLength} tegn brukt`}
            >
              {currentLength}/{maxLength}
            </p>
          )}
        </div>
      </div>
    );
  }
);

TextArea.displayName = 'TextArea';
