import React, { forwardRef } from 'react';

/**
 * Props for the SelectInput component
 */
export interface SelectInputProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'size'> {
  /** Select label text */
  label?: string;
  /** Error message to display */
  error?: string;
  /** Helper text to display below the select */
  helperText?: string;
  /** Whether the select is required */
  required?: boolean;
  /** Whether the select is disabled */
  disabled?: boolean;
  /** Size variant of the select */
  size?: 'small' | 'medium' | 'large';
  /** Whether to show the select in full width */
  fullWidth?: boolean;
  /** Options for the select */
  options: Array<{ value: string; label: string; disabled?: boolean }>;
  /** Placeholder text */
  placeholder?: string;
}

/**
 * SelectInput component with WCAG AA accessibility compliance
 * 
 * Features:
 * - WCAG AA compliant with proper contrast ratios
 * - Keyboard navigation support
 * - Screen reader friendly with ARIA attributes
 * - Error state handling with proper announcements
 * - Focus management and visual indicators
 * - Consistent styling with JobbLogg design system
 * 
 * @example
 * ```tsx
 * <SelectInput
 *   label="Kundetype"
 *   placeholder="Velg kundetype"
 *   required
 *   options={[
 *     { value: 'privat', label: 'Privatkunde' },
 *     { value: 'firma', label: 'Firmakunde' }
 *   ]}
 *   error={errors.customerType}
 *   helperText="Velg om kunden er privat eller firma"
 * />
 * ```
 */
export const SelectInput = forwardRef<HTMLSelectElement, SelectInputProps>(
  (
    {
      label,
      error,
      helperText,
      required = false,
      disabled = false,
      size = 'medium',
      fullWidth = false,
      options,
      placeholder,
      className = '',
      id,
      'aria-describedby': ariaDescribedBy,
      ...props
    },
    ref
  ) => {
    // Generate unique IDs for accessibility
    const selectId = id || `select-input-${Math.random().toString(36).substr(2, 9)}`;
    const errorId = error ? `${selectId}-error` : undefined;
    const helperTextId = helperText ? `${selectId}-helper` : undefined;
    
    // Build aria-describedby attribute
    const describedBy = [
      ariaDescribedBy,
      errorId,
      helperTextId
    ].filter(Boolean).join(' ') || undefined;

    // Size classes with mobile-optimized touch targets
    const sizeClasses = {
      small: 'h-10 text-sm px-3 touch-target', // Minimum 44px height for mobile
      medium: 'h-11 text-base px-4 touch-target', // Standard touch target
      large: 'h-12 text-lg px-4 touch-target-large' // Large touch target
    };

    // Modern select classes with enhanced flat design and mobile-optimized interactions
    const baseClasses = `
      input-modern focus-ring-enhanced interactive-press mobile-focus
      appearance-none bg-white
      ${error
        ? 'border-jobblogg-error focus:ring-jobblogg-error focus:border-jobblogg-error animate-shake'
        : 'hover:border-jobblogg-primary-light hover:shadow-soft'
      }
      ${sizeClasses[size]}
      ${fullWidth ? 'w-full' : ''}
      pr-10
    `.trim().replace(/\s+/g, ' ');

    return (
      <div className={`${fullWidth ? 'w-full' : ''} ${className}`}>
        {/* Label */}
        {label && (
          <label
            htmlFor={selectId}
            className="block text-sm font-medium text-jobblogg-text-strong mb-2 transition-colors duration-200"
          >
            {label}
            {required && (
              <span className="text-jobblogg-error ml-1" aria-label="obligatorisk">
                *
              </span>
            )}
          </label>
        )}

        {/* Select Container */}
        <div className="relative group">
          {/* Select */}
          <select
            ref={ref}
            id={selectId}
            className={baseClasses}
            disabled={disabled}
            required={required}
            aria-invalid={error ? 'true' : 'false'}
            aria-describedby={describedBy}
            {...props}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option) => (
              <option
                key={option.value}
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </option>
            ))}
          </select>

          {/* Dropdown Arrow */}
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-jobblogg-text-muted pointer-events-none transition-colors duration-200 group-focus-within:text-jobblogg-primary">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>

        {/* Helper Text */}
        {helperText && !error && (
          <p
            id={helperTextId}
            className="mt-2 text-sm text-jobblogg-text-muted transition-colors duration-200"
          >
            {helperText}
          </p>
        )}

        {/* Error Message */}
        {error && (
          <p
            id={errorId}
            className="mt-2 text-sm text-jobblogg-error animate-slide-down"
            role="alert"
            aria-live="polite"
          >
            {error}
          </p>
        )}
      </div>
    );
  }
);

SelectInput.displayName = 'SelectInput';

export default SelectInput;
