import React from 'react';
import { PrimaryButton } from './PrimaryButton';

interface SecondaryButtonProps {
  /** Button content */
  children: React.ReactNode;
  /** Click handler */
  onClick?: () => void;
  /** Button type for forms */
  type?: 'button' | 'submit' | 'reset';
  /** Disabled state */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Loading state */
  loading?: boolean;
  /** Icon to display before text */
  icon?: React.ReactNode;
  /** Button size */
  size?: 'sm' | 'md' | 'lg';
  /** Full width button */
  fullWidth?: boolean;
}

/**
 * Secondary button component - wrapper around PrimaryButton with secondary variant
 */
export const SecondaryButton: React.FC<SecondaryButtonProps> = (props) => {
  return <PrimaryButton {...props} variant="secondary" />;
};
