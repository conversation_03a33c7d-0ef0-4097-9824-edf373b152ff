import React from 'react';

interface TextStrongProps {
  /** Text content */
  children: React.ReactNode;
  /** HTML element to render */
  as?: 'p' | 'span' | 'div' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  /** Additional CSS classes */
  className?: string;
}

/**
 * Strong text component with high contrast for important text and emphasis
 * Uses jobblogg-text-strong color (12.6:1 contrast ratio - WCAG AA compliant)
 *
 * @example
 * ```tsx
 * <TextStrong as="h2">Prosjektoversikt</TextStrong>
 * <TextStrong>Viktig informasjon</TextStrong>
 * <TextStrong as="span" className="text-lg">Fremhevet tekst</TextStrong>
 * ```
 */
export const TextStrong: React.FC<TextStrongProps> = ({
  children,
  as: Component = 'p',
  className = '',
}) => {
  return (
    <Component
      className={`
        text-jobblogg-text-strong font-semibold
        ${className}
      `.trim().replace(/\s+/g, ' ')}
    >
      {children}
    </Component>
  );
};

export default TextStrong;
