import React from 'react';

interface Heading1Props {
  /** Heading content */
  children: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** HTML element to render */
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

/**
 * Heading 1 component for main page titles and hero sections
 * Uses text-heading-1 (40px) with strong contrast for maximum impact
 * 
 * @example
 * ```tsx
 * <Heading1><PERSON><PERSON> prosjekter</Heading1>
 * <Heading1 as="h2">Velkommen til JobbLogg</Heading1>
 * <Heading1 className="text-center">Hovedtittel</Heading1>
 * ```
 */
export const Heading1: React.FC<Heading1Props> = ({
  children,
  className = '',
  as: Component = 'h1',
}) => {
  return (
    <Component
      className={`
        text-heading-1 text-jobblogg-text-strong font-bold leading-tight
        ${className}
      `.trim().replace(/\s+/g, ' ')}
    >
      {children}
    </Component>
  );
};

export default Heading1;
