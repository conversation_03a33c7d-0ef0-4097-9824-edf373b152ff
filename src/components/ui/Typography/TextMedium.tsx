import React from 'react';

interface TextMediumProps {
  /** Text content */
  children: React.ReactNode;
  /** HTML element to render */
  as?: 'p' | 'span' | 'div';
  /** Additional CSS classes */
  className?: string;
}

/**
 * Medium text component for body text and descriptions
 * Uses jobblogg-text-medium color (7.3:1 contrast ratio - WCAG AA compliant)
 * 
 * @example
 * ```tsx
 * <TextMedium>Dette er brødtekst med god lesbarhet</TextMedium>
 * <TextMedium as="span">Inline tekst</TextMedium>
 * <TextMedium className="text-lg"><PERSON><PERSON><PERSON> brødtekst</TextMedium>
 * ```
 */
export const TextMedium: React.FC<TextMediumProps> = ({
  children,
  as: Component = 'p',
  className = '',
}) => {
  return (
    <Component
      className={`
        text-jobblogg-text-medium
        ${className}
      `.trim().replace(/\s+/g, ' ')}
    >
      {children}
    </Component>
  );
};

export default TextMedium;
