import React from 'react';

interface BodyTextProps {
  /** Text content */
  children: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** HTML element to render */
  as?: 'p' | 'span' | 'div';
  /** Text size variant */
  size?: 'body' | 'small' | 'caption';
}

/**
 * Body text component for main content and descriptions
 * Uses text-body (16px) as default with proper line height for readability
 * 
 * @example
 * ```tsx
 * <BodyText>Dette er hovedtekst med god lesbarhet</BodyText>
 * <BodyText size="small">Mindre tekst for detaljer</BodyText>
 * <BodyText size="caption">Bildetekst eller hjelpetekst</BodyText>
 * <BodyText as="span">Inline tekst</BodyText>
 * ```
 */
export const BodyText: React.FC<BodyTextProps> = ({
  children,
  className = '',
  as: Component = 'p',
  size = 'body',
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'text-small text-jobblogg-text-medium';
      case 'caption':
        return 'text-caption text-jobblogg-text-muted';
      default:
        return 'text-body text-jobblogg-text-medium';
    }
  };

  return (
    <Component
      className={`
        ${getSizeClasses()} leading-relaxed
        ${className}
      `.trim().replace(/\s+/g, ' ')}
    >
      {children}
    </Component>
  );
};

export default BodyText;
