# JobbLogg UI Component Library

Gjenbrukbart komponentbibliotek for JobbLogg-applikasjonen med WCAG AA-kompatible komponenter og konsistent design.

## 🎨 Design System

Alle komponenter følger JobbLogg sitt designsystem med:
- **Fargepalett**: jobblogg-prefixede tokens fra tailwind.config.js
- **Typografi**: Inter font med hierarkisk tekstsystem
- **Accessibility**: WCAG AA-kompatible kontrastforhold
- **Animasjoner**: Subtile micro-interactions med hover/focus states
- **Mobile-first**: Responsive design med progressive enhancement

## 📦 Komponenter

### Button Components

#### PrimaryButton
Primærknapp for hovedhandlinger med loading state og ikon-støtte.

```tsx
import { PrimaryButton } from '@/components/ui';

<PrimaryButton onClick={() => console.log('clicked')}>
  Opprett prosjekt
</PrimaryButton>

<PrimaryButton 
  type="submit" 
  loading={isLoading} 
  icon={<PlusIcon />}
>
  Lagre endringer
</PrimaryButton>
```

### Card Components

#### ProjectCard
Prosjektkort med gradient bakgrunn og hover-effekter.

```tsx
import { ProjectCard } from '@/components/ui';

<ProjectCard
  title="Kjøkkenrenovering"
  description="Komplett renovering av kjøkken med nye skap og benkeplate"
  projectId="project-123"
  updatedAt="2025-01-26"
  onClick={() => navigate(`/project/project-123`)}
  animationDelay="0.1s"
/>
```

### Typography Components

#### TextStrong, TextMedium, TextMuted
Tekstkomponenter med WCAG AA-kompatible kontrastforhold.

```tsx
import { TextStrong, TextMedium, TextMuted } from '@/components/ui';

<TextStrong as="h2">Prosjektoversikt</TextStrong>
<TextMedium>Dette er brødtekst med god lesbarhet</TextMedium>
<TextMuted>Sekundær informasjon og metadata</TextMuted>
```

### Layout Components

#### PageLayout
Konsistent sidelayout med header, tilbake-knapp og handlinger.

```tsx
import { PageLayout } from '@/components/ui';

<PageLayout 
  title="Opprett nytt prosjekt" 
  showBackButton 
  backUrl="/"
  headerActions={<UserButton />}
>
  <CreateProjectForm />
</PageLayout>
```

### Empty State Components

#### EmptyState
Tom tilstand med call-to-action for når ingen innhold er tilgjengelig.

```tsx
import { EmptyState } from '@/components/ui';

<EmptyState
  title="Ingen prosjekter ennå"
  description="Opprett ditt første prosjekt for å komme i gang med dokumentering"
  actionLabel="Opprett prosjekt"
  onAction={() => navigate('/create')}
  icon={<DocumentIcon />}
/>
```

## 🚀 Bruk

### Import
```tsx
// Enkeltimport
import { PrimaryButton, ProjectCard, TextStrong } from '@/components/ui';

// Eller spesifikk import
import { PrimaryButton } from '@/components/ui/Button';
import { ProjectCard } from '@/components/ui/Card';
```

### Styling
Alle komponenter støtter `className`-prop for tilpasset styling:

```tsx
<PrimaryButton className="w-full mt-4">
  Full bredde knapp
</PrimaryButton>

<TextStrong className="text-2xl mb-4">
  Stor overskrift
</TextStrong>
```

## ♿ Accessibility

- **Keyboard Navigation**: Alle interaktive komponenter støtter tab-navigasjon
- **ARIA Labels**: Beskrivende labels for skjermlesere
- **Focus States**: Synlige focus-indikatorer
- **Color Contrast**: WCAG AA-kompatible kontrastforhold
- **Semantic HTML**: Riktig bruk av HTML-elementer

## 🎯 Kontrastforhold

- **text-strong**: 16.8:1 (Overskrifter og viktig tekst)
- **text-medium**: 7.3:1 (Brødtekst)
- **text-muted**: 4.9:1 (Sekundær tekst)

## 🔧 Utvidelse

For å legge til nye komponenter:

1. Opprett komponentmappe under `src/components/ui/`
2. Følg eksisterende TypeScript-interface mønster
3. Bruk kun jobblogg-prefixede fargetokens
4. Inkluder JSDoc-kommentarer med eksempler
5. Legg til export i `index.ts`
6. Test accessibility og responsivitet

## 📱 Responsive Design

Alle komponenter er bygget med mobile-first tilnærming:
- Basis styling for mobile (320px+)
- `sm:` breakpoint for små tablets (640px+)
- `md:` breakpoint for tablets (768px+)
- `lg:` breakpoint for desktop (1024px+)
