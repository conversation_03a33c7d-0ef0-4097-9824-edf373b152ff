import React, { useEffect, useState } from 'react';
import { TextStrong, TextMuted } from '../Typography';

interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  imageAlt: string;
  imageTitle?: string;
  imageDescription?: string;
  imageDate?: string;
}

export const ImageModal: React.FC<ImageModalProps> = ({
  isOpen,
  onClose,
  imageUrl,
  imageAlt,
  imageTitle,
  imageDescription,
  imageDate
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Handle escape key and prevent body scroll
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Reset loading state when modal opens
  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      setHasError(false);
    }
  }, [isOpen, imageUrl]);

  if (!isOpen) return null;

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-90 transition-opacity duration-300 animate-fade-in"
        onClick={handleOverlayClick}
        role="button"
        tabIndex={0}
        aria-label="Lukk bildevisning"
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onClose();
          }
        }}
      />
      
      {/* Modal Content */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-6xl">
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 p-3 bg-black bg-opacity-50 text-white hover:bg-opacity-70 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
            aria-label="Lukk bildevisning"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Image Container */}
          <div className="bg-white rounded-xl shadow-2xl overflow-hidden animate-slide-up">
            <div className="relative">
              {/* Loading State */}
              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-jobblogg-neutral min-h-[400px]">
                  <div className="flex flex-col items-center gap-3">
                    <div className="w-8 h-8 border-3 border-jobblogg-primary border-t-transparent rounded-full animate-spin"></div>
                    <TextMuted>Laster bilde...</TextMuted>
                  </div>
                </div>
              )}

              {/* Error State */}
              {hasError && (
                <div className="flex items-center justify-center bg-jobblogg-error-soft min-h-[400px] p-8">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-error rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    <TextStrong className="text-jobblogg-error text-lg mb-2 block">
                      Kunne ikke laste bilde
                    </TextStrong>
                    <TextMuted>
                      Bildet kunne ikke vises. Prøv å laste siden på nytt.
                    </TextMuted>
                  </div>
                </div>
              )}

              {/* Main Image */}
              {!hasError && (
                <img
                  src={imageUrl}
                  alt={imageAlt}
                  className={`w-full h-auto max-h-[80vh] object-contain transition-opacity duration-300 ${
                    isLoading ? 'opacity-0' : 'opacity-100'
                  }`}
                  onLoad={handleImageLoad}
                  onError={handleImageError}
                />
              )}
            </div>

            {/* Image Info */}
            {(imageTitle || imageDescription || imageDate) && (
              <div className="p-6 border-t border-jobblogg-border bg-jobblogg-neutral">
                {imageTitle && (
                  <TextStrong className="text-lg mb-2 block">
                    {imageTitle}
                  </TextStrong>
                )}
                {imageDescription && (
                  <TextMuted className="mb-3">
                    {imageDescription}
                  </TextMuted>
                )}
                {imageDate && (
                  <div className="flex items-center gap-2 text-sm text-jobblogg-text-muted">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span>{imageDate}</span>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Navigation Hint */}
          <div className="text-center mt-4">
            <TextMuted className="text-white text-sm">
              Trykk ESC eller klikk utenfor bildet for å lukke
            </TextMuted>
          </div>
        </div>
      </div>
    </div>
  );
};
