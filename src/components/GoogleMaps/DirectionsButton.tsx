import React from 'react';
import { generateDirectionsUrl, isAddressComplete } from '../../utils/googleMaps';
import { SecondaryButton } from '../ui';

interface DirectionsButtonProps {
  streetAddress: string;
  postalCode: string;
  city: string;
  origin?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'outline';
  disabled?: boolean;
}

/**
 * Directions Button Component
 * Opens Google Maps with driving directions to the specified address
 */
export const DirectionsButton: React.FC<DirectionsButtonProps> = ({
  streetAddress,
  postalCode,
  city,
  origin,
  className = '',
  size = 'md',
  variant = 'secondary',
  disabled = false
}) => {
  const isComplete = isAddressComplete(streetAddress, postalCode, city);
  const isDisabled = disabled || !isComplete;

  const handleClick = () => {
    if (isDisabled) return;

    const directionsUrl = generateDirectionsUrl(streetAddress, postalCode, city, origin);

    // On mobile, try to open in the same window to allow app switching
    // On desktop, open in new tab
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile) {
      // Mobile: Open in same window to allow Google Maps app to take over
      window.location.href = directionsUrl;
    } else {
      // Desktop: Open in new tab
      window.open(directionsUrl, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <SecondaryButton
      onClick={handleClick}
      disabled={isDisabled}
      size={size}
      variant={variant}
      className={`flex items-center gap-2 ${className}`}
      title={isComplete ? 'Åpne veibeskrivelse i Google Maps' : 'Adresse må være komplett for veibeskrivelse'}
    >
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7" />
      </svg>
      Navigasjon
    </SecondaryButton>
  );
};

export default DirectionsButton;
