import React, { useState } from 'react';
import { generateStaticMapUrl, isGoogleMapsConfigured, isAddressComplete, getFallbackMapImage, formatAddress } from '../../utils/googleMaps';
import { TextStrong, TextMuted } from '../ui';
import { DirectionsButton } from './DirectionsButton';

interface ProjectMapCardProps {
  project: {
    _id: string;
    name: string;
    customer?: {
      name: string;
      streetAddress?: string;
      postalCode?: string;
      city?: string;
      entrance?: string;
      // Legacy address field for backward compatibility
      address?: string;
    };
  };
  className?: string;
  width?: number;
  height?: number;
  zoom?: number;
  onClick?: () => void;
}

/**
 * Project Map Card Component
 * Displays a project with Google Maps static image and directions button
 * Replaces the default project image placeholder
 */
export const ProjectMapCard: React.FC<ProjectMapCardProps> = ({
  project,
  className = '',
  width = 300,
  height = 200,
  zoom = 15,
  onClick
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const customer = project.customer;
  if (!customer) {
    return null;
  }

  // Check if we have structured address fields or legacy address
  const hasStructuredAddress = isAddressComplete(
    customer.streetAddress,
    customer.postalCode,
    customer.city
  );
  
  const hasLegacyAddress = Boolean(customer.address && customer.address.trim());
  const isConfigured = isGoogleMapsConfigured();
  const shouldShowMap = isConfigured && (hasStructuredAddress || hasLegacyAddress) && !imageError;

  // Generate map URL based on available address data
  let mapUrl = getFallbackMapImage();
  let displayAddress = 'Ingen adresse';
  let hasValidAddress = false;

  if (hasStructuredAddress && customer.streetAddress && customer.postalCode && customer.city) {
    mapUrl = shouldShowMap
      ? generateStaticMapUrl(customer.streetAddress, customer.postalCode, customer.city, { width, height, zoom })
      : getFallbackMapImage();
    displayAddress = formatAddress(customer.streetAddress, customer.postalCode, customer.city, customer.entrance);
    hasValidAddress = true;
  } else if (hasLegacyAddress && customer.address) {
    // For legacy addresses, we can't generate proper static maps, so show fallback
    displayAddress = customer.address;
    hasValidAddress = false; // Legacy addresses don't work well with DirectionsButton
  }

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  const handleDirectionsClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click
  };

  return (
    <div
      className={`bg-white rounded-xl shadow-medium border border-jobblogg-border overflow-hidden hover-lift transition-all duration-200 ${onClick ? 'cursor-pointer' : ''} ${className}`}
      onClick={onClick}
    >
      {/* Map Image */}
      <div className="relative" style={{ height: `${height}px` }}>
        {isLoading && shouldShowMap && (
          <div className="absolute inset-0 bg-jobblogg-neutral animate-pulse flex items-center justify-center">
            <div className="w-6 h-6 border-2 border-jobblogg-primary border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
        
        <img
          src={mapUrl}
          alt={`Kart over ${displayAddress}`}
          className="w-full h-full object-cover"
          onLoad={handleImageLoad}
          onError={handleImageError}
          style={{ display: (isLoading && shouldShowMap) ? 'none' : 'block' }}
        />

        {/* Fallback/Error State */}
        {(!shouldShowMap || imageError) && (
          <div className="absolute inset-0 bg-jobblogg-neutral flex items-center justify-center">
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-2 bg-jobblogg-text-muted/20 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <TextMuted className="text-xs">
                {!isConfigured ? 'Kart ikke tilgjengelig' : 'Kartbilde'}
              </TextMuted>
            </div>
          </div>
        )}

        {/* Project Name Overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
          <TextStrong className="text-white text-lg font-semibold">
            {project.name}
          </TextStrong>
        </div>
      </div>

      {/* Project Info */}
      <div className="p-4">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 min-w-0">
            <TextStrong className="text-sm font-medium mb-1">
              {customer.name}
            </TextStrong>
            <div className="flex items-start gap-1">
              <svg className="w-3 h-3 text-jobblogg-text-medium mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <TextMuted className="text-xs leading-tight">
                {displayAddress}
              </TextMuted>
            </div>
          </div>

          {/* Directions Button */}
          {hasValidAddress && customer.streetAddress && customer.postalCode && customer.city && (
            <div onClick={handleDirectionsClick}>
              <DirectionsButton
                streetAddress={customer.streetAddress}
                postalCode={customer.postalCode}
                city={customer.city}
                size="sm"
                variant="outline"
                className="flex-shrink-0"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProjectMapCard;
