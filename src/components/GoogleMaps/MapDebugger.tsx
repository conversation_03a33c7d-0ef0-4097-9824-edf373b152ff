import React, { useState, useEffect } from 'react';
import { generateStaticMapUrl, isGoogleMapsConfigured, formatAddressForMaps } from '../../utils/googleMaps';
import { Card, TextStrong, TextMuted, PrimaryButton } from '../ui';

interface MapDebuggerProps {
  streetAddress: string;
  postalCode: string;
  city: string;
}

export const MapDebugger: React.FC<MapDebuggerProps> = ({
  streetAddress,
  postalCode,
  city
}) => {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [imageError, setImageError] = useState<string | null>(null);
  const [imageLoaded, setImageLoaded] = useState(false);

  useEffect(() => {
    // Collect debug information
    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
    const isConfigured = isGoogleMapsConfigured();
    const formattedAddress = formatAddressForMaps(streetAddress, postalCode, city);
    const mapUrl = generateStaticMapUrl(streetAddress, postalCode, city, {
      width: 400,
      height: 200,
      zoom: 15
    });

    setDebugInfo({
      apiKey: apiKey ? `${apiKey.substring(0, 10)}...` : 'NOT SET',
      apiKeyLength: apiKey?.length || 0,
      isConfigured,
      formattedAddress,
      mapUrl,
      environment: import.meta.env.MODE,
      baseUrl: window.location.origin
    });

    console.log('🗺️ Google Maps Debug Info:', {
      apiKey: apiKey ? `${apiKey.substring(0, 10)}...` : 'NOT SET',
      apiKeyLength: apiKey?.length || 0,
      isConfigured,
      formattedAddress,
      mapUrl,
      environment: import.meta.env.MODE,
      baseUrl: window.location.origin
    });
  }, [streetAddress, postalCode, city]);

  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(null);
    console.log('✅ Map image loaded successfully');
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const target = e.target as HTMLImageElement;
    const error = `Failed to load image: ${target.src}`;
    setImageError(error);
    setImageLoaded(false);
    console.error('❌ Map image failed to load:', error);
  };

  const testDirectApiCall = async () => {
    const testUrl = debugInfo.mapUrl;
    console.log('🧪 Testing direct API call:', testUrl);
    
    try {
      const response = await fetch(testUrl);
      console.log('📡 API Response:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        url: response.url
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Error Response:', errorText);
      }
    } catch (error) {
      console.error('❌ Fetch Error:', error instanceof Error ? error.message : String(error));
    }
  };

  return (
    <Card className="p-6 bg-yellow-50 border-yellow-200">
      <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-4">
        🔍 Google Maps Debug Information
      </h3>
      
      <div className="space-y-4">
        {/* API Configuration */}
        <div>
          <TextStrong className="text-sm">API Configuration:</TextStrong>
          <div className="ml-4 text-sm text-jobblogg-text-medium">
            <div>API Key: {debugInfo.apiKey} (Length: {debugInfo.apiKeyLength})</div>
            <div>Configured: {debugInfo.isConfigured ? '✅ Yes' : '❌ No'}</div>
            <div>Environment: {debugInfo.environment}</div>
            <div>Base URL: {debugInfo.baseUrl}</div>
          </div>
        </div>

        {/* Address Information */}
        <div>
          <TextStrong className="text-sm">Address Information:</TextStrong>
          <div className="ml-4 text-sm text-jobblogg-text-medium">
            <div>Input: {streetAddress}, {postalCode} {city}</div>
            <div>Formatted: {debugInfo.formattedAddress}</div>
          </div>
        </div>

        {/* Generated URL */}
        <div>
          <TextStrong className="text-sm">Generated URL:</TextStrong>
          <div className="ml-4">
            <code className="text-xs bg-gray-100 p-2 rounded block break-all">
              {debugInfo.mapUrl}
            </code>
          </div>
        </div>

        {/* Test Image */}
        <div>
          <TextStrong className="text-sm">Image Test:</TextStrong>
          <div className="ml-4 mt-2">
            {debugInfo.mapUrl && (
              <div className="space-y-2">
                <img
                  src={debugInfo.mapUrl}
                  alt="Debug Map Test"
                  className="border border-gray-300 rounded"
                  style={{ width: '300px', height: '150px' }}
                  onLoad={handleImageLoad}
                  onError={handleImageError}
                />
                <div className="text-sm">
                  Status: {imageLoaded ? '✅ Loaded' : imageError ? '❌ Error' : '⏳ Loading...'}
                </div>
                {imageError && (
                  <div className="text-red-600 text-xs">{imageError}</div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Test Button */}
        <div>
          <PrimaryButton onClick={testDirectApiCall} size="sm">
            🧪 Test Direct API Call
          </PrimaryButton>
          <TextMuted className="text-xs mt-1">
            Check browser console for detailed API response
          </TextMuted>
        </div>
      </div>
    </Card>
  );
};

export default MapDebugger;
