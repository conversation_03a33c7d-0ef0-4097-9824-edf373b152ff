import React, { useState } from 'react';
import { generateStaticMapUrl, isGoogleMapsConfigured } from '../../utils/googleMaps';
import { MAP_TYPE_OPTIONS } from '../../utils/mapTypeComparison';
import { Card, TextStrong, TextMuted, PrimaryButton } from '../ui';

interface MapTypeComparisonProps {
  streetAddress: string;
  postalCode: string;
  city: string;
  className?: string;
}

/**
 * Map Type Comparison Component
 * Shows different Google Maps types side by side for comparison
 * Useful for testing and choosing optimal map type for contractors
 */
export const MapTypeComparison: React.FC<MapTypeComparisonProps> = ({
  streetAddress,
  postalCode,
  city,
  className = ''
}) => {
  const [selectedType, setSelectedType] = useState<'roadmap' | 'satellite' | 'hybrid' | 'terrain'>('hybrid');
  const [imageErrors, setImageErrors] = useState<Record<string, boolean>>({});

  const isConfigured = isGoogleMapsConfigured();
  const hasAddress = streetAddress && postalCode && city;

  if (!isConfigured) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="text-center">
          <TextStrong className="text-jobblogg-error mb-2">Google Maps ikke konfigurert</TextStrong>
          <TextMuted>Legg til VITE_GOOGLE_MAPS_API_KEY i .env.local for å teste map-typer</TextMuted>
        </div>
      </Card>
    );
  }

  if (!hasAddress) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="text-center">
          <TextStrong className="text-jobblogg-text-muted mb-2">Ingen adresse</TextStrong>
          <TextMuted>Fyll ut adresse for å sammenligne map-typer</TextMuted>
        </div>
      </Card>
    );
  }

  const handleImageError = (mapType: string) => {
    setImageErrors(prev => ({ ...prev, [mapType]: true }));
  };

  const handleImageLoad = (mapType: string) => {
    setImageErrors(prev => ({ ...prev, [mapType]: false }));
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card className="p-6">
        <TextStrong as="h3" className="text-xl mb-2">
          Google Maps Type Sammenligning
        </TextStrong>
        <TextMuted className="mb-4">
          Sammenlign ulike map-typer for {streetAddress}, {postalCode} {city}
        </TextMuted>
        
        {/* Map Type Selector */}
        <div className="flex flex-wrap gap-2">
          {MAP_TYPE_OPTIONS.map((option) => (
            <button
              key={option.type}
              onClick={() => setSelectedType(option.type)}
              className={`
                px-4 py-2 rounded-lg text-sm font-medium transition-colors
                ${selectedType === option.type
                  ? 'bg-jobblogg-primary text-white'
                  : 'bg-jobblogg-background-soft text-jobblogg-text-medium hover:bg-jobblogg-primary-soft'
                }
              `}
            >
              {option.name}
            </button>
          ))}
        </div>
      </Card>

      {/* Selected Map Type Details */}
      {(() => {
        const selectedOption = MAP_TYPE_OPTIONS.find(opt => opt.type === selectedType);
        if (!selectedOption) return null;

        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Map Image */}
            <Card className="p-4">
              <TextStrong className="mb-4">{selectedOption.name}</TextStrong>
              <div className="relative">
                <img
                  src={generateStaticMapUrl(streetAddress, postalCode, city, {
                    width: 400,
                    height: 300,
                    zoom: selectedOption.recommendedZoom[0],
                    mapType: selectedOption.type
                  })}
                  alt={`${selectedOption.name} av ${streetAddress}`}
                  className="w-full h-auto rounded-lg border border-jobblogg-border"
                  onError={() => handleImageError(selectedOption.type)}
                  onLoad={() => handleImageLoad(selectedOption.type)}
                />
                {imageErrors[selectedOption.type] && (
                  <div className="absolute inset-0 flex items-center justify-center bg-jobblogg-background-soft rounded-lg">
                    <TextMuted>Kunne ikke laste kart</TextMuted>
                  </div>
                )}
              </div>
            </Card>

            {/* Map Type Information */}
            <Card className="p-4">
              <div className="space-y-4">
                <div>
                  <TextStrong className="text-sm text-jobblogg-text-strong mb-2">Beskrivelse</TextStrong>
                  <TextMuted className="text-sm">{selectedOption.description}</TextMuted>
                </div>

                <div>
                  <TextStrong className="text-sm text-jobblogg-success mb-2">✅ Fordeler for kontraktører</TextStrong>
                  <ul className="text-sm text-jobblogg-text-medium space-y-1">
                    {selectedOption.contractorBenefits.map((benefit, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-jobblogg-success mt-0.5">•</span>
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <TextStrong className="text-sm text-jobblogg-warning mb-2">⚠️ Ulemper</TextStrong>
                  <ul className="text-sm text-jobblogg-text-medium space-y-1">
                    {selectedOption.drawbacks.map((drawback, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-jobblogg-warning mt-0.5">•</span>
                        {drawback}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <TextStrong className="text-sm text-jobblogg-primary mb-2">🎯 Beste bruksområder</TextStrong>
                  <ul className="text-sm text-jobblogg-text-medium space-y-1">
                    {selectedOption.bestUseCases.map((useCase, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-jobblogg-primary mt-0.5">•</span>
                        {useCase}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <TextStrong className="text-sm text-jobblogg-text-strong mb-2">Anbefalt zoom</TextStrong>
                  <TextMuted className="text-sm">
                    {selectedOption.recommendedZoom[0]} - {selectedOption.recommendedZoom[1]}
                  </TextMuted>
                </div>
              </div>
            </Card>
          </div>
        );
      })()}

      {/* Recommendation */}
      <Card className="p-6 bg-jobblogg-success-soft border-jobblogg-success/20">
        <div className="flex items-start gap-3">
          <div className="w-6 h-6 text-jobblogg-success flex-shrink-0 mt-0.5">
            <svg fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <TextStrong className="text-jobblogg-success mb-2">
              Anbefaling for JobbLogg: HYBRID
            </TextStrong>
            <TextMuted className="text-sm">
              Hybrid-visning gir kontraktører det beste av begge verdener - satellittbilder for å identifisere bygninger 
              og strukturer, kombinert med gatenavn og veietiketter for enkel navigasjon. Dette er ideelt for å:
            </TextMuted>
            <ul className="text-sm text-jobblogg-text-medium mt-2 space-y-1">
              <li>• Identifisere bygningstype før ankomst</li>
              <li>• Finne riktig inngang og parkeringsmuligheter</li>
              <li>• Vurdere tilgjengelighet for utstyr og materialer</li>
              <li>• Navigere effektivt til jobbstedet</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};
