import React, { useState } from 'react';
import { usePWA, PWAUtils } from '../hooks/usePWA';
import { PrimaryButton } from './ui';

interface PWAInstallBannerProps {
  className?: string;
}

export const PWAInstallBanner: React.FC<PWAInstallBannerProps> = ({
  className = ''
}) => {
  const { isInstallable, isInstalled, installApp } = usePWA();
  const [isDismissed, setIsDismissed] = useState(() => {
    // Check if user has previously dismissed the banner
    try {
      return localStorage.getItem('pwa-install-dismissed') === 'true';
    } catch (error) {
      // Handle cases where localStorage is not available (e.g., private browsing)
      console.warn('localStorage not available:', error);
      return false;
    }
  });
  const [isInstalling, setIsInstalling] = useState(false);

  // Don't show banner if app is already installed, not installable, or dismissed
  if (isInstalled || !isInstallable || isDismissed) {
    return null;
  }

  const handleInstall = async () => {
    setIsInstalling(true);
    try {
      const success = await installApp();
      if (success) {
        setIsDismissed(true);
      }
    } catch (error) {
      console.error('Install failed:', error instanceof Error ? error.message : String(error));
    } finally {
      setIsInstalling(false);
    }
  };

  const handleDismiss = () => {
    setIsDismissed(true);
    // Store dismissal in localStorage to persist across sessions
    try {
      localStorage.setItem('pwa-install-dismissed', 'true');
    } catch (error) {
      // Handle cases where localStorage is not available (e.g., private browsing)
      console.warn('Could not save dismissal state to localStorage:', error instanceof Error ? error.message : String(error));
    }
  };

  return (
    <div className={`
      fixed bottom-4 left-4 right-4 z-50 
      bg-white border border-jobblogg-primary/20 rounded-xl shadow-lg 
      p-4 animate-slide-up
      md:left-auto md:right-4 md:max-w-sm
      ${className}
    `}>
      <div className="flex items-start gap-3">
        {/* App Icon */}
        <div className="flex-shrink-0 w-12 h-12 bg-jobblogg-primary rounded-xl flex items-center justify-center">
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-jobblogg-text-strong text-sm mb-1">
            Installer JobbLogg
          </h3>
          <p className="text-jobblogg-text-medium text-xs mb-3 leading-relaxed">
            Få rask tilgang til prosjektene dine direkte fra hjemskjermen. 
            Fungerer offline og gir deg en bedre opplevelse.
          </p>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <PrimaryButton
              onClick={handleInstall}
              loading={isInstalling}
              size="sm"
              className="text-xs px-3 py-1.5"
            >
              {isInstalling ? 'Installerer...' : 'Installer'}
            </PrimaryButton>
            
            <button
              onClick={handleDismiss}
              className="text-jobblogg-text-muted hover:text-jobblogg-text-medium text-xs px-2 py-1.5 rounded-md transition-colors"
            >
              Ikke nå
            </button>
          </div>
        </div>

        {/* Close Button */}
        <button
          onClick={handleDismiss}
          className="flex-shrink-0 w-6 h-6 text-jobblogg-text-muted hover:text-jobblogg-text-medium transition-colors rounded-full hover:bg-jobblogg-neutral flex items-center justify-center"
          aria-label="Lukk installasjonsbanner"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Install Instructions (for browsers that don't support beforeinstallprompt) */}
      <div className="mt-3 pt-3 border-t border-jobblogg-neutral">
        <p className="text-jobblogg-text-muted text-xs">
          💡 {PWAUtils.getInstallInstructions()}
        </p>
      </div>
    </div>
  );
};

// PWA Status Indicator Component
export const PWAStatusIndicator: React.FC = () => {
  const { isOnline, isUpdateAvailable, updateApp } = usePWA();
  const [isUpdating, setIsUpdating] = useState(false);

  const handleUpdate = async () => {
    setIsUpdating(true);
    try {
      await updateApp();
    } catch (error) {
      console.error('Update failed:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="fixed top-4 right-4 z-40 flex flex-col gap-2">
      {/* Online/Offline Status */}
      <div className={`
        px-3 py-1.5 rounded-full text-xs font-medium flex items-center gap-2 transition-all duration-300
        ${isOnline 
          ? 'bg-jobblogg-success-soft text-jobblogg-success border border-jobblogg-success/20' 
          : 'bg-jobblogg-warning-soft text-jobblogg-warning border border-jobblogg-warning/20'
        }
      `}>
        <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-jobblogg-success' : 'bg-jobblogg-warning'}`} />
        {isOnline ? 'Online' : 'Offline'}
      </div>

      {/* Update Available */}
      {isUpdateAvailable && (
        <div className="bg-jobblogg-primary-soft border border-jobblogg-primary/20 rounded-lg p-3 max-w-xs animate-slide-down">
          <div className="flex items-start gap-2">
            <div className="w-5 h-5 bg-jobblogg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-jobblogg-text-strong text-sm font-medium mb-1">
                Oppdatering tilgjengelig
              </p>
              <p className="text-jobblogg-text-medium text-xs mb-2">
                En ny versjon av JobbLogg er klar til installasjon.
              </p>
              <PrimaryButton
                onClick={handleUpdate}
                loading={isUpdating}
                size="sm"
                className="text-xs"
              >
                {isUpdating ? 'Oppdaterer...' : 'Oppdater nå'}
              </PrimaryButton>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// PWA Share Button Component
interface PWAShareButtonProps {
  title: string;
  text: string;
  url?: string;
  className?: string;
  children?: React.ReactNode;
}

export const PWAShareButton: React.FC<PWAShareButtonProps> = ({
  title,
  text,
  url,
  className = '',
  children
}) => {
  const [isSharing, setIsSharing] = useState(false);
  const [shareSuccess, setShareSuccess] = useState(false);

  const handleShare = async () => {
    setIsSharing(true);
    try {
      const success = await PWAUtils.shareContent({ title, text, url });
      if (success) {
        setShareSuccess(true);
        setTimeout(() => setShareSuccess(false), 2000);
      }
    } catch (error) {
      console.error('Share failed:', error);
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <PrimaryButton
      onClick={handleShare}
      loading={isSharing}
      variant="outline"
      size="sm"
      className={`${shareSuccess ? 'bg-jobblogg-success-soft border-jobblogg-success text-jobblogg-success' : ''} ${className}`}
    >
      {shareSuccess ? (
        <>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          Delt!
        </>
      ) : (
        <>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
          </svg>
          {children || 'Del'}
        </>
      )}
    </PrimaryButton>
  );
};
