import React from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { TextMuted, TextMedium } from '../ui';

interface ContractorLikeIndicatorProps {
  logEntryId: string;
  projectId: string;
  userId: string;
  showDetails?: boolean;
  className?: string;
}

export const ContractorLikeIndicator: React.FC<ContractorLikeIndicatorProps> = ({
  logEntryId,
  projectId,
  userId,
  showDetails = false,
  className = ''
}) => {
  // Get like count and details for this image
  const likeData = useQuery(
    api.imageLikes.getLikeCount,
    { logEntryId: logEntryId as any }
  );

  if (!likeData || likeData.count === 0) {
    return null; // Don't show anything if no likes
  }

  const { count, likes } = likeData;

  const formatDate = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return `${minutes} min siden`;
    } else if (hours < 24) {
      return `${hours} timer siden`;
    } else if (days < 7) {
      return `${days} dager siden`;
    } else {
      return new Date(timestamp).toLocaleDateString('nb-NO', {
        day: 'numeric',
        month: 'short'
      });
    }
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Heart Icon with Count */}
      <div className="flex items-center gap-1 bg-jobblogg-error-soft text-jobblogg-error px-2 py-1 rounded-full">
        <svg className="w-4 h-4 fill-current" viewBox="0 0 24 24">
          <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
        <TextMedium className="text-sm font-medium">
          {count}
        </TextMedium>
      </div>

      {/* Details */}
      {showDetails && likes.length > 0 && (
        <div className="flex flex-col gap-1">
          {likes.slice(0, 3).map((like, index) => (
            <div key={index} className="flex items-center gap-2">
              <TextMuted className="text-xs">
                {like.customerName || 'Anonym kunde'}
              </TextMuted>
              <TextMuted className="text-xs">
                {formatDate(like.createdAt)}
              </TextMuted>
            </div>
          ))}
          {likes.length > 3 && (
            <TextMuted className="text-xs">
              +{likes.length - 3} flere
            </TextMuted>
          )}
        </div>
      )}
    </div>
  );
};

// Component for showing like statistics in project overview
interface ProjectLikeStatsProps {
  projectId: string;
  userId: string;
  className?: string;
}

export const ProjectLikeStats: React.FC<ProjectLikeStatsProps> = ({
  projectId,
  userId,
  className = ''
}) => {
  const likeStats = useQuery(
    api.imageLikes.getProjectLikeStats,
    { projectId: projectId as any, userId }
  );

  if (!likeStats || likeStats.totalLikes === 0) {
    return null;
  }

  const formatDate = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (hours < 24) {
      return `${hours} timer siden`;
    } else if (days < 7) {
      return `${days} dager siden`;
    } else {
      return new Date(timestamp).toLocaleDateString('nb-NO', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      });
    }
  };

  return (
    <div className={`bg-jobblogg-error-soft border border-jobblogg-error-soft rounded-xl p-4 ${className}`}>
      <div className="flex items-center gap-2 mb-3">
        <svg className="w-5 h-5 text-jobblogg-error fill-current" viewBox="0 0 24 24">
          <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
        <TextMedium className="font-semibold text-jobblogg-error">
          Kundelikes
        </TextMedium>
      </div>

      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <TextMuted className="text-sm">Totalt antall likes:</TextMuted>
          <TextMedium className="font-medium">{likeStats.totalLikes}</TextMedium>
        </div>

        <div className="flex justify-between items-center">
          <TextMuted className="text-sm">Unike kunder:</TextMuted>
          <TextMedium className="font-medium">{likeStats.uniqueCustomers}</TextMedium>
        </div>

        {likeStats.mostRecentLike && (
          <div className="flex justify-between items-center">
            <TextMuted className="text-sm">Siste like:</TextMuted>
            <TextMuted className="text-sm">{formatDate(likeStats.mostRecentLike)}</TextMuted>
          </div>
        )}

        {likeStats.mostLikedCount > 1 && (
          <div className="flex justify-between items-center">
            <TextMuted className="text-sm">Mest likte bilde:</TextMuted>
            <TextMedium className="font-medium">{likeStats.mostLikedCount} likes</TextMedium>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContractorLikeIndicator;
