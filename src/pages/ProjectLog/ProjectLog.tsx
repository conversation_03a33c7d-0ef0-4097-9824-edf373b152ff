import React, { useState, useRef, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, TextStrong, TextMedium, TextMuted, PrimaryButton, TextArea, FormError, SubmitButton, ImageModal } from '../../components/ui';
import { EditHistoryModal } from '../../components/EditHistoryModal';
import { ContractorLikeIndicator } from '../../components/ContractorLikeIndicator';
import { EmbeddedChatContainer } from '../../components/chat';



const ProjectLog: React.FC = () => {
  const { projectId, logId } = useParams<{ projectId: string; logId?: string }>();
  const { user } = useUser();
  const navigate = useNavigate();

  // Remove chat mode detection - now using embedded chat
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<string>('');
  const [isDragOver, setIsDragOver] = useState(false);
  const [description, setDescription] = useState('');
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [showEditHistory, setShowEditHistory] = useState<string | null>(null);


  // Image modal state
  const [selectedImageModal, setSelectedImageModal] = useState<{
    url: string;
    alt: string;
    title?: string;
    description?: string;
    date?: string;
  } | null>(null);

  // Image modal handlers
  const openImageModal = (imageData: {
    url: string;
    alt: string;
    title?: string;
    description?: string;
    date?: string;
  }) => {
    setSelectedImageModal(imageData);
  };

  const closeImageModal = () => {
    setSelectedImageModal(null);
  };

  // Fetch project details (for now we'll use a hardcoded approach since we don't have project fetching by ID yet)
  const projects = useQuery(api.projects.getByUser, { userId: user?.id || "" });
  const project = projects?.find(p => p._id === projectId);

  // Fetch log entries for this project
  const logEntries = useQuery(
    api.logEntries.getByProject,
    projectId && user?.id && project ? { projectId: projectId as any, userId: user.id } : "skip"
  );

  // Remove specific log entry query - no longer needed for embedded chat

  // Fetch unread message counts for chat buttons
  const unreadCounts = useQuery(
    api.messages.getUnreadCounts,
    user?.id ? { userId: user.id, userRole: "contractor" } : "skip"
  );





  // Handle navigation away from deleted project
  useEffect(() => {
    // If projects query has loaded but project doesn't exist
    if (projects && projectId && user?.id && !project) {
      // Project was likely deleted, navigate to dashboard
      navigate('/', { replace: true });
    }
  }, [projects, project, projectId, user?.id, navigate]);

  // Convex mutations
  const generateUploadUrl = useMutation(api.logEntries.generateUploadUrl);
  const createLogEntry = useMutation(api.logEntries.create);

  const validateAndSetImage = (file: File) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      alert('Kun .jpg, .jpeg, .png og .webp filer er tillatt');
      return false;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('Bildet kan ikke være større enn 10MB');
      return false;
    }

    setSelectedImage(file);

    // Create preview URL
    const previewUrl = URL.createObjectURL(file);
    setImagePreview(previewUrl);
    return true;
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      validateAndSetImage(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      validateAndSetImage(files[0]);
    }
  };

  const removeImage = () => {
    if (imagePreview) {
      URL.revokeObjectURL(imagePreview);
    }
    setSelectedImage(null);
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!description || description.trim().length < 5) {
      newErrors.description = 'Beskrivelse må være minst 5 tegn lang';
    }

    if (description && description.length > 1000) {
      newErrors.description = 'Beskrivelse kan ikke være lengre enn 1000 tegn';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setUploadProgress('');
    setErrors({});

    try {
      // Validate form
      if (!validateForm()) {
        setIsLoading(false);
        return;
      }

      if (!user?.id || !projectId) {
        throw new Error('User not authenticated or project ID missing');
      }

      let imageId: string | undefined = undefined;

      // Upload image if one is selected
      if (selectedImage) {
        setUploadProgress('Laster opp bilde...');

        // Get upload URL from Convex
        const uploadUrl = await generateUploadUrl();

        // Upload the file to Convex storage
        const result = await fetch(uploadUrl, {
          method: 'POST',
          headers: { 'Content-Type': selectedImage.type },
          body: selectedImage,
        });

        if (!result.ok) {
          throw new Error('Failed to upload image');
        }

        const { storageId } = await result.json();
        imageId = storageId;
        setUploadProgress('Lagrer logg...');
      } else {
        setUploadProgress('Lagrer logg...');
      }

      // Create the log entry in the database
      await createLogEntry({
        projectId: projectId as any, // Type assertion needed for Convex ID
        userId: user.id,
        description,
        imageId: imageId as any // Type assertion needed for Convex ID
      });

      // Reset form
      setDescription('');
      setSelectedImage(null);
      if (imagePreview) {
        URL.revokeObjectURL(imagePreview);
      }
      setImagePreview(null);
      setUploadProgress('');

      // Show success message
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);

    } catch (error) {
      console.error('Error creating log entry:', error instanceof Error ? error.message : String(error));
      setErrors({ general: 'Det oppstod en feil ved lagring av loggen. Prøv igjen.' });
      setUploadProgress('');
      setTimeout(() => setUploadProgress(''), 3000);
    } finally {
      setIsLoading(false);
    }
  };

  // Edit history handlers
  const handleShowEditHistory = (entryId: string) => {
    setShowEditHistory(entryId);
  };

  const handleCloseEditHistory = () => {
    setShowEditHistory(null);
  };



  // Loading state with modern skeleton
  if (projects === undefined) {
    return (
      <div className="min-h-screen bg-jobblogg-neutral">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          {/* Header Skeleton */}
          <div className="flex items-center gap-4 mb-8">
            <div className="skeleton h-10 w-10 rounded-full"></div>
            <div className="skeleton h-8 w-64"></div>
          </div>

          {/* Form Skeleton */}
          <div className="bg-white rounded-xl p-8 shadow-lg border border-jobblogg-border">
            <div className="skeleton h-48 w-full rounded-xl mb-6"></div>
            <div className="skeleton h-20 w-full rounded-lg mb-6"></div>
            <div className="skeleton h-12 w-32 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  // Project not found with modern error state
  if (!project) {
    return (
      <div className="min-h-screen bg-jobblogg-neutral animate-fade-in">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="flex items-center gap-4 mb-8">
            <PrimaryButton
              onClick={() => navigate('/')}
              variant="ghost"
              size="sm"
              aria-label="Tilbake til oversikt"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </PrimaryButton>
          </div>

          <div className="bg-jobblogg-error-soft border border-jobblogg-error rounded-xl p-8 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-error-soft rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <TextStrong as="h2" className="text-2xl text-jobblogg-error mb-2">Prosjekt ikke funnet</TextStrong>
            <TextMedium className="text-jobblogg-error/80 mb-6">Dette prosjektet eksisterer ikke eller du har ikke tilgang til det.</TextMedium>
            <PrimaryButton onClick={() => navigate('/')}>
              Tilbake til oversikt
            </PrimaryButton>
          </div>
        </div>
      </div>
    );
  }

  // Remove chat mode rendering - now using embedded chat

  return (
    <PageLayout
      title={project.name}
      showBackButton
      backUrl="/"
      containerWidth="wide"
      headerActions={
        <div className="flex items-center gap-3">
          <TextMuted className="text-lg">
            📸 Legg til nytt bilde og beskrivelse
          </TextMuted>
          <PrimaryButton onClick={() => navigate(`/project/${projectId}/details`)}>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Kontraktørpanel
          </PrimaryButton>
        </div>
      }
    >
        {/* Modern Success Alert */}
        {showSuccess && (
          <div className="bg-jobblogg-accent-soft border border-jobblogg-accent rounded-xl p-6 mb-8 animate-scale-in">
            <div className="flex items-start gap-4">
              <div className="p-2 bg-jobblogg-accent-soft rounded-full flex-shrink-0">
                <svg
                  className="w-6 h-6 text-jobblogg-accent"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div className="flex-1">
                <TextStrong as="h3" className="text-jobblogg-accent mb-2">🎉 Logg lagret!</TextStrong>
                <TextMedium className="text-jobblogg-accent/80 text-sm mb-4">Bildet og beskrivelsen er lagret i prosjektet ditt.</TextMedium>
                <div className="flex flex-wrap gap-2">
                  <PrimaryButton
                    onClick={() => navigate(`/project/${projectId}/details`)}
                    size="sm"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    Se prosjektdetaljer
                  </PrimaryButton>
                  <PrimaryButton
                    onClick={() => navigate('/')}
                    variant="outline"
                    size="sm"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
                    </svg>
                    Tilbake til oversikt
                  </PrimaryButton>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Modern Upload Progress Alert */}
        {uploadProgress && (
          <div className="bg-jobblogg-primary-soft border border-jobblogg-border rounded-xl p-6 mb-8 animate-scale-in">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-jobblogg-primary-soft rounded-full">
                <svg
                  className="w-6 h-6 text-info animate-spin"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
              </div>
              <div>
                <TextStrong as="h3" className="text-info">Arbeider...</TextStrong>
                <TextMedium className="text-info/80 text-sm">{uploadProgress}</TextMedium>
              </div>
            </div>
          </div>
        )}

        {/* Modern Project Log Form */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg border border-jobblogg-border p-8 animate-slide-up">
            <div className="mb-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </div>
              <TextStrong as="h2" className="text-2xl mb-2">📝 Ny loggføring</TextStrong>
              <TextMedium>Dokumenter fremgangen med bilder og beskrivelser</TextMedium>
            </div>

            {project?.isArchived ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-jobblogg-text-muted rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <TextStrong className="text-xl mb-2 block text-jobblogg-text-muted">Prosjekt arkivert</TextStrong>
                <TextMedium className="text-jobblogg-text-muted">
                  Dette prosjektet er arkivert og kan ikke endres. Du kan fortsatt se eksisterende loggføringer.
                </TextMedium>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-8">
              {/* General Form Error */}
              {errors.general && (
                <FormError error={errors.general} />
              )}

              {/* Modern Drag & Drop Image Upload */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <TextMedium className="font-semibold">Bilde</TextMedium>
                  <TextMuted className="text-sm">(valgfritt)</TextMuted>
                </div>

                {!imagePreview ? (
                  <div
                    className={`border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 cursor-pointer ${
                      isDragOver
                        ? 'border-jobblogg-primary bg-jobblogg-primary-soft scale-105'
                        : 'border-jobblogg-border hover:border-jobblogg-primary hover:bg-jobblogg-neutral'
                    }`}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <TextStrong as="h3" className="text-lg mb-2">
                      {isDragOver ? '📁 Slipp bildet her!' : '📸 Last opp bilde'}
                    </TextStrong>
                    <TextMedium className="mb-4">
                      Dra og slipp et bilde her, eller klikk for å velge
                    </TextMedium>
                    <div className="flex items-center justify-center gap-4 text-caption">
                      <span>JPG</span>
                      <span>•</span>
                      <span>PNG</span>
                      <span>•</span>
                      <span>WebP</span>
                      <span>•</span>
                      <span>Maks 10MB</span>
                    </div>
                  </div>
                ) : (
                  <div className="relative rounded-xl overflow-hidden bg-jobblogg-neutral animate-scale-in">
                    <img
                      src={imagePreview}
                      alt="Forhåndsvisning"
                      className="w-full h-64 object-cover"
                    />
                    <div className="absolute inset-0 bg-jobblogg-text-strong bg-opacity-20 opacity-0 hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                      <PrimaryButton
                        onClick={removeImage}
                        variant="danger"
                        size="sm"
                        className="shadow-lg rounded-full w-10 h-10 p-0"
                        aria-label="Fjern bilde"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </PrimaryButton>
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-jobblogg-text-strong to-transparent p-4">
                      <p className="text-white text-sm font-medium">
                        ✅ {selectedImage?.name}
                      </p>
                      <p className="text-jobblogg-neutral-light text-xs">
                        {selectedImage && (selectedImage.size / 1024 / 1024).toFixed(1)} MB
                      </p>
                    </div>
                  </div>
                )}

                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".jpg,.jpeg,.png,.webp"
                  onChange={handleImageChange}
                  className="hidden"
                />
              </div>

              {/* Description Field */}
              <TextArea
                label="Beskrivelse *"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Beskriv hva som ble gjort i dag, utfordringer, fremgang, eller andre viktige detaljer..."
                rows={4}
                maxLength={1000}
                showCharacterCount
                error={errors.description}
                helperText="💡 Tips: Detaljerte beskrivelser hjelper deg å følge fremgangen over tid"
                required
              />



              {/* Modern Form Actions */}
              <div className="mt-12">
                <SubmitButton
                  loading={isLoading}
                  loadingText={uploadProgress || 'Lagrer logg...'}
                  className="shadow-lg hover:shadow-xl group mb-4"
                >
                  <svg className="w-5 h-5 group-hover:rotate-90 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Lagre logg
                </SubmitButton>

                <div className="flex justify-center">
                  <PrimaryButton
                    onClick={() => navigate(`/project/${projectId}/details`)}
                    variant="ghost"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    Se alle logger i prosjektet
                  </PrimaryButton>
                </div>
              </div>
          </form>
            )}
        </div>
      </div>

        {/* Modern Existing Log Entries */}
        {logEntries && logEntries.length > 0 && (
          <div className="mt-12 max-w-2xl mx-auto">
            <div className="bg-white rounded-xl shadow-lg p-8">
              <div className="flex items-center gap-3 mb-8">
                <div className="w-10 h-10 bg-jobblogg-accent-soft rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <TextStrong as="h2" className="text-2xl">📋 Tidligere logger</TextStrong>
                  <TextMuted className="text-sm">{logEntries.length} oppføringer</TextMuted>
                </div>
              </div>

              <div className="space-y-6">
                {logEntries.map((entry, index) => (
                  <div
                    key={entry._id}
                    className="bg-jobblogg-neutral rounded-xl p-6 card-hover animate-slide-up"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="flex flex-col gap-4">
                      <div className="flex justify-between items-start gap-4">
                        <div className="flex-1">
                          <div className="flex items-start gap-2 mb-2">
                            <TextMedium className="leading-relaxed flex-1">
                              {entry.entryType === 'system' && entry.description === 'Prosjekt startet'
                                ? 'Prosjekt registrert'
                                : entry.description}
                            </TextMedium>
                            {entry.isEdited && (
                              <span className="px-2 py-1 bg-jobblogg-primary-soft text-jobblogg-primary text-xs font-medium rounded-full flex-shrink-0">
                                Redigert
                              </span>
                            )}
                          </div>


                          {entry.isEdited && (
                            <button
                              onClick={() => handleShowEditHistory(entry._id)}
                              className="text-jobblogg-primary hover:text-jobblogg-primary-dark text-sm font-medium transition-colors duration-200 flex items-center gap-1"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              Se endringshistorikk
                            </button>
                          )}
                        </div>
                        <div className="flex items-center gap-2 text-body-small flex-shrink-0">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          <span>
                            {new Date(entry.createdAt).toLocaleDateString('nb-NO', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                        </div>
                      </div>
                      {entry.imageUrl && (
                        <div className="mt-4">
                          <button
                            onClick={() => openImageModal({
                              url: entry.imageUrl!,
                              alt: "Prosjektbilde",
                              title: entry.description || "Prosjektbilde",
                              description: entry.description,
                              date: new Date(entry.createdAt).toLocaleDateString('nb-NO', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })
                            })}
                            className="relative block w-full text-left focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-opacity-50 rounded-xl group"
                            aria-label="Klikk for å se bildet i full størrelse"
                          >
                            <img
                              src={entry.imageUrl}
                              alt="Prosjektbilde"
                              className="rounded-xl w-full h-auto max-h-96 object-cover shadow-md hover:shadow-lg transition-all duration-200 group-hover:scale-[1.02] cursor-pointer"
                            />
                            {/* Click indicator overlay */}
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-100">
                              <div className="bg-white bg-opacity-90 rounded-full p-3 shadow-lg">
                                <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                                </svg>
                              </div>
                            </div>
                          </button>

                          {/* Customer Engagement Section - Like indicator below image */}
                          {user && (
                            <div className="mt-3">
                              <ContractorLikeIndicator
                                logEntryId={entry._id}
                                projectId={project._id}
                                userId={user.id}
                                showDetails={false}
                                className="justify-start"
                              />
                            </div>
                          )}
                        </div>
                      )}

                      {/* Embedded Chat for Contractor */}
                      <EmbeddedChatContainer
                        logId={entry._id}
                        userId={user?.id || ""}
                        userRole="contractor"
                        headerText="Diskuter med kunde"
                        maxHeight="250px"
                      />

                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}



        {/* Empty State for No Logs */}
        {logEntries && logEntries.length === 0 && (
          <div className="mt-12 max-w-2xl mx-auto">
            <div className="bg-jobblogg-neutral rounded-xl p-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-neutral-secondary rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <TextStrong as="h3" className="text-lg mb-2">Ingen logger ennå</TextStrong>
              <TextMuted>
                Dette prosjektet har ingen logger. Legg til den første loggen ovenfor! 📝
              </TextMuted>
            </div>
          </div>
        )}

        {/* Edit History Modal */}
        {showEditHistory && (
          <EditHistoryModal
            entryId={showEditHistory}
            isOpen={true}
            onClose={handleCloseEditHistory}
          />
        )}

        {/* Image Modal */}
        {selectedImageModal && (
          <ImageModal
            isOpen={!!selectedImageModal}
            onClose={closeImageModal}
            imageUrl={selectedImageModal.url}
            imageAlt={selectedImageModal.alt}
            imageTitle={selectedImageModal.title}
            imageDescription={selectedImageModal.description}
            imageDate={selectedImageModal.date}
          />
        )}
    </PageLayout>
  );
};

export default ProjectLog;
