import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useQuery, useMutation } from 'convex/react';
import { useUser, UserButton } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { ProjectCard, Heading2, EmptyState, DashboardLayout, StatsCard, BodyText, TextMuted, ArchiveStatusBadge, ArchiveActions, ProjectMapCard, isAddressComplete } from '../../components/ui';
import { ChatStatsCard } from '../../components/chat';

// Draft project interface for old CreateProject form
interface DraftProject {
  formData: {
    projectName: string;
    description: string;
    customerName: string;
    customerType: 'privat' | 'firma';
    contactPerson: string;
    phone: string;
    email: string;
    address: string;
    orgNumber: string;
    notes: string;
  };
  useExistingCustomer: boolean;
  selectedCustomerId: string;
  timestamp: number;
}

// Draft project interface for new CreateProjectWizard
interface WizardDraftProject {
  formData: {
    projectName: string;
    description: string;
    customerName: string;
    customerType: 'privat' | 'firma';
    contactPerson: string;
    phone: string;
    email: string;
    address: string;
    orgNumber: string;
    notes: string;
    jobDescription: string;
    accessNotes: string;
    equipmentNeeds: string;
    unresolvedQuestions: string;
    personalNotes: string;
  };
  currentStep: number;
  useExistingCustomer: boolean;
  selectedCustomerId: string;
  createdProjectId: string | null;
  timestamp: number;
}

const STORAGE_KEY = 'jobblogg-create-project-form';
const WIZARD_STORAGE_KEY = 'jobblogg-create-project-wizard';
const DRAFT_EXPIRY_DAYS = 7;



const Dashboard: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();

  // Archive filtering state
  const [showArchived, setShowArchived] = useState(false);

  // Query active or archived projects based on filter - skip if user not loaded
  const projects = useQuery(
    api.projects.getByUserWithCustomers,
    user?.id ? { userId: user.id } : "skip"
  );
  const archivedProjects = useQuery(
    api.projects.getArchivedByUserWithCustomers,
    user?.id ? { userId: user.id } : "skip"
  );


  // Mutations for archive/restore
  const archiveProject = useMutation(api.projects.archiveProject);
  const restoreProject = useMutation(api.projects.restoreProject);

  // FAB tooltip and pulse state
  const [showTooltip, setShowTooltip] = useState(false);
  const [isNewUser, setIsNewUser] = useState(false);

  // Draft project state
  const [draftProject, setDraftProject] = useState<DraftProject | null>(null);
  const [wizardDraftProject, setWizardDraftProject] = useState<WizardDraftProject | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showWizardDeleteConfirm, setShowWizardDeleteConfirm] = useState(false);

  // Draft management functions
  const loadDraftProject = () => {
    try {
      const savedData = localStorage.getItem(STORAGE_KEY);
      if (savedData) {
        const parsed: DraftProject = JSON.parse(savedData);

        // Check if draft is valid and not expired
        if (parsed.timestamp && parsed.formData?.projectName?.trim()) {
          const daysSinceCreated = (Date.now() - parsed.timestamp) / (1000 * 60 * 60 * 24);

          if (daysSinceCreated <= DRAFT_EXPIRY_DAYS) {
            setDraftProject(parsed);
            return;
          } else {
            // Auto-cleanup expired draft
            localStorage.removeItem(STORAGE_KEY);
          }
        }
      }
      setDraftProject(null);
    } catch (error) {
      console.error('Error loading draft project:', error);
      setDraftProject(null);
    }
  };

  // Load wizard draft project
  const loadWizardDraftProject = () => {
    try {
      const savedData = localStorage.getItem(WIZARD_STORAGE_KEY);
      if (savedData) {
        const parsed: WizardDraftProject = JSON.parse(savedData);

        // Check if draft is valid and not expired
        if (parsed.timestamp && parsed.formData?.projectName?.trim()) {
          const daysSinceCreated = (Date.now() - parsed.timestamp) / (1000 * 60 * 60 * 24);

          if (daysSinceCreated <= DRAFT_EXPIRY_DAYS) {
            setWizardDraftProject(parsed);
            return;
          } else {
            // Auto-cleanup expired draft
            localStorage.removeItem(WIZARD_STORAGE_KEY);
          }
        }
      }
      setWizardDraftProject(null);
    } catch (error) {
      console.error('Error loading wizard draft project:', error);
      setWizardDraftProject(null);
    }
  };

  const deleteDraft = () => {
    try {
      localStorage.removeItem(STORAGE_KEY);
      setDraftProject(null);
      setShowDeleteConfirm(false);
    } catch (error) {
      console.error('Error deleting draft:', error);
    }
  };

  const deleteWizardDraft = () => {
    try {
      localStorage.removeItem(WIZARD_STORAGE_KEY);
      setWizardDraftProject(null);
      setShowWizardDeleteConfirm(false);
    } catch (error) {
      console.error('Error deleting wizard draft:', error);
    }
  };

  const continueDraftEditing = () => {
    navigate('/create');
  };

  const continueWizardDraftEditing = () => {
    navigate('/create-wizard');
  };

  const formatDraftTimestamp = (timestamp: number): string => {
    const now = Date.now();
    const diffMinutes = Math.floor((now - timestamp) / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMinutes < 60) {
      return `Oppdatert for ${diffMinutes} min siden`;
    } else if (diffHours < 24) {
      return `Oppdatert for ${diffHours} timer siden`;
    } else {
      return `Oppdatert for ${diffDays} dager siden`;
    }
  };

  // Load draft projects on component mount and when localStorage changes
  useEffect(() => {
    loadDraftProject();
    loadWizardDraftProject();

    // Listen for localStorage changes (e.g., from other tabs)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === STORAGE_KEY) {
        loadDraftProject();
      } else if (e.key === WIZARD_STORAGE_KEY) {
        loadWizardDraftProject();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Check if user is new (for pulsing animation) - only when projects exist
  useEffect(() => {
    const hasSeenFAB = localStorage.getItem('jobblogg-fab-seen');
    if (!hasSeenFAB && projects && projects.length > 0) {
      setIsNewUser(true);
      // Mark as seen after 10 seconds or when clicked
      const timer = setTimeout(() => {
        setIsNewUser(false);
        localStorage.setItem('jobblogg-fab-seen', 'true');
      }, 10000);

      return () => clearTimeout(timer);
    }
  }, [projects?.length]);

  const handleFABClick = () => {
    if (isNewUser) {
      setIsNewUser(false);
      localStorage.setItem('jobblogg-fab-seen', 'true');
    }
  };

  // Loading state with modern skeleton
  if (projects === undefined) {
    return (
      <div className="min-h-screen bg-jobblogg-neutral">
        <div className="container mx-auto px-4 py-8 max-w-7xl">
          {/* Header Skeleton */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-12">
            <div className="flex items-center gap-6">
              <div className="skeleton h-12 w-64"></div>
              <div className="skeleton h-10 w-10 rounded-full"></div>
            </div>
            <div className="skeleton h-12 w-40 rounded-xl"></div>
          </div>

          {/* Stats Skeleton */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl p-6 shadow-lg border border-jobblogg-border">
                <div className="skeleton h-4 w-20 mb-3"></div>
                <div className="skeleton h-8 w-16 mb-2"></div>
                <div className="skeleton h-3 w-24"></div>
              </div>
            ))}
          </div>

          {/* Projects Grid Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl shadow-lg p-6 border border-jobblogg-border">
                <div className="skeleton h-48 w-full rounded-lg mb-4"></div>
                <div className="skeleton h-6 w-3/4 mb-2"></div>
                <div className="skeleton h-4 w-full mb-4"></div>
                <div className="flex gap-2">
                  <div className="skeleton h-8 w-20"></div>
                  <div className="skeleton h-8 w-24"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Archive/Restore handlers
  const handleArchiveProject = async (projectId: string) => {
    console.log('🗂️ Attempting to archive project:', projectId);
    console.log('👤 User ID:', user?.id);

    if (!user?.id) {
      console.error('❌ No user ID available for archiving');
      return;
    }

    try {
      console.log('📤 Calling archiveProject mutation...');
      const result = await archiveProject({
        projectId: projectId as any,
        userId: user.id
      });
      console.log('✅ Archive successful:', result);
    } catch (error) {
      console.error('❌ Feil ved arkivering av prosjekt:', error);
    }
  };

  const handleRestoreProject = async (projectId: string) => {
    console.log('🔄 Attempting to restore project:', projectId);
    console.log('👤 User ID:', user?.id);

    if (!user?.id) {
      console.error('❌ No user ID available for restoring');
      return;
    }

    try {
      console.log('📤 Calling restoreProject mutation...');
      const result = await restoreProject({
        projectId: projectId as any,
        userId: user.id
      });
      console.log('✅ Restore successful:', result);
    } catch (error) {
      console.error('❌ Feil ved gjenåpning av prosjekt:', error);
    }
  };

  // Get the appropriate project list based on filter
  const currentProjects = showArchived ? archivedProjects : projects;

  // Sort projects by creation date (newest first)
  const sortedProjects = currentProjects?.sort((a, b) => b.createdAt - a.createdAt) || [];

  // Debug logging
  console.log('Dashboard Debug:', {
    userId: user?.id,
    userLoaded: !!user,
    projectsRaw: projects,
    projectsCount: projects?.length,
    archivedProjectsRaw: archivedProjects,
    archivedProjectsCount: archivedProjects?.length,
    currentProjectsCount: currentProjects?.length,
    sortedProjectsCount: sortedProjects.length,
    showArchived,
    projectsUndefined: projects === undefined,
    archivedUndefined: archivedProjects === undefined
  });

  // Show loading state if user is not loaded yet
  if (!user) {
    return (
      <DashboardLayout title="Laster..." subtitle="Henter brukerinformasjon...">
        <div className="animate-pulse space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-32 bg-jobblogg-neutral rounded-xl"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-64 bg-jobblogg-neutral rounded-xl"></div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Show loading state if queries are still loading
  if (projects === undefined || archivedProjects === undefined) {
    return (
      <DashboardLayout title="Laster prosjekter..." subtitle="Henter dine prosjekter...">
        <div className="animate-pulse space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-24 bg-jobblogg-neutral rounded-xl"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-64 bg-jobblogg-neutral rounded-xl"></div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <>
      <DashboardLayout
      title="Dine prosjekter"
      subtitle={`Velkommen tilbake, ${user?.firstName || 'Bruker'}! 👋`}
      headerActions={
        <div className="flex items-center gap-4">
          {/* Archive Filter Toggle */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowArchived(false)}
              className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                !showArchived
                  ? 'bg-jobblogg-primary text-white'
                  : 'text-jobblogg-text-medium hover:text-jobblogg-primary hover:bg-jobblogg-primary/10'
              }`}
            >
              Aktive ({projects?.length || 0})
            </button>
            <button
              onClick={() => setShowArchived(true)}
              className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                showArchived
                  ? 'bg-jobblogg-warning text-white'
                  : 'text-jobblogg-text-medium hover:text-jobblogg-warning hover:bg-jobblogg-warning/10'
              }`}
            >
              Arkiverte ({archivedProjects?.length || 0})
            </button>

            {/* Link to dedicated archived projects page */}
            {(archivedProjects?.length || 0) > 0 && (
              <button
                onClick={() => navigate('/archived-projects')}
                className="px-3 py-1.5 rounded-lg text-sm font-medium text-jobblogg-text-medium hover:text-jobblogg-primary hover:bg-jobblogg-primary/10 transition-all duration-200 flex items-center gap-1"
                title="Gå til arkiverte prosjekter"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Administrer
              </button>
            )}
          </div>

          {/* User Identity Group: Avatar + Online Status */}
          <div className="flex items-center gap-2">
            <UserButton
              afterSignOutUrl="/"
              appearance={{
                elements: {
                  avatarBox: "w-10 h-10 rounded-full ring-2 ring-jobblogg-primary/20 hover:ring-jobblogg-primary/40 transition-all duration-200"
                }
              }}
            />
            {/* Online Status Indicator */}
            <div
              className="flex items-center gap-1.5 text-jobblogg-accent text-sm font-medium"
              aria-label="Online"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                />
              </svg>
              <span>Online</span>
              <span className="sr-only">Online</span>
            </div>
          </div>
        </div>
      }
      statsSection={
        <div className="grid-stats">
          <StatsCard
            title="Aktive prosjekter"
            value={projects?.length || 0}
            variant="primary"
            animationDelay="0s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            }
          />
          <StatsCard
            title="Denne måneden"
            value={(projects || []).filter(p => {
              const projectDate = new Date(p.createdAt);
              const now = new Date();
              return projectDate.getMonth() === now.getMonth() &&
                     projectDate.getFullYear() === now.getFullYear();
            }).length}
            variant="accent"
            animationDelay="0.1s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            }
          />
          <StatsCard
            title="Siste prosjekt"
            value={(projects && projects.length > 0) ?
              new Date(projects.sort((a, b) => b.createdAt - a.createdAt)[0].createdAt).toLocaleDateString('nb-NO', { day: 'numeric', month: 'short' }) :
              '-'
            }
            variant="primary"
            animationDelay="0.2s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            }
          />

          {/* Chat Statistics Card */}
          <ChatStatsCard
            animationDelay="0.3s"
            onClick={() => navigate('/conversations')}
          />

        </div>
      }
    >

      {/* Draft Projects Section */}
      {(draftProject || wizardDraftProject) && (
        <section className="space-section">
          <Heading2 className="mb-6 flex items-center gap-3">
            <svg className="w-6 h-6 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Prosjektutkast
          </Heading2>

          <div className="grid-mobile-cards">
            {/* Old CreateProject Draft */}
            {draftProject && (
            <div className="bg-white rounded-xl border-2 border-dashed border-jobblogg-warning/30 p-6 hover:border-jobblogg-warning/50 transition-colors duration-200 animate-slide-up">
              {/* Draft Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-2">
                  <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-warning/10 text-jobblogg-warning border border-jobblogg-warning/20">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    Kladd
                  </div>
                </div>
                <TextMuted className="text-xs">
                  {formatDraftTimestamp(draftProject.timestamp)}
                </TextMuted>
              </div>

              {/* Draft Content */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-2">
                  {draftProject.formData.projectName || 'Uten navn'}
                </h3>

                {draftProject.formData.customerName && (
                  <div className="flex items-center gap-2 mb-2">
                    <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <TextMuted className="text-sm">
                      {draftProject.formData.customerName}
                      {draftProject.formData.customerType === 'firma' && (
                        <span className="ml-1 text-xs bg-jobblogg-neutral px-1 py-0.5 rounded">
                          Bedrift
                        </span>
                      )}
                    </TextMuted>
                  </div>
                )}

                {draftProject.formData.address && (
                  <div className="flex items-center gap-2 mb-2">
                    <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <TextMuted className="text-sm">
                      {draftProject.formData.address}
                    </TextMuted>
                  </div>
                )}

                {draftProject.formData.description && (
                  <BodyText className="text-sm text-jobblogg-text-muted mt-3 line-clamp-2">
                    {draftProject.formData.description}
                  </BodyText>
                )}
              </div>

              {/* Draft Actions */}
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={continueDraftEditing}
                  className="flex-1 inline-flex items-center justify-center gap-2 px-4 py-3 bg-jobblogg-primary hover:bg-jobblogg-primary-dark text-white font-medium rounded-lg transition-colors duration-200 min-h-[44px]"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Fortsett redigering
                </button>
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="inline-flex items-center justify-center gap-2 px-4 py-3 bg-jobblogg-error/10 hover:bg-jobblogg-error/20 text-jobblogg-error font-medium rounded-lg transition-colors duration-200 min-h-[44px]"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Slett kladd
                </button>
              </div>
            </div>
            )}

            {/* New CreateProjectWizard Draft */}
            {wizardDraftProject && (
            <div className="bg-white rounded-xl border-2 border-dashed border-jobblogg-primary/30 p-6 hover:border-jobblogg-primary/50 transition-colors duration-200 animate-slide-up">
              {/* Draft Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-2">
                  <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-primary/10 text-jobblogg-primary border border-jobblogg-primary/20">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Prosjektveiviser
                  </div>
                  <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-accent/10 text-jobblogg-accent border border-jobblogg-accent/20">
                    Steg {wizardDraftProject.currentStep}/3
                  </div>
                </div>
                <TextMuted className="text-xs ml-4">
                  {formatDraftTimestamp(wizardDraftProject.timestamp)}
                </TextMuted>
              </div>

              {/* Draft Content */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-2">
                  {wizardDraftProject.formData.projectName || 'Uten navn'}
                </h3>

                {wizardDraftProject.formData.customerName && (
                  <div className="flex items-center gap-2 mb-2">
                    <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <TextMuted className="text-sm">
                      {wizardDraftProject.formData.customerName}
                      {wizardDraftProject.formData.customerType === 'firma' && (
                        <span className="ml-1 text-xs bg-jobblogg-neutral px-1 py-0.5 rounded">
                          Bedrift
                        </span>
                      )}
                    </TextMuted>
                  </div>
                )}

                {wizardDraftProject.formData.address && (
                  <div className="flex items-center gap-2 mb-2">
                    <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <TextMuted className="text-sm">
                      {wizardDraftProject.formData.address}
                    </TextMuted>
                  </div>
                )}

                {wizardDraftProject.formData.description && (
                  <BodyText className="text-sm text-jobblogg-text-muted mt-3 line-clamp-2">
                    {wizardDraftProject.formData.description}
                  </BodyText>
                )}
              </div>

              {/* Draft Actions */}
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={continueWizardDraftEditing}
                  className="flex-1 inline-flex items-center justify-center gap-2 px-4 py-3 bg-jobblogg-primary hover:bg-jobblogg-primary-dark text-white font-medium rounded-lg transition-colors duration-200 min-h-[44px]"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Fortsett
                </button>
                <button
                  onClick={() => setShowWizardDeleteConfirm(true)}
                  className="inline-flex items-center justify-center gap-2 px-4 py-3 bg-jobblogg-error/10 hover:bg-jobblogg-error/20 text-jobblogg-error font-medium rounded-lg transition-colors duration-200 min-h-[44px]"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Slett utkast
                </button>
              </div>
            </div>
            )}
          </div>
        </section>
      )}

      {/* Projects Grid Section */}
      <section className="space-section">
        <Heading2 className="mb-6 flex items-center gap-3">
          <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          Prosjektoversikt
        </Heading2>

        <div className="grid-mobile-cards">
          {/* Project Cards with Map Integration */}
          {sortedProjects.map((project, index) => {
            // Check if project has complete address information for map display
            const hasCompleteAddress = project.customer && isAddressComplete(
              project.customer.streetAddress,
              project.customer.postalCode,
              project.customer.city
            );

            // Use ProjectMapCard for projects with complete addresses, regular ProjectCard otherwise
            if (hasCompleteAddress) {
              return (
                <div key={project._id} className="relative">
                  <ProjectMapCard
                    project={project}
                    className="cursor-pointer"
                    onClick={() => navigate(`/project/${project._id}`)}
                    width={300}
                    height={200}
                    zoom={15}
                  />

                  {/* Archive Actions Overlay */}
                  {(project.isArchived || showArchived) && (
                    <div className="absolute top-2 right-2">
                      <ArchiveActions
                        isArchived={project.isArchived}
                        onArchive={() => handleArchiveProject(project._id)}
                        onRestore={() => handleRestoreProject(project._id)}
                        size="sm"
                      />
                    </div>
                  )}
                </div>
              );
            }

            // Fallback to regular ProjectCard
            return (
              <ProjectCard
                key={project._id}
                title={project.name}
                description={project.description || 'Ingen beskrivelse tilgjengelig'}
                projectId={project._id}
                updatedAt={new Date(project.createdAt).toLocaleDateString('nb-NO')}
                onClick={() => navigate(`/project/${project._id}`)}
                animationDelay={`${index * 0.1}s`}
                customer={project.customer}
                isArchived={project.isArchived}
                archivedAt={project.archivedAt}
                showArchiveActions={true}
                onArchive={() => handleArchiveProject(project._id)}
                onRestore={() => handleRestoreProject(project._id)}
              />
            );
          })}

          {/* Empty State using UI Component */}
          {sortedProjects.length === 0 && (
            <div className="col-span-full">
              <EmptyState
                title={showArchived ? "📦 Ingen arkiverte prosjekter" : "🚀 Kom i gang med ditt første prosjekt!"}
                description={showArchived
                  ? "Du har ingen arkiverte prosjekter ennå. Prosjekter du arkiverer vil vises her."
                  : "JobbLogg hjelper deg å dokumentere arbeidsprosessen din med bilder og notater. Opprett ditt første prosjekt for å komme i gang."
                }
                actionLabel={showArchived ? "Vis aktive prosjekter" : "Opprett ditt første prosjekt"}
                onAction={() => showArchived ? setShowArchived(false) : navigate('/create-wizard')}
              />
            </div>
          )}
        </div>
      </section>
      </DashboardLayout>

      {/* Floating Action Button with Tooltip - Only show when projects exist */}
      {projects && projects.length > 0 && (
        <div className="fixed bottom-6 right-6 z-50">
        {/* Tooltip */}
        {showTooltip && (
          <div className="
            absolute bottom-16 right-0 mb-2
            bg-jobblogg-text-strong text-white text-sm font-medium
            px-3 py-2 rounded-lg shadow-lg
            whitespace-nowrap
            animate-fade-in
            before:content-[''] before:absolute before:top-full before:right-4
            before:border-4 before:border-transparent before:border-t-jobblogg-text-strong
          ">
            Nytt prosjekt
          </div>
        )}

        <Link to="/create-wizard" onClick={handleFABClick}>
          <button
            className={`
              relative w-14 h-14 bg-jobblogg-primary hover:bg-jobblogg-primary-dark
              rounded-full shadow-lg hover:shadow-xl
              flex items-center justify-center
              transition-all duration-200 ease-in-out
              hover:scale-110 active:scale-95
              focus:outline-none focus:ring-4 focus:ring-jobblogg-primary/30
              group
              ${isNewUser ? 'animate-pulse' : ''}
            `}
            aria-label="Opprett nytt prosjekt"
            onMouseEnter={() => setShowTooltip(true)}
            onMouseLeave={() => setShowTooltip(false)}
            onFocus={() => setShowTooltip(true)}
            onBlur={() => setShowTooltip(false)}
          >
            {/* Pulsing ring for new users */}
            {isNewUser && (
              <div className="
                absolute inset-0 rounded-full
                bg-jobblogg-primary opacity-30
                animate-ping
              " />
            )}

            <svg
              className="w-6 h-6 text-white group-hover:rotate-90 transition-transform duration-200 relative z-10"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 4v16m8-8H4"
              />
            </svg>
          </button>
        </Link>
        </div>
      )}

      {/* Delete Draft Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full p-6 animate-scale-up">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-jobblogg-error/10 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-jobblogg-text-strong">Slett kladd</h3>
                <TextMuted className="text-sm">Denne handlingen kan ikke angres</TextMuted>
              </div>
            </div>

            <BodyText className="mb-6 text-jobblogg-text-medium">
              Er du sikker på at du vil slette kladden "{draftProject?.formData.projectName || 'Uten navn'}"?
              All utfylt informasjon vil gå tapt.
            </BodyText>

            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-3 bg-jobblogg-neutral hover:bg-jobblogg-border text-jobblogg-text-strong font-medium rounded-lg transition-colors duration-200 min-h-[44px]"
              >
                Avbryt
              </button>
              <button
                onClick={deleteDraft}
                className="flex-1 px-4 py-3 bg-jobblogg-error hover:bg-jobblogg-error/90 text-white font-medium rounded-lg transition-colors duration-200 min-h-[44px]"
              >
                Slett kladd
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Wizard Draft Confirmation Dialog */}
      {showWizardDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full p-6 animate-scale-up">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-jobblogg-error/10 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-jobblogg-text-strong">Slett utkast</h3>
                <TextMuted className="text-sm">Denne handlingen kan ikke angres</TextMuted>
              </div>
            </div>

            <BodyText className="mb-6 text-jobblogg-text-medium">
              Er du sikker på at du vil slette utkastet "{wizardDraftProject?.formData.projectName || 'Uten navn'}"?
              All utfylt informasjon fra steg {wizardDraftProject?.currentStep}/3 vil gå tapt.
            </BodyText>

            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={() => setShowWizardDeleteConfirm(false)}
                className="flex-1 px-4 py-3 bg-jobblogg-neutral hover:bg-jobblogg-border text-jobblogg-text-strong font-medium rounded-lg transition-colors duration-200 min-h-[44px]"
              >
                Avbryt
              </button>
              <button
                onClick={deleteWizardDraft}
                className="flex-1 px-4 py-3 bg-jobblogg-error hover:bg-jobblogg-error/90 text-white font-medium rounded-lg transition-colors duration-200 min-h-[44px]"
              >
                Slett utkast
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Dashboard;
