import React from 'react';
import { TextInput, SelectInput, PhoneInput, PrimaryButton, FormError, AddressMapPreview, AddressAutocomplete, PostalCodeInput } from '../../../components/ui';
import type { AddressSuggestion } from '../../../components/ui';

// Local interface definition to avoid import issues
interface WizardFormData {
  projectName: string;
  description: string;
  customerName: string;
  customerType: 'privat' | 'firma';
  contactPerson: string;
  phone: string;
  email: string;
  address: string;
  streetAddress: string;
  postalCode: string;
  city: string;
  entrance: string;
  orgNumber: string;
  notes: string;
  jobDescription: string;
  accessNotes: string;
  equipmentNeeds: string;
  unresolvedQuestions: string;
  personalNotes: string;
}

interface Step2CustomerInfoProps {
  formData: WizardFormData;
  updateFormData: (updates: Partial<WizardFormData>) => void;
  errors: { [key: string]: string };
  useExistingCustomer: boolean;
  setUseExistingCustomer: (value: boolean) => void;
  selectedCustomerId: string;
  setSelectedCustomerId: (value: string) => void;
  existingCustomers: any[] | undefined;
  onNext: () => void;
  onPrevious: () => void;
  isLoading: boolean;
  setIsLoading: (value: boolean) => void;
  setErrors: (errors: { [key: string]: string }) => void;
  createProject: any;
  createCustomer: any;
  user: any;
  clearSavedData: () => void;
  setShowSuccess: (value: boolean) => void;
  navigate: (path: string) => void;
  setCreatedProjectId: (projectId: string | null) => void;
}

export const Step2CustomerInfo: React.FC<Step2CustomerInfoProps> = ({
  formData,
  updateFormData,
  errors,
  useExistingCustomer,
  setUseExistingCustomer,
  selectedCustomerId,
  setSelectedCustomerId,
  existingCustomers,
  onNext,
  onPrevious,
  isLoading,
  setIsLoading,
  setErrors,
  createProject,
  createCustomer,
  user,
  clearSavedData,
  setShowSuccess,
  navigate,
  setCreatedProjectId
}) => {
  // Dynamic label and placeholder based on customer type
  const getCustomerNameLabel = () => {
    return formData.customerType === 'firma' ? 'Firmanavn' : 'Kundenavn';
  };

  const getCustomerNamePlaceholder = () => {
    return formData.customerType === 'firma' ? 'F.eks. Nordmann Bygg AS' : 'F.eks. Ola Nordmann';
  };

  // Validate step 2 fields
  const validateStep = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!useExistingCustomer) {
      if (!formData.customerName || formData.customerName.trim().length < 2) {
        newErrors.customerName = 'Kundenavn må være minst 2 tegn langt';
      }

      // Validate individual address fields instead of combined address
      if (!formData.streetAddress || formData.streetAddress.trim().length < 3) {
        newErrors.streetAddress = 'Gateadresse må være minst 3 tegn lang';
      }

      if (!formData.postalCode || formData.postalCode.trim().length !== 4) {
        newErrors.postalCode = 'Postnummer må være 4 siffer';
      }

      if (!formData.city || formData.city.trim().length < 2) {
        newErrors.city = 'Poststed må være minst 2 tegn langt';
      }

      // Validate postal code format (4 digits)
      if (formData.postalCode && formData.postalCode.trim() !== '') {
        const postalCodePattern = /^\d{4}$/;
        if (!postalCodePattern.test(formData.postalCode.trim())) {
          newErrors.postalCode = 'Postnummer må være 4 siffer';
        }
      }

      if (formData.customerType === 'firma' && formData.orgNumber && formData.orgNumber.trim() !== '') {
        const orgNumberPattern = /^\d{9}$/;
        if (!orgNumberPattern.test(formData.orgNumber.trim())) {
          newErrors.orgNumber = 'Organisasjonsnummer må være 9 siffer';
        }
      }

      if (formData.email && formData.email.trim() !== '') {
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailPattern.test(formData.email.trim())) {
          newErrors.email = 'Ugyldig e-postadresse';
        }
      }
    } else {
      if (!selectedCustomerId) {
        newErrors.selectedCustomer = 'Velg en eksisterende kunde';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep()) {
      onNext();
    }
  };



  return (
    <div className="space-y-6">
      {/* Customer Selection Toggle */}
      <div className="bg-jobblogg-neutral rounded-lg p-4">
        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            <input
              type="radio"
              name="customerChoice"
              checked={!useExistingCustomer}
              onChange={() => setUseExistingCustomer(false)}
              className="w-4 h-4 text-jobblogg-primary border-jobblogg-border focus:ring-jobblogg-primary/30"
            />
            <span className="ml-2 text-sm font-medium text-jobblogg-text-strong">Ny kunde</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="customerChoice"
              checked={useExistingCustomer}
              onChange={() => setUseExistingCustomer(true)}
              className="w-4 h-4 text-jobblogg-primary border-jobblogg-border focus:ring-jobblogg-primary/30"
            />
            <span className="ml-2 text-sm font-medium text-jobblogg-text-strong">Eksisterende kunde</span>
          </label>
        </div>
      </div>

      {/* Existing Customer Selection */}
      {useExistingCustomer && (
        <SelectInput
          label="Velg kunde"
          placeholder="Velg en kunde..."
          required
          fullWidth
          value={selectedCustomerId}
          onChange={(e) => setSelectedCustomerId(e.target.value)}
          error={errors.selectedCustomer}
          options={
            existingCustomers?.map((customer) => ({
              value: customer._id,
              label: `${customer.name} - ${customer.address}`
            })) || []
          }
        />
      )}

      {/* New Customer Form */}
      {!useExistingCustomer && (
        <>
          {/* Customer Type */}
          <SelectInput
            label="Kundetype"
            placeholder="Velg kundetype"
            required
            fullWidth
            value={formData.customerType}
            onChange={(e) => updateFormData({ customerType: e.target.value as 'privat' | 'firma' })}
            options={[
              { value: 'privat', label: 'Privat' },
              { value: 'firma', label: 'Firma' }
            ]}
          />

          {/* Customer Name */}
          <TextInput
            label={getCustomerNameLabel()}
            placeholder={getCustomerNamePlaceholder()}
            required
            fullWidth
            value={formData.customerName}
            onChange={(e) => updateFormData({ customerName: e.target.value })}
            error={errors.customerName}
          />

          {/* Contact Person (for firma) */}
          {formData.customerType === 'firma' && (
            <TextInput
              label="Kontaktperson"
              placeholder="F.eks. Ola Nordmann"
              fullWidth
              value={formData.contactPerson}
              onChange={(e) => updateFormData({ contactPerson: e.target.value })}
            />
          )}

          {/* Smart Address Fields */}
          <div className="space-y-4">
            <AddressAutocomplete
              label="Adresse"
              placeholder="F.eks. Storgata 15"
              required
              fullWidth
              value={formData.streetAddress}
              onChange={(value) => updateFormData({ streetAddress: value })}
              onAddressSelect={(suggestion: AddressSuggestion) => {
                // Auto-fill postal code and city when address is selected
                updateFormData({
                  streetAddress: suggestion.address,
                  postalCode: suggestion.postalCode,
                  city: suggestion.city
                });
              }}
              error={errors.streetAddress}
              helperText="Start å skrive for å få forslag til adresser"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <PostalCodeInput
                label="Postnummer"
                required
                fullWidth
                value={formData.postalCode}
                onChange={(value) => updateFormData({ postalCode: value })}
                onCityChange={(city) => updateFormData({ city })}
                error={errors.postalCode}
              />
              <TextInput
                label="Poststed"
                placeholder="F.eks. Oslo"
                required
                fullWidth
                value={formData.city}
                onChange={(e) => updateFormData({ city: e.target.value })}
                error={errors.city}
                helperText="Fylles automatisk ut fra postnummer"
              />
            </div>

            <TextInput
              label="Oppgang/Inngang/Etasje"
              placeholder="F.eks. Oppgang A, 2. etasje (valgfritt)"
              fullWidth
              value={formData.entrance}
              onChange={(e) => updateFormData({ entrance: e.target.value })}
              helperText="Tilleggsinformasjon for å finne frem"
            />
          </div>

          {/* Address Map Preview */}
          <AddressMapPreview
            streetAddress={formData.streetAddress}
            postalCode={formData.postalCode}
            city={formData.city}
            className="mt-4"
            width={400}
            height={200}
            zoom={15}
          />

          {/* Phone and Email */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <PhoneInput
              label="Telefon"
              fullWidth
              value={formData.phone}
              onChange={(value) => updateFormData({ phone: value })}
              helperText="Norsk mobilnummer"
            />
            <TextInput
              label="E-post"
              type="email"
              placeholder="F.eks. <EMAIL>"
              fullWidth
              value={formData.email}
              onChange={(e) => updateFormData({ email: e.target.value })}
              error={errors.email}
            />
          </div>

          {/* Organization Number (for firma) */}
          {formData.customerType === 'firma' && (
            <TextInput
              label="Organisasjonsnummer"
              placeholder="F.eks. 123456789"
              fullWidth
              value={formData.orgNumber}
              onChange={(e) => updateFormData({ orgNumber: e.target.value })}
              error={errors.orgNumber}
              helperText="9 siffer (valgfritt)"
            />
          )}
        </>
      )}

      {/* General Error */}
      {errors.general && <FormError message={errors.general} />}

      {/* Navigation */}
      <div className="flex justify-between pt-4">
        <PrimaryButton
          variant="secondary"
          onClick={onPrevious}
          size="lg"
          className="btn-wizard-lg"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Tilbake
        </PrimaryButton>

        <PrimaryButton
          variant="secondary"
          onClick={handleNext}
          disabled={(!useExistingCustomer && (!formData.customerName.trim() || !formData.streetAddress.trim() || !formData.postalCode.trim() || !formData.city.trim())) || (useExistingCustomer && !selectedCustomerId)}
          size="lg"
          className="btn-wizard-lg"
        >
          Neste
          <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </PrimaryButton>
      </div>
    </div>
  );
};

export default Step2CustomerInfo;
