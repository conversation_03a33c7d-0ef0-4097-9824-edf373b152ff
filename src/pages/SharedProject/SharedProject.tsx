import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, Heading1, Heading2, TextMedium, TextMuted, TextStrong, EmptyState, ImageModal, PrimaryButton } from '../../components/ui';

import { EditHistoryModal } from '../../components/EditHistoryModal';

import { CustomerLikeButton, useCustomerSession } from '../../components/CustomerLikeButton';
import { debugLog } from '../../utils/featureFlags';
import { useNavigate } from 'react-router-dom';
import { EmbeddedChatContainer } from '../../components/chat';


const SharedProject: React.FC = () => {
  const { sharedId, logId } = useParams<{ sharedId: string; logId?: string }>();
  const navigate = useNavigate();
  const [showEditHistory, setShowEditHistory] = useState<string | null>(null);
  const [hasTrackedAccess, setHasTrackedAccess] = useState(false);

  // Remove chat mode detection - no longer needed for embedded chat




  // Image modal state
  const [selectedImage, setSelectedImage] = useState<{
    url: string;
    alt: string;
    title?: string;
    description?: string;
    date?: string;
  } | null>(null);

  // Image modal handlers
  const openImageModal = (imageData: {
    url: string;
    alt: string;
    title?: string;
    description?: string;
    date?: string;
  }) => {
    setSelectedImage(imageData);
  };

  const closeImageModal = () => {
    setSelectedImage(null);
  };

  // Fetch shared project data
  const project = useQuery(
    api.projects.getBySharedId,
    sharedId ? { sharedId } : "skip"
  );

  // Customer session for likes (after project is defined)
  const customerSessionId = useCustomerSession(sharedId, project?._id);

  // Track access mutation
  const trackAccess = useMutation(api.projects.trackSharedProjectAccess);

  // Fetch log entries for shared project
  const logEntries = useQuery(
    api.logEntries.getBySharedProject,
    sharedId ? { sharedId } : "skip"
  );

  // Remove specific log entry query - no longer needed for embedded chat





  // Fetch like counts for all images in shared project
  const imageLikes = useQuery(
    api.imageLikes.getLikesForSharedProject,
    project && sharedId ? {
      projectId: project._id,
      sharedId
    } : "skip"
  );

  // Edit history handlers
  const handleShowEditHistory = (entryId: string) => {
    setShowEditHistory(entryId);
  };

  const handleCloseEditHistory = () => {
    setShowEditHistory(null);
  };



  // Track access when project is loaded (only once per session)
  useEffect(() => {
    if (project && sharedId && project.isPubliclyShared && !hasTrackedAccess) {
      // Track access asynchronously without blocking UI
      trackAccess({ sharedId })
        .then(() => {
          setHasTrackedAccess(true);
          console.log('Project access tracked successfully');
        })
        .catch(error => {
          console.warn('Failed to track project access:', error);
        });
    }
  }, [project?._id, sharedId, hasTrackedAccess]); // Track only once per session

  // Clear customer data when navigating away from shared project
  useEffect(() => {
    return () => {
      // Cleanup function runs when component unmounts
      // Customer data persistence removed with comment system
    };
  }, []);

  // Loading state
  if (project === undefined || logEntries === undefined) {
    return (
      <PageLayout containerWidth="wide">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-jobblogg-neutral rounded-lg w-1/2"></div>
          <div className="h-4 bg-jobblogg-neutral rounded w-3/4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-32 bg-jobblogg-neutral rounded-xl"></div>
            ))}
          </div>
        </div>
      </PageLayout>
    );
  }

  // Debug logging for contractor notes
  if (project) {
    debugLog('SharedProject render', {
      projectId: project._id,
      showContractorNotes: project.shareSettings?.showContractorNotes,
      hasJobData: !!project.jobData,
      jobDataKeys: project.jobData ? Object.keys(project.jobData) : []
    });

    // Additional debug for contractor notes
    console.log('🔍 SharedProject Debug:', {
      sharedId: project.sharedId,
      showContractorNotes: project.shareSettings?.showContractorNotes,
      hasJobData: !!project.jobData,
      jobData: project.jobData,
      shareSettings: project.shareSettings
    });
  }

  // Project not found or not shared
  if (!project) {
    return (
      <PageLayout containerWidth="wide">
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-6 bg-jobblogg-error-soft rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <Heading2 className="mb-4">Prosjekt ikke funnet</Heading2>
          <TextMuted className="text-lg">
            Dette prosjektet eksisterer ikke eller er ikke lenger delt offentlig.
          </TextMuted>
        </div>
      </PageLayout>
    );
  }

  // Remove chat mode rendering - now using embedded chat

  return (
    <PageLayout containerWidth="wide">
      <div className="space-y-8">
        {/* Project Header */}
        <div className="text-center space-y-4 animate-slide-up">
          <div className="w-16 h-16 mx-auto bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 8h5a2 2 0 002-2V9a2 2 0 00-2-2H9a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <Heading1 className="bg-gradient-to-r from-jobblogg-primary to-jobblogg-accent bg-clip-text text-transparent">
            {project.name}
          </Heading1>
          <TextMedium className="text-lg max-w-2xl mx-auto">
            {project.description}
          </TextMedium>
          
          {/* Customer Info */}
          {project.customer && (
            <div className="bg-jobblogg-neutral rounded-xl p-4 max-w-md mx-auto">
              <TextStrong className="block mb-1">Kunde</TextStrong>
              <TextMedium>{project.customer.name}</TextMedium>
              {project.customer.address && (
                <TextMuted className="text-sm">{project.customer.address}</TextMuted>
              )}
            </div>
          )}
        </div>



        {/* Project Log Entries */}
        <div className="space-y-6">
          <div className="space-y-2">
            <Heading2>Prosjektlogg</Heading2>
            <div className="flex items-center gap-2 text-jobblogg-text-muted">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span className="text-sm">{logEntries?.length || 0} oppføringer</span>
            </div>
          </div>

          {logEntries && logEntries.length > 0 ? (
            <div className="space-y-6">
              {logEntries.map((entry, index) => (
                <div
                  key={entry._id}
                  className="bg-jobblogg-neutral rounded-xl p-6 card-hover animate-slide-up"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="flex flex-col gap-4">
                    <div className="flex justify-between items-start gap-4">
                      <div className="flex-1">
                        <div className="flex items-start gap-2 mb-2">
                          <TextMedium className="leading-relaxed flex-1">
                            {entry.entryType === 'system' && entry.description === 'Prosjekt startet'
                              ? 'Prosjekt registrert'
                              : entry.description}
                          </TextMedium>
                          {entry.isEdited && (
                            <span className="px-2 py-1 bg-jobblogg-primary-soft text-jobblogg-primary text-xs font-medium rounded-full flex-shrink-0">
                              Redigert
                            </span>
                          )}
                        </div>


                        {entry.isEdited && (
                          <button
                            onClick={() => handleShowEditHistory(entry._id)}
                            className="text-jobblogg-primary hover:text-jobblogg-primary-dark text-sm font-medium transition-colors duration-200 flex items-center gap-1"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Se endringshistorikk
                          </button>
                        )}
                      </div>
                      <div className="flex items-center gap-2 text-body-small flex-shrink-0">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span>
                          {new Date(entry.createdAt).toLocaleDateString('nb-NO', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>
                    </div>
                    {entry.imageUrl && (
                      <div className="mt-4">
                        <div className="relative">
                          <button
                            onClick={() => openImageModal({
                              url: entry.imageUrl!,
                              alt: "Prosjektbilde",
                              title: entry.description || "Prosjektbilde",
                              description: entry.description,
                              date: new Date(entry.createdAt).toLocaleDateString('nb-NO', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })
                            })}
                            className="relative block w-full text-left focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-opacity-50 rounded-xl group"
                            aria-label="Klikk for å se bildet i full størrelse"
                          >
                            <img
                              src={entry.imageUrl}
                              alt="Prosjektbilde"
                              className="rounded-xl w-full h-auto max-h-96 object-cover shadow-md hover:shadow-lg transition-all duration-200 group-hover:scale-[1.02] cursor-pointer"
                            />
                            {/* Click indicator overlay */}
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-100">
                              <div className="bg-white bg-opacity-90 rounded-full p-3 shadow-lg">
                                <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                                </svg>
                              </div>
                            </div>
                          </button>

                        </div>

                        {/* Like Button - positioned below the image (Instagram style) */}
                        {customerSessionId && project && sharedId && (
                          <div className="mt-3">
                            <CustomerLikeButton
                              logEntryId={entry._id}
                              projectId={project._id}
                              sharedId={sharedId}
                              customerSessionId={customerSessionId}
                              isArchived={project.isArchived}
                              showCount={true}
                            />
                          </div>
                        )}

                      </div>
                    )}

                    {/* Embedded Chat for Customer - Available for all log entries */}
                    {customerSessionId && project && sharedId && (
                      <EmbeddedChatContainer
                        logId={entry._id}
                        userId={customerSessionId}
                        userRole="customer"
                        headerText="Diskuter med kontraktør"
                        maxHeight="250px"
                        className="mt-3"
                      />
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <EmptyState
              icon={
                <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              }
              title="Ingen logger ennå"
              description="Dette prosjektet har ingen logger ennå. Kom tilbake senere for oppdateringer! 📝"
            />
          )}
        </div>

        {/* Contractor Notes Section - Only show if enabled in share settings */}
        {project.shareSettings?.showContractorNotes && project.jobData && (
          <div className="bg-white rounded-xl shadow-lg border border-jobblogg-border p-8 animate-slide-up" style={{ animationDelay: '400ms' }}>
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-jobblogg-accent/10 to-jobblogg-primary/10 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <Heading2>Kontraktørnotater</Heading2>
            </div>

            <div className="space-y-6">
              {/* Job Description */}
              {project.jobData.jobDescription && (
                <div>
                  <TextStrong className="block mb-2 text-jobblogg-text-strong">Jobbeskrivelse</TextStrong>
                  <div className="bg-jobblogg-neutral rounded-lg p-4 border border-jobblogg-border">
                    <TextMedium className="whitespace-pre-wrap">{project.jobData.jobDescription}</TextMedium>
                  </div>
                </div>
              )}

              {/* Access Notes */}
              {project.jobData.accessNotes && (
                <div>
                  <TextStrong className="block mb-2 text-jobblogg-text-strong">Tilgangsnotater</TextStrong>
                  <div className="bg-jobblogg-neutral rounded-lg p-4 border border-jobblogg-border">
                    <TextMedium className="whitespace-pre-wrap">{project.jobData.accessNotes}</TextMedium>
                  </div>
                </div>
              )}

              {/* Equipment Needs */}
              {project.jobData.equipmentNeeds && (
                <div>
                  <TextStrong className="block mb-2 text-jobblogg-text-strong">Utstyrsbehov</TextStrong>
                  <div className="bg-jobblogg-neutral rounded-lg p-4 border border-jobblogg-border">
                    <TextMedium className="whitespace-pre-wrap">{project.jobData.equipmentNeeds}</TextMedium>
                  </div>
                </div>
              )}

              {/* Unresolved Questions */}
              {project.jobData.unresolvedQuestions && (
                <div>
                  <TextStrong className="block mb-2 text-jobblogg-text-strong">Uløste spørsmål</TextStrong>
                  <div className="bg-jobblogg-neutral rounded-lg p-4 border border-jobblogg-border">
                    <TextMedium className="whitespace-pre-wrap">{project.jobData.unresolvedQuestions}</TextMedium>
                  </div>
                </div>
              )}

              {/* Personal Notes */}
              {project.jobData.personalNotes && (
                <div>
                  <TextStrong className="block mb-2 text-jobblogg-text-strong">Personlige notater</TextStrong>
                  <div className="bg-jobblogg-neutral rounded-lg p-4 border border-jobblogg-border">
                    <TextMedium className="whitespace-pre-wrap">{project.jobData.personalNotes}</TextMedium>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}



        {/* Edit History Modal */}
        {showEditHistory && (
          <EditHistoryModal
            entryId={showEditHistory}
            isOpen={true}
            onClose={handleCloseEditHistory}
            isSharedView={true}
            sharedId={sharedId}
          />
        )}
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <ImageModal
          isOpen={!!selectedImage}
          onClose={closeImageModal}
          imageUrl={selectedImage.url}
          imageAlt={selectedImage.alt}
          imageTitle={selectedImage.title}
          imageDescription={selectedImage.description}
          imageDate={selectedImage.date}
        />
      )}
    </PageLayout>
  );
};

export default SharedProject;
