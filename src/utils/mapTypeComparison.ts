/**
 * Google Maps Map Types Comparison for JobbLogg
 * Analysis of different map types for contractor/construction use cases
 */

export interface MapTypeOption {
  type: 'roadmap' | 'satellite' | 'hybrid' | 'terrain';
  name: string;
  description: string;
  contractorBenefits: string[];
  drawbacks: string[];
  bestUseCases: string[];
  recommendedZoom: number[];
}

export const MAP_TYPE_OPTIONS: MapTypeOption[] = [
  {
    type: 'roadmap',
    name: '<PERSON><PERSON><PERSON><PERSON> (Roadmap)',
    description: 'Standard veikart med gater, veier og stedsnavn',
    contractorBenefits: [
      'Tydelige gatenavn og adresser',
      'Lett å lese og navigere',
      'Viser offentlig transport og veier',
      'Rask lasting og lav båndbredde'
    ],
    drawbacks: [
      'Viser ikke bygningsdetaljer',
      'Kan ikke skille mellom ulike bygningstyper',
      'Ingen visuell kontekst av området'
    ],
    bestUseCases: [
      'Navigasjon til jobbsted',
      '<PERSON><PERSON> gateadresser',
      'Planlegge ruter'
    ],
    recommendedZoom: [13, 17]
  },
  {
    type: 'satellite',
    name: '<PERSON><PERSON><PERSON><PERSON> (Satellite)',
    description: 'Satellittbilder fra luften uten etiketter',
    contractorBenefits: [
      'Viser faktiske bygninger og strukturer',
      'Kan identifisere bygningstyper (villa, leilighet, etc.)',
      'Viser utendørsområder og hager',
      'Identifiserer parkeringsmuligheter',
      'Viser bygningens tilstand og størrelse'
    ],
    drawbacks: [
      'Ingen gatenavn eller adresser synlige',
      'Kan være vanskelig å orientere seg',
      'Bilder kan være utdaterte',
      'Høyere båndbreddebruk'
    ],
    bestUseCases: [
      'Identifisere bygningstype før ankomst',
      'Vurdere tilgjengelighet og parkering',
      'Se utendørsområder for utendørsarbeid'
    ],
    recommendedZoom: [15, 20]
  },
  {
    type: 'hybrid',
    name: 'Hybrid',
    description: 'Satellittbilder med veietiketter og stedsnavn',
    contractorBenefits: [
      'Beste av begge verdener',
      'Viser bygninger OG gatenavn',
      'Perfekt for kontraktører',
      'Lett navigasjon med visuell kontekst',
      'Identifiserer både adresse og bygningstype'
    ],
    drawbacks: [
      'Høyeste båndbreddebruk',
      'Kan virke rotete ved lavt zoom',
      'Lengre lastetid'
    ],
    bestUseCases: [
      'Kontraktørarbeid generelt',
      'Identifisere spesifikke bygninger',
      'Planlegge tilgang og utstyr',
      'Vurdere jobbkompleksitet'
    ],
    recommendedZoom: [15, 18]
  },
  {
    type: 'terrain',
    name: 'Terreng (Terrain)',
    description: 'Viser topografi, høyder og naturlige funksjoner',
    contractorBenefits: [
      'Viser høydeforskjeller og skråninger',
      'Identifiserer vanskelig tilgjengelige områder',
      'Nyttig for utendørsprosjekter',
      'Viser naturlige hindringer'
    ],
    drawbacks: [
      'Mindre detaljert for bygninger',
      'Ikke optimal for urbane områder',
      'Begrenset nytte for innendørsarbeid'
    ],
    bestUseCases: [
      'Utendørs konstruksjonsprosjekter',
      'Landskapsarbeid',
      'Vurdere tilgjengelighet i bratt terreng'
    ],
    recommendedZoom: [10, 15]
  }
];

/**
 * Get recommended map type based on project context
 */
export const getRecommendedMapType = (context: {
  projectType?: 'indoor' | 'outdoor' | 'mixed';
  priority?: 'navigation' | 'building_identification' | 'terrain_assessment';
  bandwidth?: 'low' | 'normal' | 'high';
}): 'roadmap' | 'satellite' | 'hybrid' | 'terrain' => {
  const { projectType = 'mixed', priority = 'building_identification', bandwidth = 'normal' } = context;

  // For terrain assessment, always use terrain
  if (priority === 'terrain_assessment') {
    return 'terrain';
  }

  // For low bandwidth, prefer roadmap
  if (bandwidth === 'low') {
    return 'roadmap';
  }

  // For building identification, prefer hybrid or satellite
  if (priority === 'building_identification') {
    return bandwidth === 'high' ? 'hybrid' : 'satellite';
  }

  // For navigation priority, prefer roadmap
  if (priority === 'navigation') {
    return 'roadmap';
  }

  // Default recommendation for contractors: hybrid (best overall)
  return 'hybrid';
};

/**
 * JobbLogg specific recommendations
 */
export const JOBBLOGG_MAP_RECOMMENDATIONS = {
  // For project cards on dashboard - quick overview
  projectCard: {
    mapType: 'hybrid' as const,
    zoom: 16,
    reasoning: 'Shows both building details and street names for quick identification'
  },
  
  // For address preview in forms - detailed view
  addressPreview: {
    mapType: 'hybrid' as const,
    zoom: 17,
    reasoning: 'Higher zoom to show specific building and immediate surroundings'
  },
  
  // For directions/navigation
  directions: {
    mapType: 'roadmap' as const,
    zoom: 15,
    reasoning: 'Clear street names and navigation routes'
  }
} as const;
