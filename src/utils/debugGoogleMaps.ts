/**
 * Debug utilities for Google Maps API issues
 */

const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '';

/**
 * Test Google Maps API key with a simple request
 */
export const testGoogleMapsAPI = async (): Promise<{
  success: boolean;
  error?: string;
  details?: any;
}> => {
  if (!GOOGLE_MAPS_API_KEY) {
    return {
      success: false,
      error: 'API key not configured'
    };
  }

  try {
    // Test with a simple static map request
    const testUrl = `https://maps.googleapis.com/maps/api/staticmap?center=Oslo,Norge&zoom=10&size=100x100&key=${GOOGLE_MAPS_API_KEY}`;
    
    console.log('Testing Google Maps API with URL:', testUrl);
    
    const response = await fetch(testUrl);
    
    if (response.ok) {
      return {
        success: true,
        details: {
          status: response.status,
          contentType: response.headers.get('content-type')
        }
      };
    } else {
      const errorText = await response.text();
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        details: {
          status: response.status,
          statusText: response.statusText,
          errorBody: errorText
        }
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error
    };
  }
};

/**
 * Generate a test static map URL for debugging
 */
export const generateTestMapUrl = (
  address: string = 'Karl Johans gate 1, 0154 Oslo',
  mapType: 'roadmap' | 'satellite' | 'hybrid' | 'terrain' = 'roadmap'
): string => {
  const params = new URLSearchParams({
    center: address,
    zoom: '15',
    size: '400x300',
    maptype: mapType,
    markers: `color:red|${address}`,
    key: GOOGLE_MAPS_API_KEY
  });

  return `https://maps.googleapis.com/maps/api/staticmap?${params.toString()}`;
};

/**
 * Check API key restrictions and common issues
 */
export const diagnoseAPIIssues = () => {
  const issues: string[] = [];
  const suggestions: string[] = [];

  if (!GOOGLE_MAPS_API_KEY) {
    issues.push('API key not configured');
    suggestions.push('Add VITE_GOOGLE_MAPS_API_KEY to your .env.local file');
  } else {
    if (GOOGLE_MAPS_API_KEY.length < 30) {
      issues.push('API key seems too short');
      suggestions.push('Verify your API key is complete');
    }

    if (!GOOGLE_MAPS_API_KEY.startsWith('AIza')) {
      issues.push('API key format looks incorrect');
      suggestions.push('Google Maps API keys typically start with "AIza"');
    }
  }

  // Check current domain
  const currentDomain = window.location.hostname;
  if (currentDomain === 'localhost' || currentDomain === '127.0.0.1') {
    suggestions.push('Ensure your API key allows localhost referrers');
    suggestions.push('Add http://localhost:5173/* to your API key restrictions');
  }

  return {
    issues,
    suggestions,
    apiKey: GOOGLE_MAPS_API_KEY ? `${GOOGLE_MAPS_API_KEY.substring(0, 10)}...` : 'Not configured',
    domain: currentDomain
  };
};
