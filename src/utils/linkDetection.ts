/**
 * Link detection and processing utilities for JobbLogg chat system
 */

export interface DetectedLink {
  url: string;
  displayText: string;
  startIndex: number;
  endIndex: number;
  isValid: boolean;
}

export interface ProcessedTextSegment {
  type: 'text' | 'link';
  content: string;
  url?: string;
  isValid?: boolean;
}

/**
 * Regular expression to detect URLs in text
 * Matches http://, https://, www., and domain.tld patterns
 */
const URL_REGEX = /(https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?|www\.(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?|(?:[-\w.])+\.(?:com|org|net|edu|gov|mil|int|no|se|dk|fi|de|uk|fr|es|it|nl|be|ch|at|pl|cz|hu|ru|jp|cn|au|ca|br|mx|ar|cl|co|pe|ve|za|in|kr|th|sg|my|id|ph|vn|tw|hk|nz)(?:\:[0-9]+)?(?:\/(?:[\w\/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?)/gi;

/**
 * List of allowed protocols for security
 */
const ALLOWED_PROTOCOLS = ['http:', 'https:'];

/**
 * Validates if a URL is safe and properly formatted
 */
export function validateUrl(url: string): boolean {
  try {
    // Add protocol if missing
    let fullUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      fullUrl = `https://${url}`;
    }

    const urlObj = new URL(fullUrl);
    
    // Check if protocol is allowed
    if (!ALLOWED_PROTOCOLS.includes(urlObj.protocol)) {
      return false;
    }

    // Check for valid hostname
    if (!urlObj.hostname || urlObj.hostname.length < 3) {
      return false;
    }

    // Prevent javascript: and data: URLs
    if (urlObj.protocol === 'javascript:' || urlObj.protocol === 'data:') {
      return false;
    }

    return true;
  } catch {
    return false;
  }
}

/**
 * Normalizes a URL by adding protocol if missing
 */
export function normalizeUrl(url: string): string {
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  return `https://${url}`;
}

/**
 * Detects all URLs in a text string
 */
export function detectLinks(text: string): DetectedLink[] {
  const links: DetectedLink[] = [];
  let match;

  // Reset regex lastIndex to ensure fresh matching
  URL_REGEX.lastIndex = 0;

  while ((match = URL_REGEX.exec(text)) !== null) {
    const url = match[0];
    const normalizedUrl = normalizeUrl(url);
    const isValid = validateUrl(url);

    links.push({
      url: normalizedUrl,
      displayText: url,
      startIndex: match.index,
      endIndex: match.index + url.length,
      isValid
    });
  }

  return links;
}

/**
 * Processes text and splits it into segments of text and links
 */
export function processTextWithLinks(text: string): ProcessedTextSegment[] {
  const links = detectLinks(text);
  
  if (links.length === 0) {
    return [{ type: 'text', content: text }];
  }

  const segments: ProcessedTextSegment[] = [];
  let lastIndex = 0;

  // Sort links by start index to process them in order
  links.sort((a, b) => a.startIndex - b.startIndex);

  for (const link of links) {
    // Add text before the link
    if (link.startIndex > lastIndex) {
      const textContent = text.slice(lastIndex, link.startIndex);
      if (textContent) {
        segments.push({ type: 'text', content: textContent });
      }
    }

    // Add the link
    segments.push({
      type: 'link',
      content: link.displayText,
      url: link.url,
      isValid: link.isValid
    });

    lastIndex = link.endIndex;
  }

  // Add remaining text after the last link
  if (lastIndex < text.length) {
    const textContent = text.slice(lastIndex);
    if (textContent) {
      segments.push({ type: 'text', content: textContent });
    }
  }

  return segments;
}

/**
 * Extracts domain from URL for display purposes
 */
export function extractDomain(url: string): string {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname.replace(/^www\./, '');
  } catch {
    return url;
  }
}

/**
 * Checks if URL is likely to be an image based on file extension
 */
export function isImageUrl(url: string): boolean {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp'];
  const urlLower = url.toLowerCase();
  return imageExtensions.some(ext => urlLower.includes(ext));
}

/**
 * Checks if URL is likely to be a video based on file extension or domain
 */
export function isVideoUrl(url: string): boolean {
  const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi'];
  const videoDomains = ['youtube.com', 'youtu.be', 'vimeo.com', 'twitch.tv'];
  
  const urlLower = url.toLowerCase();
  
  return videoExtensions.some(ext => urlLower.includes(ext)) ||
         videoDomains.some(domain => urlLower.includes(domain));
}
