/**
 * Offline Storage Utility for JobbLogg PWA
 * Provides offline data storage and synchronization capabilities
 */

// Types for offline data
export interface OfflineProject {
  id: string;
  title: string;
  description: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  isOffline?: boolean;
  syncStatus?: 'pending' | 'syncing' | 'synced' | 'error';
}

export interface OfflineProjectLog {
  id: string;
  projectId: string;
  description: string;
  images: string[];
  createdAt: string;
  isOffline?: boolean;
  syncStatus?: 'pending' | 'syncing' | 'synced' | 'error';
}

export interface OfflineData {
  projects: OfflineProject[];
  projectLogs: OfflineProjectLog[];
  lastSync: string;
}

// Storage keys
const STORAGE_KEYS = {
  OFFLINE_DATA: 'jobblogg-offline-data',
  SYNC_QUEUE: 'jobblogg-sync-queue',
  LAST_SYNC: 'jobblogg-last-sync'
} as const;

/**
 * Offline Storage Manager
 */
export class OfflineStorageManager {
  private static instance: OfflineStorageManager;
  private syncQueue: Array<{ type: string; data: any; timestamp: string }> = [];

  private constructor() {
    this.loadSyncQueue();
  }

  static getInstance(): OfflineStorageManager {
    if (!OfflineStorageManager.instance) {
      OfflineStorageManager.instance = new OfflineStorageManager();
    }
    return OfflineStorageManager.instance;
  }

  // Get offline data
  getOfflineData(): OfflineData {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.OFFLINE_DATA);
      return data ? JSON.parse(data) : { projects: [], projectLogs: [], lastSync: '' };
    } catch (error) {
      console.error('[OfflineStorage] Error loading offline data:', error);
      return { projects: [], projectLogs: [], lastSync: '' };
    }
  }

  // Save offline data
  saveOfflineData(data: OfflineData): void {
    try {
      localStorage.setItem(STORAGE_KEYS.OFFLINE_DATA, JSON.stringify(data));
    } catch (error) {
      console.error('[OfflineStorage] Error saving offline data:', error);
    }
  }

  // Add project to offline storage
  addOfflineProject(project: Omit<OfflineProject, 'isOffline' | 'syncStatus'>): void {
    const offlineData = this.getOfflineData();
    const offlineProject: OfflineProject = {
      ...project,
      isOffline: true,
      syncStatus: 'pending'
    };
    
    offlineData.projects.push(offlineProject);
    this.saveOfflineData(offlineData);
    
    // Add to sync queue
    this.addToSyncQueue('create-project', offlineProject);
  }

  // Add project log to offline storage
  addOfflineProjectLog(projectLog: Omit<OfflineProjectLog, 'isOffline' | 'syncStatus'>): void {
    const offlineData = this.getOfflineData();
    const offlineLog: OfflineProjectLog = {
      ...projectLog,
      isOffline: true,
      syncStatus: 'pending'
    };
    
    offlineData.projectLogs.push(offlineLog);
    this.saveOfflineData(offlineData);
    
    // Add to sync queue
    this.addToSyncQueue('create-project-log', offlineLog);
  }

  // Get projects (including offline ones)
  getAllProjects(): OfflineProject[] {
    const offlineData = this.getOfflineData();
    return offlineData.projects;
  }

  // Get project logs for a project (including offline ones)
  getProjectLogs(projectId: string): OfflineProjectLog[] {
    const offlineData = this.getOfflineData();
    return offlineData.projectLogs.filter(log => log.projectId === projectId);
  }

  // Add item to sync queue
  private addToSyncQueue(type: string, data: any): void {
    const queueItem = {
      type,
      data,
      timestamp: new Date().toISOString()
    };
    
    this.syncQueue.push(queueItem);
    this.saveSyncQueue();
  }

  // Load sync queue from storage
  private loadSyncQueue(): void {
    try {
      const queue = localStorage.getItem(STORAGE_KEYS.SYNC_QUEUE);
      this.syncQueue = queue ? JSON.parse(queue) : [];
    } catch (error) {
      console.error('[OfflineStorage] Error loading sync queue:', error);
      this.syncQueue = [];
    }
  }

  // Save sync queue to storage
  private saveSyncQueue(): void {
    try {
      localStorage.setItem(STORAGE_KEYS.SYNC_QUEUE, JSON.stringify(this.syncQueue));
    } catch (error) {
      console.error('[OfflineStorage] Error saving sync queue:', error);
    }
  }

  // Get sync queue
  getSyncQueue(): Array<{ type: string; data: any; timestamp: string }> {
    return [...this.syncQueue];
  }

  // Clear sync queue
  clearSyncQueue(): void {
    this.syncQueue = [];
    this.saveSyncQueue();
  }

  // Remove item from sync queue
  removeFromSyncQueue(index: number): void {
    this.syncQueue.splice(index, 1);
    this.saveSyncQueue();
  }

  // Update sync status
  updateSyncStatus(id: string, type: 'project' | 'projectLog', status: OfflineProject['syncStatus']): void {
    const offlineData = this.getOfflineData();
    
    if (type === 'project') {
      const project = offlineData.projects.find(p => p.id === id);
      if (project) {
        project.syncStatus = status;
        if (status === 'synced') {
          project.isOffline = false;
        }
      }
    } else {
      const log = offlineData.projectLogs.find(l => l.id === id);
      if (log) {
        log.syncStatus = status;
        if (status === 'synced') {
          log.isOffline = false;
        }
      }
    }
    
    this.saveOfflineData(offlineData);
  }

  // Clear all offline data
  clearOfflineData(): void {
    localStorage.removeItem(STORAGE_KEYS.OFFLINE_DATA);
    localStorage.removeItem(STORAGE_KEYS.SYNC_QUEUE);
    localStorage.removeItem(STORAGE_KEYS.LAST_SYNC);
    this.syncQueue = [];
  }

  // Get storage usage
  getStorageUsage(): { used: number; available: number; percentage: number } {
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        // Modern browsers with Storage API
        navigator.storage.estimate().then(estimate => {
          const used = estimate.usage || 0;
          const available = estimate.quota || 0;
          const percentage = available > 0 ? (used / available) * 100 : 0;
          
          console.log('[OfflineStorage] Storage usage:', {
            used: Math.round(used / 1024 / 1024 * 100) / 100 + ' MB',
            available: Math.round(available / 1024 / 1024 * 100) / 100 + ' MB',
            percentage: Math.round(percentage * 100) / 100 + '%'
          });
          
          return { used, available, percentage };
        });
      }
      
      // Fallback for older browsers
      let totalSize = 0;
      for (const key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          totalSize += localStorage[key].length;
        }
      }
      
      // Estimate available storage (5MB typical limit for localStorage)
      const estimatedLimit = 5 * 1024 * 1024; // 5MB in bytes
      const percentage = (totalSize / estimatedLimit) * 100;
      
      return {
        used: totalSize,
        available: estimatedLimit,
        percentage
      };
    } catch (error) {
      console.error('[OfflineStorage] Error calculating storage usage:', error);
      return { used: 0, available: 0, percentage: 0 };
    }
  }

  // Check if storage is nearly full
  isStorageNearlyFull(): boolean {
    const usage = this.getStorageUsage();
    return usage.percentage > 80; // Alert when over 80% full
  }
}

// Export singleton instance
export const offlineStorage = OfflineStorageManager.getInstance();

// Utility functions
export const OfflineUtils = {
  // Check if app is offline
  isOffline: (): boolean => !navigator.onLine,

  // Generate offline ID
  generateOfflineId: (): string => `offline-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,

  // Format sync status for display
  formatSyncStatus: (status: OfflineProject['syncStatus']): string => {
    switch (status) {
      case 'pending':
        return 'Venter på synkronisering';
      case 'syncing':
        return 'Synkroniserer...';
      case 'synced':
        return 'Synkronisert';
      case 'error':
        return 'Synkroniseringsfeil';
      default:
        return 'Ukjent status';
    }
  },

  // Get sync status color
  getSyncStatusColor: (status: OfflineProject['syncStatus']): string => {
    switch (status) {
      case 'pending':
        return 'text-jobblogg-warning';
      case 'syncing':
        return 'text-jobblogg-primary';
      case 'synced':
        return 'text-jobblogg-success';
      case 'error':
        return 'text-jobblogg-error';
      default:
        return 'text-jobblogg-text-muted';
    }
  }
};
