/**
 * Responsive Date Formatting Utilities for JobbLogg
 * 
 * Provides mobile-first date formatting with Norwegian localization
 * and responsive design considerations for different screen sizes.
 * 
 * Uses JobbLogg's custom breakpoints:
 * - Mobile: ≤377px (xs breakpoint)
 * - Desktop: >377px
 */

export interface DateFormatOptions {
  showTime?: boolean;
  showYear?: boolean;
  abbreviated?: boolean;
  relative?: boolean;
}

/**
 * Format timestamp for mobile viewports (≤377px)
 * Uses compact Norwegian formatting to save space
 */
export const formatDateMobile = (
  timestamp: number, 
  options: DateFormatOptions = {}
): string => {
  try {
    const date = new Date(timestamp);
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return 'Ugyldig dato';
    }

    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    const diffInDays = Math.floor(diffInHours / 24);

    // Relative time for recent dates (if enabled)
    if (options.relative && diffInHours < 24) {
      if (diffInHours < 1) {
        return 'Nå';
      } else if (diffInHours < 2) {
        return '1t siden';
      } else {
        return `${Math.floor(diffInHours)}t siden`;
      }
    }

    // Time only for today
    if (diffInHours < 24 && options.showTime !== false) {
      return date.toLocaleTimeString('nb-NO', {
        hour: '2-digit',
        minute: '2-digit'
      });
    }

    // This week - abbreviated weekday + time
    if (diffInDays < 7 && options.abbreviated) {
      const weekdays = ['søn', 'man', 'tir', 'ons', 'tor', 'fre', 'lør'];
      const weekday = weekdays[date.getDay()];
      
      if (options.showTime) {
        const time = date.toLocaleTimeString('nb-NO', {
          hour: '2-digit',
          minute: '2-digit'
        });
        return `${weekday} ${time}`;
      }
      return weekday;
    }

    // Compact date format: DD.MM or DD.MM.YY
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    
    if (options.showYear || diffInDays > 365) {
      const year = date.getFullYear().toString().slice(-2);
      return `${day}.${month}.${year}`;
    }
    
    return `${day}.${month}`;
    
  } catch (error) {
    console.error('Error formatting mobile date:', error);
    return 'Ugyldig dato';
  }
};

/**
 * Format timestamp for desktop viewports (>377px)
 * Uses full Norwegian formatting with more detail
 */
export const formatDateDesktop = (
  timestamp: number, 
  options: DateFormatOptions = {}
): string => {
  try {
    const date = new Date(timestamp);
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return 'Ugyldig dato';
    }

    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    // Relative time for very recent dates (if enabled)
    if (options.relative && diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      if (diffInMinutes < 1) {
        return 'Akkurat nå';
      } else if (diffInMinutes < 60) {
        return `${diffInMinutes} min siden`;
      }
    }

    // Full Norwegian date format
    const formatOptions: Intl.DateTimeFormatOptions = {
      day: '2-digit',
      month: options.abbreviated ? 'short' : '2-digit',
      year: options.showYear !== false ? 'numeric' : undefined,
    };

    if (options.showTime !== false) {
      formatOptions.hour = '2-digit';
      formatOptions.minute = '2-digit';
    }

    return date.toLocaleDateString('nb-NO', formatOptions);
    
  } catch (error) {
    console.error('Error formatting desktop date:', error);
    return 'Ugyldig dato';
  }
};

/**
 * Format timestamp with responsive design
 * Returns object with both mobile and desktop formats
 */
export const formatDateResponsive = (
  timestamp: number, 
  options: DateFormatOptions = {}
) => {
  return {
    mobile: formatDateMobile(timestamp, options),
    desktop: formatDateDesktop(timestamp, options)
  };
};

/**
 * Format chat message timestamp (optimized for chat bubbles)
 */
export const formatChatTimestamp = (timestamp: number) => {
  return formatDateResponsive(timestamp, {
    showTime: true,
    abbreviated: true,
    relative: false
  });
};

/**
 * Format log entry timestamp (optimized for project logs)
 */
export const formatLogTimestamp = (timestamp: number) => {
  return formatDateResponsive(timestamp, {
    showTime: true,
    abbreviated: true,
    showYear: false
  });
};

/**
 * Format project creation timestamp (optimized for project details)
 */
export const formatProjectTimestamp = (timestamp: number) => {
  return formatDateResponsive(timestamp, {
    showTime: true,
    abbreviated: false,
    showYear: true
  });
};

/**
 * Format read status timestamp (optimized for read indicators)
 */
export const formatReadStatusTimestamp = (timestamp: number) => {
  return formatDateResponsive(timestamp, {
    showTime: true,
    abbreviated: true,
    relative: true
  });
};

/**
 * Legacy format function for backward compatibility
 * Returns DD.MM.YYYY HH:MM format
 */
export const formatNorwegianDateTime = (timestamp: number): string => {
  try {
    const date = new Date(timestamp);

    if (isNaN(date.getTime())) {
      return 'Ugyldig dato';
    }

    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${day}.${month}.${year} ${hours}:${minutes}`;
  } catch (error) {
    console.error('Error formatting Norwegian date:', error);
    return 'Ugyldig dato';
  }
};

/**
 * React component helper for responsive timestamp display
 * Returns JSX with responsive classes
 */
export const ResponsiveTimestamp = ({ 
  timestamp, 
  options = {},
  className = '',
  ariaLabel
}: {
  timestamp: number;
  options?: DateFormatOptions;
  className?: string;
  ariaLabel?: string;
}) => {
  const { mobile, desktop } = formatDateResponsive(timestamp, options);
  const fullAriaLabel = ariaLabel || `Tidspunkt: ${desktop}`;

  return (
    <>
      {/* Mobile timestamp (≤377px) */}
      <time 
        className={`xs:hidden ${className}`}
        dateTime={new Date(timestamp).toISOString()}
        aria-label={fullAriaLabel}
      >
        {mobile}
      </time>
      
      {/* Desktop timestamp (>377px) */}
      <time 
        className={`hidden xs:inline ${className}`}
        dateTime={new Date(timestamp).toISOString()}
        aria-label={fullAriaLabel}
      >
        {desktop}
      </time>
    </>
  );
};
