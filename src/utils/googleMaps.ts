/**
 * Google Maps utility functions for JobbLogg
 * Handles address formatting, static map generation, and directions
 */

// Google Maps API configuration
const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '';

// Default contractor location (can be configured)
const DEFAULT_CONTRACTOR_LOCATION = 'Oslo, Norge';

/**
 * Format address from structured fields to a single string
 */
export const formatAddress = (
  streetAddress: string,
  postalCode: string,
  city: string,
  entrance?: string
): string => {
  const baseAddress = `${streetAddress}, ${postalCode} ${city}`;
  return entrance ? `${baseAddress} (${entrance})` : baseAddress;
};

/**
 * Format address for Google Maps API (plain text - URLSearchParams will handle encoding)
 */
export const formatAddressForMaps = (
  streetAddress: string,
  postalCode: string,
  city: string
): string => {
  // Return plain text - URLSearchParams will handle encoding automatically
  return `${streetAddress}, ${postalCode} ${city}, Norge`;
};

/**
 * Generate Google Maps static image URL
 */
export const generateStaticMapUrl = (
  streetAddress: string,
  postalCode: string,
  city: string,
  options: {
    width?: number;
    height?: number;
    zoom?: number;
    mapType?: 'roadmap' | 'satellite' | 'hybrid' | 'terrain';
  } = {}
): string => {
  const {
    width = 400,
    height = 300,
    zoom = 15,
    mapType = 'roadmap'
  } = options;

  const address = formatAddressForMaps(streetAddress, postalCode, city);
  
  const params = new URLSearchParams({
    center: address,
    zoom: zoom.toString(),
    size: `${width}x${height}`,
    maptype: mapType,
    markers: `color:red|${address}`,
    key: GOOGLE_MAPS_API_KEY
  });

  return `https://maps.googleapis.com/maps/api/staticmap?${params.toString()}`;
};

/**
 * Detect if user is on mobile device
 */
const isMobileDevice = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

/**
 * Generate Google Maps directions URL optimized for mobile and desktop
 * On mobile: Uses google.com/maps URL that automatically opens the app if installed
 * On desktop: Uses standard web directions URL
 */
export const generateDirectionsUrl = (
  streetAddress: string,
  postalCode: string,
  city: string,
  origin: string = DEFAULT_CONTRACTOR_LOCATION
): string => {
  const destination = formatAddressForMaps(streetAddress, postalCode, city);

  if (isMobileDevice()) {
    // Mobile-optimized URL that opens Google Maps app if available
    const params = new URLSearchParams({
      api: '1',
      origin: origin,
      destination: destination,
      travelmode: 'driving'
    });
    return `https://www.google.com/maps/dir/?${params.toString()}`;
  } else {
    // Desktop web URL - need to encode destination for URL path
    const encodedOrigin = encodeURIComponent(origin);
    const encodedDestination = encodeURIComponent(destination);
    return `https://www.google.com/maps/dir/${encodedOrigin}/${encodedDestination}`;
  }
};

/**
 * Validate if Google Maps API key is configured
 */
export const isGoogleMapsConfigured = (): boolean => {
  return Boolean(GOOGLE_MAPS_API_KEY && GOOGLE_MAPS_API_KEY.length > 0);
};

/**
 * Get fallback image URL when Google Maps is not available
 */
export const getFallbackMapImage = (): string => {
  return '/images/map-placeholder.svg';
};

/**
 * Check if address fields are complete for Maps integration
 */
export const isAddressComplete = (
  streetAddress?: string,
  postalCode?: string,
  city?: string
): boolean => {
  return Boolean(
    streetAddress && streetAddress.trim() &&
    postalCode && postalCode.trim() &&
    city && city.trim()
  );
};
