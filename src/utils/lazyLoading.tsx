import { lazy, type ComponentType } from 'react';
import React from 'react';

// Error boundary for lazy components
class LazyComponentErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ComponentType }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    console.error('[LazyComponent] Error caught by boundary:', error instanceof Error ? error.message : String(error));
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('[LazyComponent] Component error details:', {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : 'No stack trace',
      componentStack: errorInfo.componentStack
    });
  }

  render() {
    if (this.state.hasError) {
      const Fallback = this.props.fallback;
      if (Fallback) {
        return <Fallback />;
      }

      return (
        <div className="min-h-screen bg-white flex items-center justify-center px-4 xxs:px-2">
          <div className="text-center p-6 xxs:p-4 max-w-md xxs:max-w-[calc(100vw-1rem)] mx-auto">
            <div className="text-red-500 mb-4">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-jobblogg-text-strong mb-2">
              Noe gikk galt
            </h2>
            <p className="text-jobblogg-text-muted mb-4">
              Siden kunne ikke lastes. Prøv å oppdatere siden.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-jobblogg-primary text-white rounded-lg hover:bg-jobblogg-primary/90 transition-colors"
            >
              Oppdater siden
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Lazy loading utility with error boundary support
export function createLazyComponent<T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>
): T {
  const LazyComponent = lazy(() => {
    return importFunc().catch(error => {
      console.error('[LazyComponent] Import failed:', error instanceof Error ? error.message : String(error));
      // Return a fallback component instead of throwing
      return {
        default: (() => (
          <div className="min-h-screen bg-white flex items-center justify-center px-4 xxs:px-2">
            <div className="text-center p-6 xxs:p-4 max-w-md xxs:max-w-[calc(100vw-1rem)] mx-auto">
              <div className="text-red-500 mb-4">
                <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-jobblogg-text-strong mb-2">
                Kunne ikke laste siden
              </h2>
              <p className="text-jobblogg-text-muted mb-4">
                Det oppstod en feil ved lasting av siden. Prøv å oppdatere siden.
              </p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-jobblogg-primary text-white rounded-lg hover:bg-jobblogg-primary/90 transition-colors"
              >
                Oppdater siden
              </button>
            </div>
          </div>
        )) as T
      };
    });
  });

  // Wrap with error boundary
  const WrappedComponent = (props: any) => (
    <LazyComponentErrorBoundary>
      <LazyComponent {...props} />
    </LazyComponentErrorBoundary>
  );

  // Return the wrapped component with proper typing
  return WrappedComponent as unknown as T;
}

// Preload utility for critical routes
export function preloadComponent(importFunc: () => Promise<any>): void {
  // Preload the component in the background
  importFunc().catch(error => {
    console.warn('[LazyLoading] Preload failed:', error instanceof Error ? error.message : String(error));
  });
}

// Image lazy loading utility
export class ImageLazyLoader {
  private observer: IntersectionObserver | null = null;
  private images: Set<HTMLImageElement> = new Set();

  constructor() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        this.handleIntersection.bind(this),
        {
          rootMargin: '50px 0px',
          threshold: 0.01
        }
      );
    }
  }

  private handleIntersection(entries: IntersectionObserverEntry[]) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement;
        this.loadImage(img);
        this.observer?.unobserve(img);
        this.images.delete(img);
      }
    });
  }

  private loadImage(img: HTMLImageElement) {
    const src = img.dataset.src;
    const srcset = img.dataset.srcset;

    if (src) {
      img.src = src;
    }
    if (srcset) {
      img.srcset = srcset;
    }

    img.classList.remove('lazy');
    img.classList.add('lazy-loaded');
  }

  observe(img: HTMLImageElement) {
    if (this.observer) {
      this.images.add(img);
      this.observer.observe(img);
    } else {
      // Fallback for browsers without IntersectionObserver
      this.loadImage(img);
    }
  }

  unobserve(img: HTMLImageElement) {
    if (this.observer) {
      this.observer.unobserve(img);
      this.images.delete(img);
    }
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
      this.images.clear();
    }
  }
}

// Global image lazy loader instance
export const imageLazyLoader = new ImageLazyLoader();

// React hook for lazy loading images
import { useEffect, useRef } from 'react';

export function useLazyImage(src: string, placeholder?: string) {
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    const img = imgRef.current;
    if (!img) return;

    // Set placeholder
    if (placeholder) {
      img.src = placeholder;
    }

    // Set data attributes for lazy loading
    img.dataset.src = src;
    img.classList.add('lazy');

    // Observe for lazy loading
    imageLazyLoader.observe(img);

    return () => {
      imageLazyLoader.unobserve(img);
    };
  }, [src, placeholder]);

  return imgRef;
}

// Content lazy loading for large lists
export class ContentLazyLoader {
  private observer: IntersectionObserver | null = null;
  private elements: Map<Element, () => void> = new Map();

  constructor() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        this.handleIntersection.bind(this),
        {
          rootMargin: '100px 0px',
          threshold: 0.1
        }
      );
    }
  }

  private handleIntersection(entries: IntersectionObserverEntry[]) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const callback = this.elements.get(entry.target);
        if (callback) {
          callback();
          this.observer?.unobserve(entry.target);
          this.elements.delete(entry.target);
        }
      }
    });
  }

  observe(element: Element, callback: () => void) {
    if (this.observer) {
      this.elements.set(element, callback);
      this.observer.observe(element);
    } else {
      // Fallback for browsers without IntersectionObserver
      callback();
    }
  }

  unobserve(element: Element) {
    if (this.observer) {
      this.observer.unobserve(element);
      this.elements.delete(element);
    }
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
      this.elements.clear();
    }
  }
}

// Global content lazy loader instance
export const contentLazyLoader = new ContentLazyLoader();

// React hook for lazy loading content
export function useLazyContent(callback: () => void, deps: any[] = []) {
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    contentLazyLoader.observe(element, callback);

    return () => {
      contentLazyLoader.unobserve(element);
    };
  }, deps);

  return elementRef;
}

// Performance monitoring utilities
export const PerformanceMonitor = {
  // Measure component render time
  measureRender: (componentName: string, renderFn: () => void) => {
    const startTime = performance.now();
    renderFn();
    const endTime = performance.now();
    console.log(`[Performance] ${componentName} render time: ${endTime - startTime}ms`);
  },

  // Measure image load time
  measureImageLoad: (src: string): Promise<number> => {
    return new Promise((resolve) => {
      const startTime = performance.now();
      const img = new Image();
      
      img.onload = () => {
        const loadTime = performance.now() - startTime;
        console.log(`[Performance] Image load time (${src}): ${loadTime}ms`);
        resolve(loadTime);
      };
      
      img.onerror = () => {
        const loadTime = performance.now() - startTime;
        console.warn(`[Performance] Image load failed (${src}): ${loadTime}ms`);
        resolve(loadTime);
      };
      
      img.src = src;
    });
  },

  // Monitor Core Web Vitals
  monitorWebVitals: () => {
    // Largest Contentful Paint (LCP)
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      console.log('[Performance] LCP:', lastEntry.startTime);
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay (FID)
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        console.log('[Performance] FID:', entry.processingStart - entry.startTime);
      });
    }).observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift (CLS)
    let clsValue = 0;
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
          console.log('[Performance] CLS:', clsValue);
        }
      });
    }).observe({ entryTypes: ['layout-shift'] });
  }
};

// Bundle size optimization utilities
export const BundleOptimizer = {
  // Dynamic import with error handling
  dynamicImport: async <T>(importFn: () => Promise<T>): Promise<T | null> => {
    try {
      return await importFn();
    } catch (error) {
      console.error('[BundleOptimizer] Dynamic import failed:', error instanceof Error ? error.message : String(error));
      return null;
    }
  },

  // Preload critical resources
  preloadResource: (href: string, as: string) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    document.head.appendChild(link);
  },

  // Prefetch non-critical resources
  prefetchResource: (href: string) => {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = href;
    document.head.appendChild(link);
  }
};
