/* ===== CLERK AUTHENTICATION TOUCH TARGET FIXES ===== */
/* Critical fixes for mobile audit issues - WCAG AA compliance */

/* 
 * Fix for critical touch target violations identified in mobile audit:
 * - a.cl-internal-1du9ypm: 48×14px → 44×44px minimum
 * - button.btn-outline.btn-responsive: 256×40px → proper height
 * - Touch target spacing and overlap issues
 */

/* ===== CLERK INTERNAL ELEMENTS ===== */
/* Fix Clerk internal link elements - Critical Priority 1 */
.cl-internal-1du9ypm,
a[class*="cl-internal"],
.cl-footerActionLink,
.cl-footerActionText,
.cl-socialButtonsBlockButton,
.cl-formButtonPrimary {
  min-height: 44px !important;
  min-width: 44px !important;
  padding: 12px 16px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-decoration: none !important;
  border-radius: 0.5rem !important;
  transition: all 0.2s ease !important;
  touch-action: manipulation !important;
  box-sizing: border-box !important;
}

/* Ensure proper spacing between Clerk elements */
.cl-internal-1du9ypm + .cl-internal-1du9ypm,
a[class*="cl-internal"] + a[class*="cl-internal"],
.cl-footerActionLink + .cl-footerActionLink {
  margin-left: 8px !important;
}

/* ===== CLERK FORM BUTTONS ===== */
/* Fix Clerk form button touch targets */
.cl-formButtonPrimary,
.cl-formButtonSecondary,
.cl-socialButtonsBlockButton,
button[class*="cl-formButton"],
button[class*="cl-socialButton"] {
  min-height: 44px !important;
  min-width: 44px !important;
  padding: 12px 24px !important;
  touch-action: manipulation !important;
  border-radius: 0.75rem !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

/* ===== CLERK INPUT ELEMENTS ===== */
/* Ensure Clerk input fields have proper touch targets */
.cl-formFieldInput,
input[class*="cl-formField"],
.cl-input {
  min-height: 44px !important;
  padding: 12px 16px !important;
  font-size: 16px !important;
  touch-action: manipulation !important;
  border-radius: 0.5rem !important;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
/* Ultra-narrow screen adjustments (≤377px) */
@media (max-width: 377px) {
  .cl-internal-1du9ypm,
  a[class*="cl-internal"],
  .cl-footerActionLink,
  .cl-formButtonPrimary,
  .cl-socialButtonsBlockButton {
    min-height: 40px !important;
    min-width: 40px !important;
    padding: 10px 14px !important;
    font-size: 14px !important;
  }

  .cl-formFieldInput,
  input[class*="cl-formField"],
  .cl-input {
    min-height: 40px !important;
    padding: 10px 14px !important;
    font-size: 14px !important;
  }
}

/* Critical narrow screen adjustments (≤351px) */
@media (max-width: 351px) {
  .cl-internal-1du9ypm,
  a[class*="cl-internal"],
  .cl-footerActionLink,
  .cl-formButtonPrimary,
  .cl-socialButtonsBlockButton {
    min-height: 36px !important;
    min-width: 36px !important;
    padding: 8px 12px !important;
    font-size: 13px !important;
  }

  .cl-formFieldInput,
  input[class*="cl-formField"],
  .cl-input {
    min-height: 36px !important;
    padding: 8px 12px !important;
    font-size: 13px !important;
  }
}

/* ===== TOUCH TARGET SPACING FIXES ===== */
/* Prevent touch target overlaps - Critical Priority 1 */
.cl-card,
.cl-main,
.cl-form {
  position: relative !important;
}

.cl-card * {
  position: relative !important;
  z-index: 1 !important;
}

.cl-card *:focus,
.cl-card *:active {
  z-index: 2 !important;
}

/* Ensure minimum spacing between interactive elements */
.cl-formButtonPrimary,
.cl-socialButtonsBlockButton,
.cl-footerActionLink {
  margin: 4px !important;
}

.cl-formButtonPrimary + .cl-formButtonPrimary,
.cl-socialButtonsBlockButton + .cl-socialButtonsBlockButton,
.cl-footerActionLink + .cl-footerActionLink {
  margin-top: 8px !important;
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
/* Enhanced focus states for better keyboard navigation */
.cl-internal-1du9ypm:focus,
a[class*="cl-internal"]:focus,
.cl-formButtonPrimary:focus,
.cl-socialButtonsBlockButton:focus {
  outline: 2px solid #1D4ED8 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(29, 78, 216, 0.1) !important;
}

/* Ensure proper hover states */
.cl-internal-1du9ypm:hover,
a[class*="cl-internal"]:hover,
.cl-formButtonPrimary:hover,
.cl-socialButtonsBlockButton:hover {
  transform: scale(1.02) !important;
  transition: transform 0.2s ease !important;
}

/* ===== CONTAINER FIXES ===== */
/* Fix Clerk container responsive issues */
.cl-card {
  width: 100% !important;
  max-width: 28rem !important;
  margin: 0 auto !important;
  padding: 2rem !important;
  box-sizing: border-box !important;
}

@media (max-width: 377px) {
  .cl-card {
    padding: 1.5rem !important;
    margin: 0 1rem !important;
    max-width: calc(100vw - 2rem) !important;
  }
}

@media (max-width: 351px) {
  .cl-card {
    padding: 1rem !important;
    margin: 0 0.5rem !important;
    max-width: calc(100vw - 1rem) !important;
  }
}

/* ===== LOADING STATES ===== */
/* Ensure loading states maintain touch targets */
.cl-formButtonPrimary[data-loading="true"],
.cl-socialButtonsBlockButton[data-loading="true"] {
  min-height: 44px !important;
  min-width: 44px !important;
  pointer-events: none !important;
}

@media (max-width: 377px) {
  .cl-formButtonPrimary[data-loading="true"],
  .cl-socialButtonsBlockButton[data-loading="true"] {
    min-height: 40px !important;
    min-width: 40px !important;
  }
}

@media (max-width: 351px) {
  .cl-formButtonPrimary[data-loading="true"],
  .cl-socialButtonsBlockButton[data-loading="true"] {
    min-height: 36px !important;
    min-width: 36px !important;
  }
}

/* ===== CRITICAL OVERRIDE SPECIFICITY ===== */
/* Ensure these fixes take precedence over Clerk's default styles */
.cl-rootBox .cl-card .cl-internal-1du9ypm,
.cl-rootBox .cl-card a[class*="cl-internal"],
.cl-rootBox .cl-card .cl-formButtonPrimary,
.cl-rootBox .cl-card .cl-socialButtonsBlockButton {
  min-height: 44px !important;
  min-width: 44px !important;
  touch-action: manipulation !important;
}

@media (max-width: 377px) {
  .cl-rootBox .cl-card .cl-internal-1du9ypm,
  .cl-rootBox .cl-card a[class*="cl-internal"],
  .cl-rootBox .cl-card .cl-formButtonPrimary,
  .cl-rootBox .cl-card .cl-socialButtonsBlockButton {
    min-height: 40px !important;
    min-width: 40px !important;
  }
}

@media (max-width: 351px) {
  .cl-rootBox .cl-card .cl-internal-1du9ypm,
  .cl-rootBox .cl-card a[class*="cl-internal"],
  .cl-rootBox .cl-card .cl-formButtonPrimary,
  .cl-rootBox .cl-card .cl-socialButtonsBlockButton {
    min-height: 36px !important;
    min-width: 36px !important;
  }
}
