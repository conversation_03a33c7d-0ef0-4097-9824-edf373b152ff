@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* Import critical touch target fixes for Clerk authentication */
@import './styles/clerkTouchTargetFixes.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    /* Prevent horizontal scrolling on ultra-narrow screens */
    overflow-x: hidden;
  }

  body {
    @apply transition-colors duration-200;
    /* Prevent horizontal scrolling on ultra-narrow screens */
    overflow-x: hidden;
    min-width: 320px;
  }

  /* Global layout overflow prevention */
  * {
    box-sizing: border-box;
  }

  /* Prevent layout overflow on ultra-narrow screens */
  @media (max-width: 351px) {
    html, body {
      max-width: 100vw;
      overflow-x: hidden;
    }

    #root {
      max-width: 100vw;
      overflow-x: hidden;
    }
  }
}

@layer components {
 
 /* ===== SURFACE AND HIGHLIGHT SUPPORT CLASSES ===== */

/* New background surface – for sections, forms, alerts, cards */
.bg-surface {
  @apply bg-jobblogg-surface;
}

/* Section container with surface background and subtle border */
.section-surface {
  @apply bg-jobblogg-surface rounded-xl border border-jobblogg-border p-6 shadow-soft;
}

/* Emphasize elements using the highlight color (e.g., tags, status indicators) */
.text-highlight {
  @apply text-jobblogg-highlight;
}

.bg-highlight-soft {
  @apply bg-jobblogg-highlight/10 text-jobblogg-highlight;
}

/* Tags, badges, and chips styled with the highlight color */
.tag-highlight {
  @apply inline-flex items-center px-3 py-1 text-sm font-medium rounded-full bg-highlight-soft;
}
  
  /* Modern button system - Flat design with enhanced accessibility and WCAG AA touch targets */
  .btn-modern {
    @apply inline-flex items-center justify-center gap-2 transition-all duration-200
           font-medium rounded-xl px-6 py-3 min-h-[44px] min-w-[44px]
           focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2
           disabled:opacity-50 disabled:cursor-not-allowed
           shadow-soft hover:shadow-medium whitespace-nowrap
           touch-manipulation;
  }

  /* Primary button - Main CTA */
  .btn-primary-solid {
    @apply btn-modern bg-jobblogg-primary text-white
           hover:bg-jobblogg-primary-light active:bg-jobblogg-primary-dark
           focus:ring-jobblogg-primary;
  }

  /* Secondary button - Alternative actions */
  .btn-secondary-solid {
    @apply btn-modern bg-jobblogg-accent text-white
           hover:bg-jobblogg-accent-light active:bg-jobblogg-accent-dark
           focus:ring-jobblogg-accent;
  }

  /* Outline button - Secondary actions */
  .btn-outline {
    @apply btn-modern border-2 border-jobblogg-primary text-jobblogg-primary bg-white
           hover:bg-jobblogg-primary hover:text-white active:bg-jobblogg-primary-dark
           focus:ring-jobblogg-primary;
  }

  /* Ghost button - Subtle actions */
  .btn-ghost-enhanced {
    @apply btn-modern text-jobblogg-text-medium bg-transparent
           hover:text-jobblogg-text-strong hover:bg-jobblogg-neutral
           focus:ring-jobblogg-primary shadow-none hover:shadow-soft;
  }

  /* Soft button variants */
  .btn-soft {
    @apply btn-modern bg-jobblogg-primary-soft text-jobblogg-primary
           hover:bg-jobblogg-primary hover:text-white
           focus:ring-jobblogg-primary;
  }

  .btn-success-soft {
    @apply btn-modern bg-jobblogg-accent-soft text-jobblogg-accent
           hover:bg-jobblogg-accent hover:text-white
           focus:ring-jobblogg-accent;
  }

  .btn-warning-soft {
    @apply btn-modern bg-jobblogg-warning-soft text-jobblogg-warning
           hover:bg-jobblogg-warning hover:text-jobblogg-text-strong
           focus:ring-jobblogg-warning;
  }

  .btn-error-soft {
    @apply btn-modern bg-jobblogg-error-soft text-jobblogg-error
           hover:bg-jobblogg-error hover:text-white
           focus:ring-jobblogg-error;
  }

  /* Responsive button utilities with WCAG AA touch targets */
  .btn-responsive {
    @apply text-sm sm:text-base px-3 py-2 sm:px-6 sm:py-3
           min-h-[44px] min-w-[44px] sm:min-w-[120px]
           touch-manipulation;
  }

  .btn-responsive-lg {
    @apply text-base sm:text-lg px-4 py-2.5 sm:px-8 sm:py-4
           min-h-[48px] min-w-[48px] sm:min-w-[160px]
           touch-manipulation;
  }

  /* Ultra-narrow screen button adjustments */
  @media (max-width: 377px) {
    .btn-responsive {
      @apply min-h-[40px] min-w-[40px] px-2 py-1.5 text-xs;
    }

    .btn-responsive-lg {
      @apply min-h-[44px] min-w-[44px] px-3 py-2 text-sm;
    }
  }

  @media (max-width: 351px) {
    .btn-responsive {
      @apply min-h-[36px] min-w-[36px] px-1.5 py-1 text-xs;
    }

    .btn-responsive-lg {
      @apply min-h-[40px] min-w-[40px] px-2 py-1.5 text-xs;
    }
  }

  .btn-wizard {
    @apply btn-modern btn-responsive whitespace-nowrap flex-shrink-0;
  }

  .btn-wizard-lg {
    @apply btn-modern btn-responsive-lg whitespace-nowrap flex-shrink-0;
  }

  /* Modern card system - Flat design with subtle elevation */
  .card-modern {
    @apply bg-white rounded-xl border border-jobblogg-border p-6
           transition-all duration-200 shadow-soft;
  }

  .card-elevated {
    @apply card-modern shadow-medium hover:shadow-large
           hover:-translate-y-0.5 cursor-pointer;
  }

  .card-hover {
    @apply card-elevated;
  }

  /* Modern input system - Enhanced accessibility and flat design */
  .input-modern {
    @apply w-full bg-white border border-jobblogg-border text-jobblogg-text-strong
           placeholder:text-jobblogg-text-muted rounded-xl px-4 py-3 min-h-[44px]
           transition-all duration-200
           focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary
           hover:border-jobblogg-primary
           disabled:bg-jobblogg-neutral disabled:text-jobblogg-text-muted disabled:cursor-not-allowed;
  }

  /* Textarea styling - Consistent with input design */
  .textarea-modern {
    @apply input-modern resize-y min-h-[120px] py-3;
  }



  .input-bordered {
    @apply input-modern;
  }

  /* Alert components - Enhanced contrast for WCAG AA compliance */
  .alert-success {
    @apply bg-jobblogg-accent-soft border border-jobblogg-accent text-jobblogg-text-strong rounded-xl p-4 shadow-sm;
  }

  .alert-warning {
    @apply bg-jobblogg-warning-soft border border-jobblogg-warning text-jobblogg-text-strong rounded-xl p-4 shadow-sm;
  }

  .alert-error {
    @apply bg-jobblogg-error-soft border border-jobblogg-error text-jobblogg-text-strong rounded-xl p-4 shadow-sm;
  }

  .alert-info {
    @apply bg-jobblogg-primary-soft border border-jobblogg-primary text-jobblogg-text-strong rounded-xl p-4 shadow-sm;
  }

  /* Alert icon containers with proper contrast */
  .alert-icon-success {
    @apply bg-jobblogg-accent text-white rounded-full p-2 flex-shrink-0;
  }

  .alert-icon-warning {
    @apply bg-jobblogg-warning text-jobblogg-text-strong rounded-full p-2 flex-shrink-0;
  }

  .alert-icon-error {
    @apply bg-jobblogg-error text-white rounded-full p-2 flex-shrink-0;
  }

  .alert-icon-info {
    @apply bg-jobblogg-primary text-white rounded-full p-2 flex-shrink-0;
  }

  /* Loading skeleton */
  .skeleton {
    @apply animate-pulse bg-jobblogg-neutral rounded-xl;
  }

  /* Modern typography hierarchy - 2025 design system */
  .text-heading-1 {
    @apply text-4xl sm:text-5xl text-jobblogg-text-strong font-bold leading-tight mb-4;
  }

  .text-heading-2 {
    @apply text-3xl sm:text-4xl text-jobblogg-text-strong font-semibold leading-tight mb-3;
  }

  .text-heading-3 {
    @apply text-2xl sm:text-3xl text-jobblogg-text-strong font-semibold leading-snug mb-2;
  }

  .text-body {
    @apply text-base text-jobblogg-text-medium leading-relaxed;
  }

  .text-small {
    @apply text-sm text-jobblogg-text-medium leading-normal;
  }

  .text-caption {
    @apply text-xs text-jobblogg-text-muted leading-normal;
  }

  /* Text color utilities - WCAG AA compliant */
  .text-strong {
    @apply text-jobblogg-text-strong;
  }

  .text-medium {
    @apply text-jobblogg-text-medium;
  }

  .text-muted {
    @apply text-jobblogg-text-muted;
  }



  /* Modern layout system - Mobile-first responsive design */
  .container-section {
    @apply py-8 px-4 sm:py-12 sm:px-6 lg:px-8;
  }

  .container-content {
    @apply max-w-7xl mx-auto;
  }

  /* Enhanced container system for different content types */
  .container-narrow {
    @apply max-w-2xl mx-auto px-4 sm:px-6;
  }

  .container-medium {
    @apply max-w-4xl mx-auto px-4 sm:px-6;
  }

  .container-wide {
    @apply max-w-6xl mx-auto px-4 sm:px-6;
  }

  .container-full {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Ultra-narrow screen container adjustments */
  @media (max-width: 351px) {
    .container-narrow,
    .container-medium,
    .container-wide,
    .container-full {
      @apply px-2 max-w-[calc(100vw-1rem)];
    }
  }

  /* Enhanced spacing system for modern design */
  .space-section {
    @apply mb-12 sm:mb-16 lg:mb-20;
  }

  .space-component {
    @apply mb-6 sm:mb-8;
  }

  .space-element {
    @apply mb-3 sm:mb-4;
  }

  /* Page layout utilities */
  .page-header {
    @apply mb-8 sm:mb-12;
  }

  .page-content {
    @apply space-y-6 sm:space-y-8;
  }

  /* Mobile-first touch targets - WCAG AA compliant with ultra-narrow screen support */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  .touch-target-large {
    @apply min-h-[48px] min-w-[48px] flex items-center justify-center;
  }

  /* Ultra-narrow screen utilities for screens < 378px */
  .touch-target-compact {
    @apply min-h-[40px] min-w-[40px] flex items-center justify-center;
  }

  @media (max-width: 377px) {
    .touch-target {
      @apply min-h-[40px] min-w-[40px];
    }

    /* Ultra-narrow screen chat optimizations */
    .chat-input-compact {
      @apply text-xs px-2 py-1.5;
    }

    .chat-button-compact {
      @apply p-1 rounded-md;
    }
  }

  /* Ultra-narrow screens ≤351px - Critical breakpoint */
  @media (max-width: 351px) {
    .touch-target {
      @apply min-h-[36px] min-w-[36px];
    }

    /* Extreme narrow screen optimizations */
    .chat-input-ultra-compact {
      @apply text-xs px-1.5 py-1.5;
    }

    .chat-button-ultra-compact {
      @apply p-0.5 rounded;
    }

    /* Ensure minimum usable sizes */
    .chat-textarea-ultra-narrow {
      @apply min-h-[36px] text-xs;
    }

    .chat-send-button-ultra-narrow {
      @apply min-w-[36px] px-1.5;
    }

    /* Layout overflow prevention */
    .layout-overflow-safe {
      @apply max-w-[calc(100vw-1rem)] overflow-hidden;
    }

    /* Responsive text adjustments */
    .text-responsive-narrow {
      @apply text-sm leading-tight;
    }

    /* Responsive spacing adjustments */
    .space-responsive-narrow {
      @apply space-y-2;
    }

    /* Responsive padding adjustments */
    .p-responsive-narrow {
      @apply p-2;
    }
  }

  /* ===== CRITICAL TOUCH TARGET FIXES ===== */
  /* Fix for Clerk authentication touch target violations identified in mobile audit */

  /* Clerk internal link elements - Critical fix for 48×14px → 44×44px minimum */
  .cl-internal-1du9ypm,
  a[class*="cl-internal"] {
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 12px 16px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-decoration: none !important;
    border-radius: 0.5rem !important;
    transition: all 0.2s ease !important;
    touch-action: manipulation !important;
  }

  /* Ultra-narrow screen adjustments for Clerk elements */
  @media (max-width: 377px) {
    .cl-internal-1du9ypm,
    a[class*="cl-internal"] {
      min-height: 40px !important;
      min-width: 40px !important;
      padding: 10px 14px !important;
    }
  }

  @media (max-width: 351px) {
    .cl-internal-1du9ypm,
    a[class*="cl-internal"] {
      min-height: 36px !important;
      min-width: 36px !important;
      padding: 8px 12px !important;
    }
  }

  /* Fix button touch targets - Ensure minimum 44px height */
  .btn-outline.btn-responsive,
  button[class*="btn-outline"],
  button[class*="btn-responsive"] {
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 10px 16px !important;
    touch-action: manipulation !important;
  }

  /* Ultra-narrow adjustments for buttons */
  @media (max-width: 377px) {
    .btn-outline.btn-responsive,
    button[class*="btn-outline"],
    button[class*="btn-responsive"] {
      min-height: 40px !important;
      padding: 8px 14px !important;
    }
  }

  @media (max-width: 351px) {
    .btn-outline.btn-responsive,
    button[class*="btn-outline"],
    button[class*="btn-responsive"] {
      min-height: 36px !important;
      padding: 6px 12px !important;
    }
  }

  /* Fix touch target spacing - Minimum 8px between interactive elements */
  .touch-target-spacing {
    margin: 4px !important;
  }

  .touch-target-spacing + .touch-target-spacing {
    margin-left: 8px !important;
  }

  /* Prevent touch target overlaps */
  .touch-target-no-overlap {
    position: relative !important;
    z-index: 1 !important;
  }

  .touch-target-no-overlap:focus,
  .touch-target-no-overlap:active {
    z-index: 2 !important;
  }

  /* Enhanced mobile-first responsive spacing */
  .mobile-padding {
    @apply px-4 sm:px-6 md:px-8 lg:px-12;
  }

  .mobile-margin {
    @apply mx-4 sm:mx-6 md:mx-8 lg:mx-12;
  }

  .mobile-gap {
    @apply gap-3 sm:gap-4 md:gap-6 lg:gap-8;
  }

  /* Modern grid system - Enhanced responsive grids */
  .grid-auto-fit {
    @apply grid gap-6 grid-cols-[repeat(auto-fit,minmax(280px,1fr))];
  }

  .grid-auto-fill {
    @apply grid gap-6 grid-cols-[repeat(auto-fill,minmax(280px,1fr))];
  }

  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 mobile-gap;
  }

  .grid-stats {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 mobile-gap;
  }

  /* Mobile-optimized card grids */
  .grid-mobile-cards {
    @apply grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 mobile-gap;
  }

  .grid-mobile-first {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 mobile-gap;
  }

  /* Mobile-friendly list layouts */
  .list-mobile {
    @apply space-y-3 sm:space-y-4;
  }

  .list-mobile-large {
    @apply space-y-4 sm:space-y-6;
  }

  /* Enhanced flex utilities for modern layouts */
  .flex-center {
    @apply flex items-center justify-center;
  }

  .flex-between {
    @apply flex items-center justify-between;
  }

  .flex-start {
    @apply flex items-center justify-start;
  }

  .flex-end {
    @apply flex items-center justify-end;
  }

  /* Modern section spacing */
  .section-spacing {
    @apply py-12 sm:py-16 lg:py-20;
  }

  .section-spacing-sm {
    @apply py-8 sm:py-12;
  }

  .section-spacing-lg {
    @apply py-16 sm:py-20 lg:py-24;
  }

  /* Enhanced interaction utilities */
  .hover-lift {
    @apply transition-transform duration-200 hover:scale-[1.02] hover:shadow-lg;
  }

  .hover-lift-sm {
    @apply transition-transform duration-200 hover:scale-[1.01] hover:shadow-md;
  }

  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2;
  }

  .focus-ring-accent {
    @apply focus:outline-none focus:ring-2 focus:ring-jobblogg-accent focus:ring-offset-2;
  }

  /* Modern card variants */
  .card-stats {
    @apply bg-white rounded-xl p-6 shadow-sm border border-jobblogg-border hover:shadow-md transition-all duration-200;
  }

  .card-interactive {
    @apply card-elevated cursor-pointer hover-lift focus-ring;
  }

  /* Layout utilities for modern design */
  .layout-stack {
    @apply space-y-6 sm:space-y-8;
  }

  .layout-stack-sm {
    @apply space-y-3 sm:space-y-4;
  }

  .layout-stack-lg {
    @apply space-y-8 sm:space-y-12;
  }

  /* Modern gradient system - Subtle and harmonious */
  .gradient-header {
    @apply bg-gradient-to-br from-jobblogg-primary to-jobblogg-accent;
  }

  .gradient-soft {
    @apply bg-gradient-to-br from-jobblogg-primary-soft to-jobblogg-accent-soft;
  }

  .gradient-blue-soft {
    @apply bg-gradient-to-br from-jobblogg-blue-50 to-jobblogg-indigo-50;
  }

  .gradient-card-hover {
    @apply bg-gradient-to-br from-jobblogg-blue-100 to-jobblogg-indigo-100;
  }

  .gradient-neutral-soft {
    @apply bg-gradient-to-br from-jobblogg-neutral to-jobblogg-neutral-light;
  }

  /* Enhanced Micro-interactions - Smooth and purposeful */
  .hover-lift {
    @apply transition-all duration-200 hover:-translate-y-0.5 hover:shadow-medium;
  }

  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
  }

  .hover-glow {
    @apply transition-all duration-300 hover:shadow-lg hover:shadow-jobblogg-primary/20;
  }

  .hover-bounce {
    @apply transition-transform duration-200 hover:scale-110 active:scale-95;
  }

  .hover-rotate {
    @apply transition-transform duration-300 hover:rotate-3;
  }

  .hover-slide-right {
    @apply transition-transform duration-200 hover:translate-x-1;
  }

  .focus-ring-enhanced {
    @apply focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2 focus:ring-offset-white transition-all duration-200;
  }

  .interactive-press {
    @apply active:scale-95 transition-transform duration-100;
  }

  /* Mobile-specific touch interactions */
  .touch-feedback {
    @apply active:scale-95 active:bg-jobblogg-primary-soft transition-all duration-150;
  }

  .touch-ripple {
    @apply relative overflow-hidden;
  }

  .touch-ripple::before {
    content: '';
    @apply absolute inset-0 bg-current opacity-0 scale-0 rounded-full transition-all duration-300;
  }

  .touch-ripple:active::before {
    @apply opacity-10 scale-100;
  }

  /* Mobile-optimized focus states */
  .mobile-focus {
    @apply focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2 focus:ring-offset-white;
  }

  /* Swipe gesture indicators */
  .swipe-indicator {
    @apply relative;
  }

  .swipe-indicator::after {
    content: '';
    @apply absolute -right-2 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-jobblogg-primary-light rounded-full opacity-30;
  }

  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2;
  }

  /* ===== ENHANCED FORM UTILITIES ===== */
  .form-field-group {
    @apply space-y-2;
  }

  .form-field-row {
    @apply flex flex-col sm:flex-row sm:items-center gap-4;
  }

  .form-field-inline {
    @apply flex items-center gap-3;
  }

  .form-section {
    @apply space-y-6 p-6 bg-white rounded-xl border border-jobblogg-border;
  }

  .form-section-header {
    @apply pb-4 border-b border-jobblogg-border mb-6;
  }

  .form-actions {
    @apply flex flex-col sm:flex-row gap-3 pt-6 border-t border-jobblogg-border;
  }

  .form-actions-right {
    @apply flex flex-col sm:flex-row gap-3 pt-6 border-t border-jobblogg-border sm:justify-end;
  }

  .form-grid {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6;
  }

  .form-grid-3 {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  /* File upload specific utilities */
  .file-drop-zone {
    @apply border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 cursor-pointer
           border-jobblogg-border hover:border-jobblogg-primary hover:bg-jobblogg-neutral;
  }

  .file-drop-zone-active {
    @apply border-jobblogg-primary bg-jobblogg-primary-soft scale-105;
  }

  .file-drop-zone-error {
    @apply border-jobblogg-error hover:border-jobblogg-error hover:bg-jobblogg-error-soft;
  }

  .file-preview-grid {
    @apply grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }

  .file-preview-single {
    @apply grid gap-4 grid-cols-1;
  }
}

@layer utilities {
  /* Enhanced custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.4s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.4s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }

  .animate-pulse-soft {
    animation: pulseSoft 2s ease-in-out infinite;
  }

  .animate-shake {
    animation: shake 0.5s ease-in-out;
  }

  .animate-success-bounce {
    animation: successBounce 0.6s ease-out;
  }

  .animate-loading-dots {
    animation: loadingDots 1.4s ease-in-out infinite;
  }

  .animate-slide-out-right {
    animation: slideOutRight 0.3s ease-in;
  }
}

/* Keyframes */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% {
    transform: translateY(10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0.95);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

@keyframes slideInLeft {
  0% {
    transform: translateX(-20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  0% {
    transform: translateX(20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulseSoft {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

@keyframes successBounce {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.1);
  }
  50% {
    transform: scale(0.95);
  }
  75% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* FAB Tooltip Animation */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.2s ease-out;
}

/* Dialog Scale Animation */
@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* Scale Up Animation for Dialogs */
@keyframes scaleUp {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scale-up {
  animation: scaleUp 0.2s ease-out;
}
