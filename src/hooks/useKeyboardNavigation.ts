import { useEffect, useCallback, useRef, useState } from 'react';

export interface KeyboardNavigationOptions {
  onEscape?: () => void;
  onEnter?: () => void;
  onArrowUp?: () => void;
  onArrowDown?: () => void;
  onArrowLeft?: () => void;
  onArrowRight?: () => void;
  onTab?: (shiftKey: boolean) => void;
  onSpace?: () => void;
  enabled?: boolean;
  preventDefault?: boolean;
}

/**
 * Custom hook for keyboard navigation in chat components
 */
export function useKeyboardNavigation(options: KeyboardNavigationOptions) {
  const {
    onEscape,
    onEnter,
    onArrowUp,
    onArrowDown,
    onArrowLeft,
    onArrowRight,
    onTab,
    onSpace,
    enabled = true,
    preventDefault = true
  } = options;

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    switch (event.key) {
      case 'Escape':
        if (onEscape) {
          if (preventDefault) event.preventDefault();
          onEscape();
        }
        break;
      case 'Enter':
        if (onEnter && !event.shiftKey) {
          if (preventDefault) event.preventDefault();
          onEnter();
        }
        break;
      case 'ArrowUp':
        if (onArrowUp) {
          if (preventDefault) event.preventDefault();
          onArrowUp();
        }
        break;
      case 'ArrowDown':
        if (onArrowDown) {
          if (preventDefault) event.preventDefault();
          onArrowDown();
        }
        break;
      case 'ArrowLeft':
        if (onArrowLeft) {
          if (preventDefault) event.preventDefault();
          onArrowLeft();
        }
        break;
      case 'ArrowRight':
        if (onArrowRight) {
          if (preventDefault) event.preventDefault();
          onArrowRight();
        }
        break;
      case 'Tab':
        if (onTab) {
          onTab(event.shiftKey);
        }
        break;
      case ' ':
        if (onSpace) {
          if (preventDefault) event.preventDefault();
          onSpace();
        }
        break;
    }
  }, [
    enabled,
    preventDefault,
    onEscape,
    onEnter,
    onArrowUp,
    onArrowDown,
    onArrowLeft,
    onArrowRight,
    onTab,
    onSpace
  ]);

  useEffect(() => {
    if (enabled) {
      document.addEventListener('keydown', handleKeyDown);
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [enabled, handleKeyDown]);

  return { handleKeyDown };
}

/**
 * Hook for managing focus within a container
 */
export function useFocusManagement() {
  const containerRef = useRef<HTMLDivElement>(null);
  const focusableElements = useRef<HTMLElement[]>([]);

  const updateFocusableElements = useCallback(() => {
    if (!containerRef.current) return;

    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'textarea:not([disabled])',
      'select:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])'
    ].join(', ');

    focusableElements.current = Array.from(
      containerRef.current.querySelectorAll(focusableSelectors)
    ) as HTMLElement[];
  }, []);

  const focusFirst = useCallback(() => {
    updateFocusableElements();
    if (focusableElements.current.length > 0) {
      focusableElements.current[0].focus();
    }
  }, [updateFocusableElements]);

  const focusLast = useCallback(() => {
    updateFocusableElements();
    if (focusableElements.current.length > 0) {
      focusableElements.current[focusableElements.current.length - 1].focus();
    }
  }, [updateFocusableElements]);

  const focusNext = useCallback(() => {
    updateFocusableElements();
    const currentIndex = focusableElements.current.indexOf(document.activeElement as HTMLElement);
    if (currentIndex >= 0 && currentIndex < focusableElements.current.length - 1) {
      focusableElements.current[currentIndex + 1].focus();
    }
  }, [updateFocusableElements]);

  const focusPrevious = useCallback(() => {
    updateFocusableElements();
    const currentIndex = focusableElements.current.indexOf(document.activeElement as HTMLElement);
    if (currentIndex > 0) {
      focusableElements.current[currentIndex - 1].focus();
    }
  }, [updateFocusableElements]);

  const trapFocus = useCallback((event: KeyboardEvent) => {
    if (event.key !== 'Tab') return;

    updateFocusableElements();
    if (focusableElements.current.length === 0) return;

    const firstElement = focusableElements.current[0];
    const lastElement = focusableElements.current[focusableElements.current.length - 1];

    if (event.shiftKey) {
      if (document.activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
    } else {
      if (document.activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  }, [updateFocusableElements]);

  return {
    containerRef,
    focusFirst,
    focusLast,
    focusNext,
    focusPrevious,
    trapFocus,
    updateFocusableElements
  };
}

/**
 * Hook for managing message list navigation with arrow keys
 */
export function useMessageListNavigation(messages: any[]) {
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const messageRefs = useRef<(HTMLElement | null)[]>([]);

  const selectMessage = useCallback((index: number) => {
    if (index >= 0 && index < messages.length) {
      setSelectedIndex(index);
      messageRefs.current[index]?.focus();
    }
  }, [messages.length]);

  const selectNext = useCallback(() => {
    const nextIndex = Math.min(selectedIndex + 1, messages.length - 1);
    selectMessage(nextIndex);
  }, [selectedIndex, messages.length, selectMessage]);

  const selectPrevious = useCallback(() => {
    const prevIndex = Math.max(selectedIndex - 1, 0);
    selectMessage(prevIndex);
  }, [selectedIndex, selectMessage]);

  const clearSelection = useCallback(() => {
    setSelectedIndex(-1);
  }, []);

  return {
    selectedIndex,
    messageRefs,
    selectMessage,
    selectNext,
    selectPrevious,
    clearSelection
  };
}
