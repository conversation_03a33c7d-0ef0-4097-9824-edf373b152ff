import { useState, useEffect, useCallback } from 'react';
import { useAction, useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { detectLinks } from '../utils/linkDetection';
import type { LinkPreviewData } from '../components/chat/LinkPreview';

interface LinkPreviewState {
  [url: string]: {
    data: LinkPreviewData | null;
    loading: boolean;
    error: string | null;
  };
}

export function useLinkPreviews() {
  const [previews, setPreviews] = useState<LinkPreviewState>({});

  const getOrFetchLinkPreview = useAction(api.linkPreviews.getOrFetchLinkPreview);

  const fetchPreview = useCallback(async (url: string) => {
    // Don't fetch if already loading or loaded
    if (previews[url]?.loading || previews[url]?.data) {
      return;
    }

    // Set loading state
    setPreviews(prev => ({
      ...prev,
      [url]: {
        data: null,
        loading: true,
        error: null
      }
    }));

    try {
      const result = await getOrFetchLinkPreview({ url });
      
      setPreviews(prev => ({
        ...prev,
        [url]: {
          data: result ? {
            url: result.url,
            title: result.title,
            description: result.description,
            image: result.image,
            siteName: result.siteName,
            type: result.type,
            domain: result.domain,
            favicon: result.favicon,
            fetchError: result.fetchError
          } : null,
          loading: false,
          error: result ? null : 'Failed to fetch link preview'
        }
      }));
    } catch (error) {
      console.error('Error fetching link preview:', error);
      setPreviews(prev => ({
        ...prev,
        [url]: {
          data: null,
          loading: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }));
    }
  }, [getOrFetchLinkPreview, previews]);

  const getPreviewsForText = useCallback((text: string) => {
    const links = detectLinks(text);
    const validLinks = links.filter(link => link.isValid);
    
    // Fetch previews for any new links
    validLinks.forEach(link => {
      if (!previews[link.url]) {
        fetchPreview(link.url);
      }
    });

    // Return current preview states for all valid links
    return validLinks.map(link => ({
      url: link.url,
      preview: previews[link.url] || { data: null, loading: false, error: null }
    }));
  }, [previews, fetchPreview]);

  const getPreview = useCallback((url: string) => {
    return previews[url] || { data: null, loading: false, error: null };
  }, [previews]);

  const prefetchPreview = useCallback((url: string) => {
    fetchPreview(url);
  }, [fetchPreview]);

  return {
    getPreviewsForText,
    getPreview,
    prefetchPreview,
    previews
  };
}

// Hook for getting a single link preview
export function useLinkPreview(url: string | null) {
  const [preview, setPreview] = useState<{
    data: LinkPreviewData | null;
    loading: boolean;
    error: string | null;
  }>({
    data: null,
    loading: false,
    error: null
  });

  const getOrFetchLinkPreview = useAction(api.linkPreviews.getOrFetchLinkPreview);

  useEffect(() => {
    if (!url) {
      setPreview({ data: null, loading: false, error: null });
      return;
    }

    let cancelled = false;

    const fetchPreview = async () => {
      setPreview(prev => ({ ...prev, loading: true, error: null }));

      try {
        const result = await getOrFetchLinkPreview({ url });
        
        if (!cancelled) {
          setPreview({
            data: result ? {
              url: result.url,
              title: result.title,
              description: result.description,
              image: result.image,
              siteName: result.siteName,
              type: result.type,
              domain: result.domain,
              favicon: result.favicon,
              fetchError: result.fetchError
            } : null,
            loading: false,
            error: result ? null : 'Failed to fetch link preview'
          });
        }
      } catch (error) {
        if (!cancelled) {
          console.error('Error fetching link preview:', error);
          setPreview({
            data: null,
            loading: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    };

    fetchPreview();

    return () => {
      cancelled = true;
    };
  }, [url, getOrFetchLinkPreview]);

  return preview;
}

// Hook for batch fetching multiple link previews
export function useBatchLinkPreviews(urls: string[]) {
  const [previews, setPreviews] = useState<Record<string, {
    data: LinkPreviewData | null;
    loading: boolean;
    error: string | null;
  }>>({});

  const getOrFetchLinkPreview = useAction(api.linkPreviews.getOrFetchLinkPreview);

  useEffect(() => {
    const validUrls = urls.filter(url => url && url.trim());
    
    if (validUrls.length === 0) {
      setPreviews({});
      return;
    }

    let cancelled = false;

    const fetchPreviews = async () => {
      // Initialize loading states
      const initialState: typeof previews = {};
      validUrls.forEach(url => {
        initialState[url] = { data: null, loading: true, error: null };
      });
      setPreviews(initialState);

      // Fetch all previews concurrently
      const promises = validUrls.map(async (url) => {
        try {
          const result = await getOrFetchLinkPreview({ url });
          return {
            url,
            data: result ? {
              url: result.url,
              title: result.title,
              description: result.description,
              image: result.image,
              siteName: result.siteName,
              type: result.type,
              domain: result.domain,
              favicon: result.favicon,
              fetchError: result.fetchError
            } : null,
            error: result ? null : 'Failed to fetch link preview'
          };
        } catch (error) {
          return {
            url,
            data: null,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      });

      const results = await Promise.all(promises);

      if (!cancelled) {
        const newPreviews: typeof previews = {};
        results.forEach(result => {
          newPreviews[result.url] = {
            data: result.data,
            loading: false,
            error: result.error
          };
        });
        setPreviews(newPreviews);
      }
    };

    fetchPreviews();

    return () => {
      cancelled = true;
    };
  }, [urls.join(','), getOrFetchLinkPreview]); // Use join to create stable dependency

  return previews;
}
