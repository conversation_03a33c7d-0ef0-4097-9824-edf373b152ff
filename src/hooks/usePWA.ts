import { useState, useEffect } from 'react';

interface PWAInstallPrompt extends Event {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

interface PWAState {
  isInstallable: boolean;
  isInstalled: boolean;
  isOnline: boolean;
  isUpdateAvailable: boolean;
  installPrompt: PWAInstallPrompt | null;
}

interface PWAActions {
  installApp: () => Promise<boolean>;
  updateApp: () => Promise<void>;
  registerForNotifications: () => Promise<boolean>;
}

export function usePWA(): PWAState & PWAActions {
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false);
  const [installPrompt, setInstallPrompt] = useState<PWAInstallPrompt | null>(null);
  const [serviceWorkerRegistration, setServiceWorkerRegistration] = useState<ServiceWorkerRegistration | null>(null);

  useEffect(() => {
    // Check if app is already installed
    const checkInstallStatus = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const isInWebAppiOS = (window.navigator as any).standalone === true;
      setIsInstalled(isStandalone || isInWebAppiOS);
    };

    checkInstallStatus();

    // Listen for install prompt
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setInstallPrompt(e as PWAInstallPrompt);
      setIsInstallable(true);
    };

    // Listen for app installed
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setIsInstallable(false);
      setInstallPrompt(null);
      console.log('[PWA] App installed successfully');
    };

    // Listen for online/offline status
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    // Register event listeners
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Register service worker
    registerServiceWorker();

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const registerServiceWorker = async () => {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/'
        });

        setServiceWorkerRegistration(registration);

        // Check for updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                setIsUpdateAvailable(true);
                console.log('[PWA] New version available');
              }
            });
          }
        });

        // Listen for controlling service worker changes
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          window.location.reload();
        });

        console.log('[PWA] Service Worker registered successfully');
      } catch (error) {
        console.error('[PWA] Service Worker registration failed:', error instanceof Error ? error.message : String(error));
      }
    }
  };

  const installApp = async (): Promise<boolean> => {
    if (!installPrompt) {
      console.warn('[PWA] No install prompt available');
      return false;
    }

    try {
      await installPrompt.prompt();
      const choiceResult = await installPrompt.userChoice;
      
      if (choiceResult.outcome === 'accepted') {
        console.log('[PWA] User accepted the install prompt');
        setIsInstallable(false);
        setInstallPrompt(null);
        return true;
      } else {
        console.log('[PWA] User dismissed the install prompt');
        return false;
      }
    } catch (error) {
      console.error('[PWA] Install failed:', error instanceof Error ? error.message : String(error));
      return false;
    }
  };

  const updateApp = async (): Promise<void> => {
    if (!serviceWorkerRegistration) {
      console.warn('[PWA] No service worker registration available');
      return;
    }

    try {
      const waitingWorker = serviceWorkerRegistration.waiting;
      if (waitingWorker) {
        waitingWorker.postMessage({ type: 'SKIP_WAITING' });
        setIsUpdateAvailable(false);
      }
    } catch (error) {
      console.error('[PWA] Update failed:', error);
    }
  };

  const registerForNotifications = async (): Promise<boolean> => {
    if (!('Notification' in window) || !serviceWorkerRegistration) {
      console.warn('[PWA] Notifications not supported');
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      
      if (permission === 'granted') {
        console.log('[PWA] Notification permission granted');
        
        // Subscribe to push notifications (if needed)
        const subscription = await serviceWorkerRegistration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: urlBase64ToUint8Array(
            // Add your VAPID public key here
            'YOUR_VAPID_PUBLIC_KEY'
          )
        });

        console.log('[PWA] Push subscription created:', subscription);
        return true;
      } else {
        console.log('[PWA] Notification permission denied');
        return false;
      }
    } catch (error) {
      console.error('[PWA] Notification registration failed:', error);
      return false;
    }
  };

  return {
    // State
    isInstallable,
    isInstalled,
    isOnline,
    isUpdateAvailable,
    installPrompt,
    
    // Actions
    installApp,
    updateApp,
    registerForNotifications
  };
}

// Utility function for VAPID key conversion
function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

// PWA utility functions
export const PWAUtils = {
  // Check if running as PWA
  isPWA: (): boolean => {
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
    const isInWebAppiOS = (window.navigator as any).standalone === true;
    return isStandalone || isInWebAppiOS;
  },

  // Get install instructions based on browser
  getInstallInstructions: (): string => {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (userAgent.includes('chrome') && !userAgent.includes('edg')) {
      return 'Trykk på meny-ikonet (⋮) og velg "Installer JobbLogg"';
    } else if (userAgent.includes('firefox')) {
      return 'Trykk på adresselinjen og velg "Installer denne siden som app"';
    } else if (userAgent.includes('safari')) {
      return 'Trykk på Del-knappen og velg "Legg til på hjemskjerm"';
    } else if (userAgent.includes('edg')) {
      return 'Trykk på meny-ikonet (⋯) og velg "Installer denne siden som app"';
    } else {
      return 'Se nettleserens meny for å installere som app';
    }
  },

  // Share content using Web Share API
  shareContent: async (data: { title: string; text: string; url?: string }): Promise<boolean> => {
    if (navigator.share) {
      try {
        await navigator.share(data);
        return true;
      } catch (error) {
        console.error('[PWA] Share failed:', error);
        return false;
      }
    } else {
      // Fallback to clipboard
      try {
        const shareText = `${data.title}\n${data.text}${data.url ? `\n${data.url}` : ''}`;
        await navigator.clipboard.writeText(shareText);
        return true;
      } catch (error) {
        console.error('[PWA] Clipboard fallback failed:', error);
        return false;
      }
    }
  }
};
