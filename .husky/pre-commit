#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# JobbLogg Pre-commit Hook
# Prevents commits with import/module resolution issues

echo "🔍 Running JobbLogg pre-commit validation..."

# Run import validation
echo "📦 Validating imports..."
npm run validate:imports
if [ $? -ne 0 ]; then
  echo "❌ Import validation failed. Please fix import issues before committing."
  exit 1
fi

# Run TypeScript check
echo "🔧 Checking TypeScript..."
npm run type-check
if [ $? -ne 0 ]; then
  echo "❌ TypeScript check failed. Please fix type errors before committing."
  exit 1
fi

# Run linting with JobbLogg rules
echo "🧹 Running linter..."
npm run lint:jobblogg
if [ $? -ne 0 ]; then
  echo "❌ Linting failed. Please fix linting errors before committing."
  exit 1
fi

echo "✅ All pre-commit checks passed!"
