<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Unread Comments - JobbLogg</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #1f2937;
            margin-bottom: 8px;
        }
        .subtitle {
            color: #6b7280;
            margin-bottom: 24px;
        }
        .form-group {
            margin-bottom: 16px;
        }
        label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        textarea {
            min-height: 80px;
            resize: vertical;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        button:hover {
            background: #1d4ed8;
        }
        button.secondary {
            background: #6b7280;
        }
        button.secondary:hover {
            background: #4b5563;
        }
        .result {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 12px;
            margin-top: 16px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 13px;
        }
        .result.error {
            background: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
        }
        .result.success {
            background: #f0fdf4;
            border-color: #bbf7d0;
            color: #16a34a;
        }
        .step {
            background: #eff6ff;
            border-left: 4px solid #2563eb;
            padding: 16px;
            margin: 16px 0;
        }
        .step h3 {
            margin: 0 0 8px 0;
            color: #1e40af;
        }
        .links {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-top: 16px;
        }
        .links a {
            color: #2563eb;
            text-decoration: none;
            padding: 8px 12px;
            border: 1px solid #2563eb;
            border-radius: 6px;
            font-size: 14px;
        }
        .links a:hover {
            background: #2563eb;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Unread Comments Functionality</h1>
        <p class="subtitle">Test the complete unread comments workflow in JobbLogg</p>

        <div class="step">
            <h3>Step 1: Create Test Comment</h3>
            <p>First, create a test customer comment to simulate unread functionality.</p>
            
            <div class="form-group">
                <label for="sharedId">Shared Project ID</label>
                <input type="text" id="sharedId" placeholder="Enter shared project ID (e.g., abc123def)" />
                <small style="color: #6b7280;">Get this from a shared project URL: /shared/[sharedId]</small>
            </div>
            
            <div class="form-group">
                <label for="customerName">Customer Name</label>
                <input type="text" id="customerName" value="Test Kunde" />
            </div>
            
            <div class="form-group">
                <label for="customerEmail">Customer Email</label>
                <input type="email" id="customerEmail" value="<EMAIL>" />
            </div>
            
            <div class="form-group">
                <label for="commentMessage">Comment Message</label>
                <textarea id="commentMessage" placeholder="Enter test comment message...">Hei! Dette er en test kommentar for å teste uleste kommentarer funksjonalitet. Kan du bekrefte at du har mottatt denne meldingen?</textarea>
            </div>
            
            <button onclick="createTestComment()">Create Test Comment</button>
            <button class="secondary" onclick="openSharedProject()">Open Shared Project</button>
        </div>

        <div class="step">
            <h3>Step 2: Check Dashboard</h3>
            <p>After creating a comment, check if it appears on the contractor dashboard.</p>
            
            <button onclick="openDashboard()">Open Dashboard</button>
            <button class="secondary" onclick="openUnreadComments()">Open Unread Comments Page</button>
        </div>

        <div class="step">
            <h3>Step 3: Test Read Status</h3>
            <p>Test the automatic read detection and manual read marking.</p>
            
            <button onclick="testReadDetection()">Test Auto Read Detection</button>
            <button class="secondary" onclick="checkReadStatus()">Check Read Status</button>
        </div>

        <div class="links">
            <a href="http://localhost:5173" target="_blank">Dashboard</a>
            <a href="http://localhost:5173/unread-comments" target="_blank">Unread Comments</a>
            <a href="http://localhost:5173/create" target="_blank">Create Project</a>
        </div>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function showResult(message, isError = false, isSuccess = false) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.style.display = 'block';
            result.className = 'result';
            if (isError) result.className += ' error';
            if (isSuccess) result.className += ' success';
        }

        function createTestComment() {
            const sharedId = document.getElementById('sharedId').value.trim();
            const customerName = document.getElementById('customerName').value.trim();
            const customerEmail = document.getElementById('customerEmail').value.trim();
            const message = document.getElementById('commentMessage').value.trim();

            if (!sharedId) {
                showResult('❌ Please enter a shared project ID', true);
                return;
            }

            if (!customerName || !message) {
                showResult('❌ Please fill in customer name and message', true);
                return;
            }

            // Open shared project page to create comment
            const sharedUrl = `http://localhost:5173/shared/${sharedId}`;
            window.open(sharedUrl, '_blank');
            
            showResult(`✅ Opening shared project page: ${sharedUrl}\n\nManually create a comment using the form on that page with:\n- Name: ${customerName}\n- Email: ${customerEmail}\n- Message: ${message}`, false, true);
        }

        function openSharedProject() {
            const sharedId = document.getElementById('sharedId').value.trim();
            if (!sharedId) {
                showResult('❌ Please enter a shared project ID first', true);
                return;
            }
            
            const sharedUrl = `http://localhost:5173/shared/${sharedId}`;
            window.open(sharedUrl, '_blank');
            showResult(`✅ Opened shared project: ${sharedUrl}`, false, true);
        }

        function openDashboard() {
            window.open('http://localhost:5173', '_blank');
            showResult('✅ Opened dashboard. Check for "Uleste kommentarer" card.', false, true);
        }

        function openUnreadComments() {
            window.open('http://localhost:5173/unread-comments', '_blank');
            showResult('✅ Opened unread comments page. Check if your test comment appears.', false, true);
        }

        function testReadDetection() {
            showResult(`🔄 To test automatic read detection:

1. Create a comment using Step 1
2. Open the dashboard - you should see "Uleste kommentarer" card with count
3. Click on the unread comments card to go to /unread-comments
4. The comment should automatically be marked as read when you view it
5. Return to dashboard - the count should decrease

Check browser console for any automatic read detection logs.`, false, true);
        }

        function checkReadStatus() {
            showResult(`📊 To check read status:

1. Open browser developer tools (F12)
2. Go to Console tab
3. Look for logs related to:
   - "autoMarkAsReadByContractor"
   - "Comment marked as read"
   - Read status updates

4. Check the dashboard unread count before and after viewing comments
5. Verify the unread comments page shows/hides comments correctly`, false, true);
        }

        // Auto-populate with example shared ID if available
        window.addEventListener('load', () => {
            // You can set a default shared ID here if you have one for testing
            // document.getElementById('sharedId').value = 'your-test-shared-id';
        });
    </script>
</body>
</html>
