<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JobbLogg Layout Overflow Test</title>
    <style>
        body {
            font-family: 'Inter', system-ui, sans-serif;
            margin: 0;
            padding: 0;
            background: white;
            overflow-x: hidden;
            min-width: 320px;
        }
        
        .test-container {
            max-width: 100vw;
            overflow-x: hidden;
            padding: 16px;
        }
        
        .test-section {
            margin-bottom: 24px;
            padding: 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #1f2937;
        }
        
        .test-description {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 16px;
        }
        
        /* Test cases for different screen widths */
        .width-320 {
            width: 320px;
            background: #fef3c7;
            padding: 8px;
            margin: 8px 0;
            border-radius: 4px;
        }
        
        .width-272 {
            width: 272px;
            background: #fecaca;
            padding: 8px;
            margin: 8px 0;
            border-radius: 4px;
        }
        
        .responsive-container {
            width: 100%;
            max-width: 28rem; /* max-w-md equivalent */
            background: #dcfce7;
            padding: 8px;
            margin: 8px 0;
            border-radius: 4px;
        }
        
        .ultra-narrow-safe {
            width: 100%;
            max-width: calc(100vw - 1rem);
            background: #dbeafe;
            padding: 8px;
            margin: 8px 0;
            border-radius: 4px;
        }
        
        /* Ultra-narrow screen adjustments */
        @media (max-width: 351px) {
            .responsive-container {
                max-width: calc(100vw - 1rem);
            }
            
            .test-container {
                padding: 8px;
            }
            
            .test-section {
                padding: 12px;
            }
        }
        
        .viewport-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #1f2937;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="viewport-info" id="viewport-info">
        Viewport: <span id="viewport-width"></span>px
    </div>
    
    <div class="test-container">
        <h1 style="color: #2563eb; margin-bottom: 24px;">JobbLogg Layout Overflow Test</h1>
        
        <div class="test-section">
            <div class="test-title">🔴 Fixed Width Elements (Should Cause Overflow)</div>
            <div class="test-description">These elements have fixed widths that cause horizontal scrolling on narrow screens:</div>
            
            <div class="width-320">Fixed 320px width (html, body, div#root)</div>
            <div class="width-272">Fixed 272px width (error messages, headings)</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">🟡 Standard Responsive (May Overflow on Ultra-Narrow)</div>
            <div class="test-description">Standard max-w-md (28rem = 448px) containers:</div>
            
            <div class="responsive-container">max-w-md container (Sign-in/Sign-up pages)</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">🟢 Ultra-Narrow Safe (Fixed)</div>
            <div class="test-description">Containers with ultra-narrow screen fixes:</div>
            
            <div class="ultra-narrow-safe">max-w-[calc(100vw-1rem)] container (Fixed)</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">📱 Test Instructions</div>
            <div class="test-description">
                1. Resize browser window to 320px width<br>
                2. Check for horizontal scrolling<br>
                3. Test at 351px and below (ultra-narrow breakpoint)<br>
                4. Red elements should cause overflow (before fixes)<br>
                5. Green elements should never cause overflow (after fixes)
            </div>
        </div>
    </div>
    
    <script>
        function updateViewportInfo() {
            const width = window.innerWidth;
            document.getElementById('viewport-width').textContent = width;
            
            // Add visual indicator for ultra-narrow screens
            const info = document.getElementById('viewport-info');
            if (width <= 351) {
                info.style.background = '#dc2626'; // Red for ultra-narrow
                info.innerHTML = `Ultra-Narrow: ${width}px`;
            } else if (width <= 377) {
                info.style.background = '#f59e0b'; // Amber for narrow
                info.innerHTML = `Narrow: ${width}px`;
            } else {
                info.style.background = '#1f2937'; // Default
                info.innerHTML = `Viewport: ${width}px`;
            }
        }
        
        updateViewportInfo();
        window.addEventListener('resize', updateViewportInfo);
    </script>
</body>
</html>
