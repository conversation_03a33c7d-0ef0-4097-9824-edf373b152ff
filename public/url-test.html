<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Maps URL Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .url-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
        }
        .test-image {
            border: 2px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Google Maps URL Test</h1>
        <p>This page tests the exact URLs being generated for Google Maps Static API calls.</p>
        
        <div id="api-key-info"></div>
        
        <h2>Test Address</h2>
        <p><strong>Address:</strong> Nørvegata 34E, 6008 Ålesund</p>
        
        <div id="url-tests"></div>
        
        <h2>Manual URL Test</h2>
        <button onclick="testManualUrl()">Test Manual URL</button>
        <div id="manual-test"></div>
    </div>

    <script>
        // API Key from environment (this won't work in static HTML, but we'll show the concept)
        const API_KEY = 'AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs'; // Your actual API key
        
        // Test address
        const testAddress = {
            street: 'Nørvegata 34E',
            postal: '6008',
            city: 'Ålesund'
        };

        // Format address for maps (same logic as React app)
        function formatAddressForMaps(streetAddress, postalCode, city) {
            return `${streetAddress}, ${postalCode} ${city}, Norge`;
        }

        // Generate static map URL (same logic as React app)
        function generateStaticMapUrl(streetAddress, postalCode, city, options = {}) {
            const {
                width = 400,
                height = 200,
                zoom = 15,
                mapType = 'hybrid'
            } = options;

            const address = formatAddressForMaps(streetAddress, postalCode, city);
            
            const params = new URLSearchParams({
                center: address,
                zoom: zoom.toString(),
                size: `${width}x${height}`,
                maptype: mapType,
                markers: `color:red|${address}`,
                key: API_KEY
            });

            return `https://maps.googleapis.com/maps/api/staticmap?${params.toString()}`;
        }

        // Display API key info
        function displayApiKeyInfo() {
            const apiKeyDiv = document.getElementById('api-key-info');
            apiKeyDiv.innerHTML = `
                <div class="success">
                    <strong>API Key:</strong> ${API_KEY.substring(0, 10)}... (Length: ${API_KEY.length})
                </div>
            `;
        }

        // Test different URL configurations
        function testUrls() {
            const urlTestsDiv = document.getElementById('url-tests');
            
            const tests = [
                { name: 'Default (Hybrid)', options: {} },
                { name: 'Roadmap', options: { mapType: 'roadmap' } },
                { name: 'Satellite', options: { mapType: 'satellite' } },
                { name: 'Terrain', options: { mapType: 'terrain' } },
                { name: 'Large Size', options: { width: 600, height: 400 } },
                { name: 'High Zoom', options: { zoom: 18 } }
            ];

            let html = '<h2>URL Generation Tests</h2>';
            
            tests.forEach((test, index) => {
                const url = generateStaticMapUrl(
                    testAddress.street, 
                    testAddress.postal, 
                    testAddress.city, 
                    test.options
                );
                
                html += `
                    <div class="container">
                        <h3>${test.name}</h3>
                        <div class="url-box">${url}</div>
                        <img 
                            src="${url}" 
                            alt="${test.name} Map" 
                            class="test-image"
                            style="width: ${test.options.width || 400}px; height: ${test.options.height || 200}px;"
                            onload="console.log('✅ ${test.name} loaded successfully')"
                            onerror="console.error('❌ ${test.name} failed to load'); this.style.border='2px solid red';"
                        />
                    </div>
                `;
            });
            
            urlTestsDiv.innerHTML = html;
        }

        // Test manual URL construction
        function testManualUrl() {
            const manualTestDiv = document.getElementById('manual-test');
            
            // Construct URL step by step
            const baseUrl = 'https://maps.googleapis.com/maps/api/staticmap';
            const address = formatAddressForMaps(testAddress.street, testAddress.postal, testAddress.city);
            
            console.log('🔧 Manual URL Construction:');
            console.log('Base URL:', baseUrl);
            console.log('Formatted Address:', address);
            console.log('API Key:', API_KEY.substring(0, 10) + '...');
            
            const params = new URLSearchParams();
            params.set('center', address);
            params.set('zoom', '15');
            params.set('size', '400x200');
            params.set('maptype', 'hybrid');
            params.set('markers', `color:red|${address}`);
            params.set('key', API_KEY);
            
            const finalUrl = `${baseUrl}?${params.toString()}`;
            
            console.log('Final URL:', finalUrl);
            
            manualTestDiv.innerHTML = `
                <div class="container">
                    <h3>Manual URL Construction</h3>
                    <div class="url-box">${finalUrl}</div>
                    <img 
                        src="${finalUrl}" 
                        alt="Manual Test Map" 
                        class="test-image"
                        style="width: 400px; height: 200px;"
                        onload="console.log('✅ Manual URL loaded successfully')"
                        onerror="console.error('❌ Manual URL failed to load'); this.style.border='2px solid red';"
                    />
                    <button onclick="testApiDirectly('${finalUrl}')">Test API Response</button>
                </div>
            `;
        }

        // Test API response directly
        async function testApiDirectly(url) {
            console.log('🧪 Testing API response for:', url);
            
            try {
                const response = await fetch(url);
                console.log('📡 API Response:', {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    url: response.url
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('❌ API Error Response:', errorText);
                    alert(`API Error: ${response.status} ${response.statusText}\n\nCheck console for details.`);
                } else {
                    console.log('✅ API call successful');
                    alert('✅ API call successful! Check console for details.');
                }
            } catch (error) {
                console.error('❌ Fetch Error:', error);
                alert(`Fetch Error: ${error.message}`);
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            displayApiKeyInfo();
            testUrls();
        });
    </script>
</body>
</html>
