import { promises as fs } from 'fs';
import path from 'path';
import { glob } from 'glob';
import { AuditResult, AutoFixSuggestion, TouchTargetIssue, LayoutIssue, AccessibilityIssue } from './types.js';
import { JOBBLOGG_PATTERNS, ACCESSIBILITY_THRESHOLDS } from './config.js';

/**
 * Auto Fix Generator
 * Generates code patches for common mobile responsiveness issues
 */

export class AutoFixGenerator {
  private sourceDir: string;
  private outputDir: string;

  constructor(sourceDir: string = '../../../src', outputDir: string = './fixes') {
    this.sourceDir = sourceDir;
    this.outputDir = outputDir;
  }

  /**
   * Generate fix suggestions from audit results
   */
  async generateFixSuggestions(results: AuditResult[]): Promise<AutoFixSuggestion[]> {
    const suggestions: AutoFixSuggestion[] = [];

    for (const result of results) {
      // Generate touch target fixes
      const touchTargetFixes = await this.generateTouchTargetFixes(result.touchTargetIssues, result.url);
      suggestions.push(...touchTargetFixes);

      // Generate layout fixes
      const layoutFixes = await this.generateLayoutFixes(result.layoutIssues, result.url);
      suggestions.push(...layoutFixes);

      // Generate accessibility fixes
      const accessibilityFixes = await this.generateAccessibilityFixes(result.accessibilityIssues, result.url);
      suggestions.push(...accessibilityFixes);

      // Generate JobbLogg token fixes
      if (!result.usesJobbLoggTokens) {
        const tokenFixes = await this.generateTokenFixes(result.url);
        suggestions.push(...tokenFixes);
      }

      // Generate responsive breakpoint fixes
      if (!result.hasResponsiveBreakpoints) {
        const responsiveFixes = await this.generateResponsiveFixes(result.url);
        suggestions.push(...responsiveFixes);
      }
    }

    return suggestions;
  }

  /**
   * Generate fixes for touch target issues
   */
  private async generateTouchTargetFixes(issues: TouchTargetIssue[], routeUrl: string): Promise<AutoFixSuggestion[]> {
    const suggestions: AutoFixSuggestion[] = [];

    for (const issue of issues) {
      if (issue.type === 'touch_target_too_small') {
        const componentFiles = await this.findComponentFiles(issue.selector);
        
        for (const file of componentFiles) {
          const content = await fs.readFile(file, 'utf-8');
          const lines = content.split('\n');
          
          // Find the line with the problematic element
          const lineIndex = this.findElementLine(lines, issue.selector);
          
          if (lineIndex !== -1) {
            const line = lines[lineIndex];
            const updatedLine = this.addTouchTargetClass(line, issue.dimensions);
            
            suggestions.push({
              type: 'add_class',
              file: file.replace(this.sourceDir + '/', ''),
              line: lineIndex + 1,
              column: 0,
              original: line.trim(),
              suggested: updatedLine.trim(),
              description: `Add touch-target class to ensure ${ACCESSIBILITY_THRESHOLDS.MIN_TOUCH_TARGET}x${ACCESSIBILITY_THRESHOLDS.MIN_TOUCH_TARGET}px minimum size`,
              confidence: 'high'
            });
          }
        }
      }
    }

    return suggestions;
  }

  /**
   * Generate fixes for layout issues
   */
  private async generateLayoutFixes(issues: LayoutIssue[], routeUrl: string): Promise<AutoFixSuggestion[]> {
    const suggestions: AutoFixSuggestion[] = [];

    for (const issue of issues) {
      const componentFiles = await this.findComponentFiles(issue.selector);
      
      for (const file of componentFiles) {
        const content = await fs.readFile(file, 'utf-8');
        const lines = content.split('\n');
        
        const lineIndex = this.findElementLine(lines, issue.selector);
        
        if (lineIndex !== -1) {
          const line = lines[lineIndex];
          let updatedLine = line;
          let description = '';
          
          switch (issue.type) {
            case 'horizontal_overflow':
              updatedLine = this.addOverflowFix(line);
              description = 'Add max-w-full and overflow-hidden to prevent horizontal scrolling';
              break;
              
            case 'fixed_width_element':
              updatedLine = this.replaceFixedWidth(line);
              description = 'Replace fixed width with responsive width classes';
              break;
              
            case 'responsive_missing':
              updatedLine = this.addResponsiveClasses(line);
              description = 'Add responsive breakpoint classes for mobile adaptation';
              break;
          }
          
          if (updatedLine !== line) {
            suggestions.push({
              type: 'modify_class',
              file: file.replace(this.sourceDir + '/', ''),
              line: lineIndex + 1,
              column: 0,
              original: line.trim(),
              suggested: updatedLine.trim(),
              description,
              confidence: 'medium'
            });
          }
        }
      }
    }

    return suggestions;
  }

  /**
   * Generate fixes for accessibility issues
   */
  private async generateAccessibilityFixes(issues: AccessibilityIssue[], routeUrl: string): Promise<AutoFixSuggestion[]> {
    const suggestions: AutoFixSuggestion[] = [];

    for (const issue of issues) {
      const componentFiles = await this.findComponentFiles(issue.selector);
      
      for (const file of componentFiles) {
        const content = await fs.readFile(file, 'utf-8');
        const lines = content.split('\n');
        
        const lineIndex = this.findElementLine(lines, issue.selector);
        
        if (lineIndex !== -1) {
          const line = lines[lineIndex];
          let updatedLine = line;
          let description = '';
          
          switch (issue.type) {
            case 'contrast_ratio':
              updatedLine = this.fixContrastRatio(line);
              description = 'Replace colors with high-contrast JobbLogg design tokens';
              break;
              
            case 'font_size':
              updatedLine = this.increaseFontSize(line);
              description = `Increase font size to minimum ${ACCESSIBILITY_THRESHOLDS.MIN_FONT_SIZE}px`;
              break;
              
            case 'missing_alt':
              updatedLine = this.addAltText(line);
              description = 'Add descriptive alt text for screen readers';
              break;
          }
          
          if (updatedLine !== line) {
            suggestions.push({
              type: 'modify_class',
              file: file.replace(this.sourceDir + '/', ''),
              line: lineIndex + 1,
              column: 0,
              original: line.trim(),
              suggested: updatedLine.trim(),
              description,
              confidence: 'medium'
            });
          }
        }
      }
    }

    return suggestions;
  }

  /**
   * Generate fixes to replace hardcoded colors with JobbLogg tokens
   */
  private async generateTokenFixes(routeUrl: string): Promise<AutoFixSuggestion[]> {
    const suggestions: AutoFixSuggestion[] = [];
    const componentFiles = await this.findAllComponentFiles();
    
    for (const file of componentFiles) {
      const content = await fs.readFile(file, 'utf-8');
      const lines = content.split('\n');
      
      lines.forEach((line, index) => {
        const updatedLine = this.replaceHardcodedColors(line);
        
        if (updatedLine !== line) {
          suggestions.push({
            type: 'modify_class',
            file: file.replace(this.sourceDir + '/', ''),
            line: index + 1,
            column: 0,
            original: line.trim(),
            suggested: updatedLine.trim(),
            description: 'Replace hardcoded colors with JobbLogg design tokens',
            confidence: 'high'
          });
        }
      });
    }

    return suggestions;
  }

  /**
   * Generate fixes to add responsive breakpoints
   */
  private async generateResponsiveFixes(routeUrl: string): Promise<AutoFixSuggestion[]> {
    const suggestions: AutoFixSuggestion[] = [];
    const componentFiles = await this.findAllComponentFiles();
    
    for (const file of componentFiles) {
      const content = await fs.readFile(file, 'utf-8');
      const lines = content.split('\n');
      
      lines.forEach((line, index) => {
        if (this.needsResponsiveClasses(line)) {
          const updatedLine = this.addResponsiveVariants(line);
          
          if (updatedLine !== line) {
            suggestions.push({
              type: 'add_responsive_variant',
              file: file.replace(this.sourceDir + '/', ''),
              line: index + 1,
              column: 0,
              original: line.trim(),
              suggested: updatedLine.trim(),
              description: 'Add responsive variants using JobbLogg breakpoints',
              confidence: 'medium'
            });
          }
        }
      });
    }

    return suggestions;
  }

  /**
   * Find component files that might contain the problematic selector
   */
  private async findComponentFiles(selector: string): Promise<string[]> {
    const allFiles = await this.findAllComponentFiles();
    const relevantFiles: string[] = [];
    
    // Extract element type and classes from selector
    const elementType = selector.split(/[#.]/)[0];
    const classes = selector.match(/\.[\w-]+/g)?.map(c => c.substring(1)) || [];
    
    for (const file of allFiles) {
      const content = await fs.readFile(file, 'utf-8');
      
      // Check if file contains the element type and classes
      const hasElementType = content.includes(`<${elementType}`) || content.includes(`${elementType}>`);
      const hasClasses = classes.some(cls => content.includes(cls));
      
      if (hasElementType || hasClasses) {
        relevantFiles.push(file);
      }
    }
    
    return relevantFiles;
  }

  /**
   * Find all component files in the source directory
   */
  private async findAllComponentFiles(): Promise<string[]> {
    const patterns = [
      path.join(this.sourceDir, '**/*.tsx'),
      path.join(this.sourceDir, '**/*.jsx'),
      path.join(this.sourceDir, '**/*.ts'),
      path.join(this.sourceDir, '**/*.js')
    ];
    
    const files: string[] = [];
    
    for (const pattern of patterns) {
      const matches = await glob(pattern);
      files.push(...matches);
    }
    
    return files.filter(file => 
      !file.includes('node_modules') && 
      !file.includes('.test.') && 
      !file.includes('.spec.')
    );
  }

  /**
   * Find the line number containing a specific element
   */
  private findElementLine(lines: string[], selector: string): number {
    const elementType = selector.split(/[#.]/)[0];
    const id = selector.match(/#([\w-]+)/)?.[1];
    const classes = selector.match(/\.[\w-]+/g)?.map(c => c.substring(1)) || [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      if (line.includes(`<${elementType}`)) {
        if (id && line.includes(`id="${id}"`)) return i;
        if (classes.some(cls => line.includes(cls))) return i;
        if (!id && classes.length === 0) return i;
      }
    }
    
    return -1;
  }

  /**
   * Add touch target class to a line
   */
  private addTouchTargetClass(line: string, dimensions: { width: number; height: number }): string {
    const minDimension = Math.min(dimensions.width, dimensions.height);
    const touchClass = minDimension < 36 ? 'touch-target' : 'touch-target-compact';
    
    // Find className attribute and add touch target class
    const classNameMatch = line.match(/className="([^"]*)"/);
    
    if (classNameMatch) {
      const existingClasses = classNameMatch[1];
      const newClasses = `${existingClasses} ${touchClass}`.trim();
      return line.replace(/className="[^"]*"/, `className="${newClasses}"`);
    } else {
      // Add className attribute
      return line.replace(/(<\w+)/, `$1 className="${touchClass}"`);
    }
  }

  /**
   * Add overflow fix classes
   */
  private addOverflowFix(line: string): string {
    return this.addClasses(line, ['max-w-full', 'overflow-hidden']);
  }

  /**
   * Replace fixed width with responsive width
   */
  private replaceFixedWidth(line: string): string {
    return line
      .replace(/w-\d+/g, 'w-full')
      .replace(/min-w-\d+/g, 'min-w-0')
      .replace(/max-w-\d+/g, 'max-w-full');
  }

  /**
   * Add responsive classes
   */
  private addResponsiveClasses(line: string): string {
    // Add basic responsive variants for common properties
    let updatedLine = line;
    
    // Add responsive padding
    updatedLine = updatedLine.replace(/\bp-(\d+)/g, 'p-$1 sm:p-' + (parseInt('$1') + 1));
    
    // Add responsive text sizes
    updatedLine = updatedLine.replace(/\btext-(xs|sm|base|lg|xl)/g, 'text-$1 sm:text-lg');
    
    return updatedLine;
  }

  /**
   * Fix contrast ratio by replacing colors
   */
  private fixContrastRatio(line: string): string {
    const colorReplacements = {
      'text-gray-400': 'text-jobblogg-text-medium',
      'text-gray-500': 'text-jobblogg-text-medium',
      'text-gray-600': 'text-jobblogg-text-strong',
      'bg-blue-500': 'bg-jobblogg-primary',
      'bg-blue-600': 'bg-jobblogg-primary',
      'text-blue-500': 'text-jobblogg-primary',
      'text-blue-600': 'text-jobblogg-primary'
    };
    
    let updatedLine = line;
    Object.entries(colorReplacements).forEach(([old, replacement]) => {
      updatedLine = updatedLine.replace(new RegExp(old, 'g'), replacement);
    });
    
    return updatedLine;
  }

  /**
   * Increase font size
   */
  private increaseFontSize(line: string): string {
    const sizeMap = {
      'text-xs': 'text-sm',
      'text-sm': 'text-base',
      'text-base': 'text-lg'
    };
    
    let updatedLine = line;
    Object.entries(sizeMap).forEach(([old, replacement]) => {
      updatedLine = updatedLine.replace(new RegExp(old, 'g'), replacement);
    });
    
    return updatedLine;
  }

  /**
   * Add alt text to images
   */
  private addAltText(line: string): string {
    if (line.includes('<img') && !line.includes('alt=')) {
      return line.replace(/(<img[^>]*)(\/?>)/, '$1 alt="Beskrivende tekst"$2');
    }
    return line;
  }

  /**
   * Replace hardcoded colors with JobbLogg tokens
   */
  private replaceHardcodedColors(line: string): string {
    const tokenReplacements = {
      '#1D4ED8': 'jobblogg-primary',
      '#2563EB': 'jobblogg-primary',
      '#10B981': 'jobblogg-success',
      '#FBBF24': 'jobblogg-warning',
      '#DC2626': 'jobblogg-error',
      'blue-600': 'jobblogg-primary',
      'blue-500': 'jobblogg-primary',
      'green-500': 'jobblogg-success',
      'yellow-400': 'jobblogg-warning',
      'red-600': 'jobblogg-error'
    };
    
    let updatedLine = line;
    Object.entries(tokenReplacements).forEach(([old, replacement]) => {
      updatedLine = updatedLine.replace(new RegExp(old, 'g'), replacement);
    });
    
    return updatedLine;
  }

  /**
   * Check if line needs responsive classes
   */
  private needsResponsiveClasses(line: string): boolean {
    const hasClasses = line.includes('className=');
    const hasBreakpoints = JOBBLOGG_PATTERNS.BREAKPOINTS.some(bp => line.includes(bp));
    const hasResponsiveProperties = /\b(p-|m-|text-|w-|h-)\d+/.test(line);
    
    return hasClasses && !hasBreakpoints && hasResponsiveProperties;
  }

  /**
   * Add responsive variants to existing classes
   */
  private addResponsiveVariants(line: string): string {
    let updatedLine = line;
    
    // Add responsive variants for common patterns
    updatedLine = updatedLine.replace(/\bp-(\d+)/g, 'p-$1 xs:p-' + (parseInt('$1') + 1));
    updatedLine = updatedLine.replace(/\bm-(\d+)/g, 'm-$1 xs:m-' + (parseInt('$1') + 1));
    updatedLine = updatedLine.replace(/\btext-(xs|sm)/g, 'text-$1 xs:text-base');
    
    return updatedLine;
  }

  /**
   * Helper to add classes to a line
   */
  private addClasses(line: string, classes: string[]): string {
    const classNameMatch = line.match(/className="([^"]*)"/);
    
    if (classNameMatch) {
      const existingClasses = classNameMatch[1];
      const newClasses = `${existingClasses} ${classes.join(' ')}`.trim();
      return line.replace(/className="[^"]*"/, `className="${newClasses}"`);
    } else {
      return line.replace(/(<\w+)/, `$1 className="${classes.join(' ')}"`);
    }
  }

  /**
   * Generate patch files for the suggestions
   */
  async generatePatchFiles(suggestions: AutoFixSuggestion[]): Promise<string[]> {
    const patchFiles: string[] = [];
    const groupedSuggestions = this.groupSuggestionsByFile(suggestions);
    
    await this.ensureDirectoryExists(this.outputDir);
    
    for (const [file, fileSuggestions] of groupedSuggestions.entries()) {
      const patchContent = this.generatePatchContent(file, fileSuggestions);
      const patchFile = path.join(this.outputDir, `${file.replace(/[\/\\]/g, '_')}.patch`);
      
      await fs.writeFile(patchFile, patchContent);
      patchFiles.push(patchFile);
    }
    
    return patchFiles;
  }

  /**
   * Group suggestions by file
   */
  private groupSuggestionsByFile(suggestions: AutoFixSuggestion[]): Map<string, AutoFixSuggestion[]> {
    const grouped = new Map<string, AutoFixSuggestion[]>();
    
    suggestions.forEach(suggestion => {
      const existing = grouped.get(suggestion.file) || [];
      existing.push(suggestion);
      grouped.set(suggestion.file, existing);
    });
    
    return grouped;
  }

  /**
   * Generate patch file content
   */
  private generatePatchContent(file: string, suggestions: AutoFixSuggestion[]): string {
    let patch = `--- a/${file}\n+++ b/${file}\n`;
    
    suggestions.forEach(suggestion => {
      patch += `@@ -${suggestion.line},1 +${suggestion.line},1 @@\n`;
      patch += `-${suggestion.original}\n`;
      patch += `+${suggestion.suggested}\n`;
      patch += `// ${suggestion.description}\n\n`;
    });
    
    return patch;
  }

  /**
   * Ensure output directory exists
   */
  private async ensureDirectoryExists(dir: string): Promise<void> {
    try {
      await fs.access(dir);
    } catch {
      await fs.mkdir(dir, { recursive: true });
    }
  }
}
