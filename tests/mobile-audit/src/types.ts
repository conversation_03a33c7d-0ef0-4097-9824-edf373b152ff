/**
 * TypeScript definitions for JobbLogg mobile audit framework
 */

export interface ViewportConfig {
  name: string;
  width: number;
  height: number;
  deviceScaleFactor: number;
  isMobile: boolean;
  hasTouch: boolean;
  description: string;
}

export interface RouteConfig {
  path: string;
  name: string;
  requiresAuth: boolean;
  dynamicParams?: Record<string, string>;
  description: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
}

export interface TouchTargetIssue {
  type: 'touch_target_too_small' | 'touch_target_spacing' | 'touch_target_overlap';
  severity: 'critical' | 'high' | 'medium' | 'low';
  element: string;
  selector: string;
  dimensions: { width: number; height: number };
  position: { x: number; y: number };
  recommendation: string;
  wcagReference: string;
}

export interface LayoutIssue {
  type: 'horizontal_overflow' | 'fixed_width_element' | 'responsive_missing' | 'layout_break';
  severity: 'critical' | 'high' | 'medium' | 'low';
  element: string;
  selector: string;
  description: string;
  recommendation: string;
  affectedViewports: string[];
}

export interface AccessibilityIssue {
  type: 'contrast_ratio' | 'font_size' | 'line_height' | 'missing_alt' | 'keyboard_navigation';
  severity: 'critical' | 'high' | 'medium' | 'low';
  element: string;
  selector: string;
  description: string;
  wcagLevel: 'A' | 'AA' | 'AAA';
  recommendation: string;
}

export interface PerformanceMetrics {
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  fcp: number; // First Contentful Paint
  ttfb: number; // Time to First Byte
  lighthouseScore: number;
}

export interface AuditResult {
  url: string;
  routeName: string;
  viewport: ViewportConfig;
  timestamp: string;
  authentication: 'signed-in' | 'signed-out' | 'not-required';
  status: 'passed' | 'failed' | 'warning';
  
  // Issue categories
  touchTargetIssues: TouchTargetIssue[];
  layoutIssues: LayoutIssue[];
  accessibilityIssues: AccessibilityIssue[];
  
  // Performance data
  performanceMetrics: PerformanceMetrics;
  
  // Technical validation
  hasViewportMeta: boolean;
  hasJavaScriptErrors: boolean;
  javascriptErrors: string[];
  
  // JobbLogg-specific checks
  usesJobbLoggTokens: boolean;
  hasResponsiveBreakpoints: boolean;
  norwegianTextSupport: boolean;
  
  // Screenshots and artifacts
  screenshotPath?: string;
  tracePath?: string;
}

export interface SummaryReport {
  totalRoutes: number;
  totalViewports: number;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  warningTests: number;
  overallScore: number;
  
  // Issue summaries
  criticalIssues: number;
  highIssues: number;
  mediumIssues: number;
  lowIssues: number;
  
  // Category breakdowns
  touchTargetIssueCount: number;
  layoutIssueCount: number;
  accessibilityIssueCount: number;
  
  // Performance summary
  averageLighthouseScore: number;
  averageLCP: number;
  averageCLS: number;
  
  // JobbLogg-specific metrics
  jobbLoggTokenCompliance: number;
  responsiveBreakpointCompliance: number;
  norwegianLocalizationSupport: number;
  
  // Recommendations
  topRecommendations: string[];
  quickFixes: string[];
  
  // Detailed results
  results: AuditResult[];
  
  // Metadata
  generatedAt: string;
  environment: 'development' | 'production';
  version: string;
}

export interface TestConfig {
  baseUrl: string;
  environment: 'development' | 'production';
  routes: RouteConfig[];
  viewports: ViewportConfig[];
  authConfig: {
    signInUrl: string;
    signUpUrl: string;
    testCredentials?: {
      email: string;
      password: string;
    };
  };
  mockData: {
    projectIds: string[];
    sharedIds: string[];
    userIds: string[];
  };
}

export interface ValidationModule {
  name: string;
  description: string;
  validate: (page: any, config: ViewportConfig) => Promise<any[]>;
}

export interface ReportGenerator {
  generateJSON: (results: AuditResult[]) => Promise<string>;
  generateMarkdown: (results: AuditResult[]) => Promise<string>;
  generateHTML: (results: AuditResult[]) => Promise<string>;
}

export interface AutoFixSuggestion {
  type: 'add_class' | 'modify_class' | 'add_responsive_variant' | 'fix_touch_target';
  file: string;
  line: number;
  column: number;
  original: string;
  suggested: string;
  description: string;
  confidence: 'high' | 'medium' | 'low';
}
