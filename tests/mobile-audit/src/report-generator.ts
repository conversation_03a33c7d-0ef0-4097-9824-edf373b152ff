import { promises as fs } from 'fs';
import path from 'path';
import { AuditResult, SummaryReport, ReportGenerator } from './types.js';
import { VIEWPORTS, ROUTES } from './config.js';

/**
 * Report Generator
 * Creates comprehensive reports in JSON, Markdown, and HTML formats
 */

export class MobileAuditReportGenerator implements ReportGenerator {
  private outputDir: string;

  constructor(outputDir: string = './reports') {
    this.outputDir = outputDir;
  }

  /**
   * Generate JSON report with full audit results
   */
  async generateJSON(results: AuditResult[]): Promise<string> {
    const summary = this.generateSummary(results);
    const report = {
      summary,
      results,
      metadata: {
        generatedAt: new Date().toISOString(),
        version: '1.0.0',
        framework: 'JobbLogg Mobile Audit',
        totalResults: results.length
      }
    };

    const jsonPath = path.join(this.outputDir, 'mobile-audit-report.json');
    await this.ensureDirectoryExists(this.outputDir);
    await fs.writeFile(jsonPath, JSON.stringify(report, null, 2));
    
    return jsonPath;
  }

  /**
   * Generate Markdown report with actionable recommendations
   */
  async generateMarkdown(results: AuditResult[]): Promise<string> {
    const summary = this.generateSummary(results);
    const markdown = this.buildMarkdownReport(summary, results);
    
    const markdownPath = path.join(this.outputDir, 'mobile-audit-report.md');
    await this.ensureDirectoryExists(this.outputDir);
    await fs.writeFile(markdownPath, markdown);
    
    return markdownPath;
  }

  /**
   * Generate HTML report with interactive elements
   */
  async generateHTML(results: AuditResult[]): Promise<string> {
    const summary = this.generateSummary(results);
    const html = this.buildHTMLReport(summary, results);
    
    const htmlPath = path.join(this.outputDir, 'mobile-audit-report.html');
    await this.ensureDirectoryExists(this.outputDir);
    await fs.writeFile(htmlPath, html);
    
    return htmlPath;
  }

  /**
   * Generate summary statistics from audit results
   */
  private generateSummary(results: AuditResult[]): SummaryReport {
    const totalTests = results.length;
    const passedTests = results.filter(r => r.status === 'passed').length;
    const failedTests = results.filter(r => r.status === 'failed').length;
    const warningTests = results.filter(r => r.status === 'warning').length;

    // Count issues by severity
    let criticalIssues = 0;
    let highIssues = 0;
    let mediumIssues = 0;
    let lowIssues = 0;

    results.forEach(result => {
      [...result.touchTargetIssues, ...result.layoutIssues, ...result.accessibilityIssues].forEach(issue => {
        switch (issue.severity) {
          case 'critical': criticalIssues++; break;
          case 'high': highIssues++; break;
          case 'medium': mediumIssues++; break;
          case 'low': lowIssues++; break;
        }
      });
    });

    // Calculate averages
    const avgLighthouseScore = results.reduce((sum, r) => sum + r.performanceMetrics.lighthouseScore, 0) / totalTests;
    const avgLCP = results.reduce((sum, r) => sum + r.performanceMetrics.lcp, 0) / totalTests;
    const avgCLS = results.reduce((sum, r) => sum + r.performanceMetrics.cls, 0) / totalTests;

    // JobbLogg-specific metrics
    const jobbLoggTokenCompliance = results.filter(r => r.usesJobbLoggTokens).length / totalTests * 100;
    const responsiveBreakpointCompliance = results.filter(r => r.hasResponsiveBreakpoints).length / totalTests * 100;
    const norwegianLocalizationSupport = results.filter(r => r.norwegianTextSupport).length / totalTests * 100;

    // Generate recommendations
    const topRecommendations = this.generateTopRecommendations(results);
    const quickFixes = this.generateQuickFixes(results);

    return {
      totalRoutes: ROUTES.length,
      totalViewports: VIEWPORTS.length,
      totalTests,
      passedTests,
      failedTests,
      warningTests,
      overallScore: Math.round((passedTests / totalTests) * 100),
      
      criticalIssues,
      highIssues,
      mediumIssues,
      lowIssues,
      
      touchTargetIssueCount: results.reduce((sum, r) => sum + r.touchTargetIssues.length, 0),
      layoutIssueCount: results.reduce((sum, r) => sum + r.layoutIssues.length, 0),
      accessibilityIssueCount: results.reduce((sum, r) => sum + r.accessibilityIssues.length, 0),
      
      averageLighthouseScore: Math.round(avgLighthouseScore),
      averageLCP: Math.round(avgLCP),
      averageCLS: Math.round(avgCLS * 1000) / 1000,
      
      jobbLoggTokenCompliance: Math.round(jobbLoggTokenCompliance),
      responsiveBreakpointCompliance: Math.round(responsiveBreakpointCompliance),
      norwegianLocalizationSupport: Math.round(norwegianLocalizationSupport),
      
      topRecommendations,
      quickFixes,
      results,
      
      generatedAt: new Date().toISOString(),
      environment: process.env.NODE_ENV as 'development' | 'production' || 'development',
      version: '1.0.0'
    };
  }

  /**
   * Generate top recommendations based on most common issues
   */
  private generateTopRecommendations(results: AuditResult[]): string[] {
    const recommendations = new Map<string, number>();

    results.forEach(result => {
      [...result.touchTargetIssues, ...result.layoutIssues, ...result.accessibilityIssues].forEach(issue => {
        const rec = issue.recommendation;
        recommendations.set(rec, (recommendations.get(rec) || 0) + 1);
      });
    });

    return Array.from(recommendations.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([rec]) => rec);
  }

  /**
   * Generate quick fixes for common issues
   */
  private generateQuickFixes(results: AuditResult[]): string[] {
    const fixes: string[] = [];

    // Check for common patterns
    const hasSmallTouchTargets = results.some(r => r.touchTargetIssues.some(i => i.type === 'touch_target_too_small'));
    const hasLayoutOverflow = results.some(r => r.layoutIssues.some(i => i.type === 'horizontal_overflow'));
    const hasContrastIssues = results.some(r => r.accessibilityIssues.some(i => i.type === 'contrast_ratio'));

    if (hasSmallTouchTargets) {
      fixes.push('Add touch-target utility classes to buttons and interactive elements');
    }

    if (hasLayoutOverflow) {
      fixes.push('Add max-w-full and overflow-hidden classes to prevent horizontal scrolling');
    }

    if (hasContrastIssues) {
      fixes.push('Replace hardcoded colors with jobblogg-prefixed design tokens');
    }

    if (!results.every(r => r.usesJobbLoggTokens)) {
      fixes.push('Update components to use JobbLogg design system tokens');
    }

    if (!results.every(r => r.hasResponsiveBreakpoints)) {
      fixes.push('Add responsive breakpoint classes (xxs:, xs:, sm:, md:, lg:)');
    }

    return fixes;
  }

  /**
   * Build Markdown report content
   */
  private buildMarkdownReport(summary: SummaryReport, results: AuditResult[]): string {
    return `# JobbLogg Mobile Responsiveness Audit Report

Generated: ${new Date(summary.generatedAt).toLocaleString('nb-NO')}
Environment: ${summary.environment}

## 📊 Executive Summary

- **Overall Score**: ${summary.overallScore}% (${summary.passedTests}/${summary.totalTests} tests passed)
- **Routes Tested**: ${summary.totalRoutes}
- **Viewports Tested**: ${summary.totalViewports}
- **Total Issues**: ${summary.criticalIssues + summary.highIssues + summary.mediumIssues + summary.lowIssues}

### Issue Breakdown
- 🔴 Critical: ${summary.criticalIssues}
- 🟠 High: ${summary.highIssues}
- 🟡 Medium: ${summary.mediumIssues}
- 🟢 Low: ${summary.lowIssues}

### Performance Metrics
- **Average Lighthouse Score**: ${summary.averageLighthouseScore}/100
- **Average LCP**: ${summary.averageLCP}ms
- **Average CLS**: ${summary.averageCLS}

### JobbLogg Compliance
- **Design Token Usage**: ${summary.jobbLoggTokenCompliance}%
- **Responsive Breakpoints**: ${summary.responsiveBreakpointCompliance}%
- **Norwegian Localization**: ${summary.norwegianLocalizationSupport}%

## 🎯 Top Recommendations

${summary.topRecommendations.map((rec, i) => `${i + 1}. ${rec}`).join('\n')}

## ⚡ Quick Fixes

${summary.quickFixes.map((fix, i) => `- ${fix}`).join('\n')}

## 📱 Detailed Results by Viewport

${VIEWPORTS.map(viewport => this.buildViewportSection(viewport, results)).join('\n\n')}

## 🔧 Implementation Guide

### Adding Touch Target Classes
\`\`\`css
/* Add to components with small interactive elements */
.touch-target {
  min-width: 44px;
  min-height: 44px;
}

.touch-target-compact {
  min-width: 36px;
  min-height: 36px;
}
\`\`\`

### Responsive Breakpoint Usage
\`\`\`jsx
// Use JobbLogg custom breakpoints
<div className="p-2 xxs:p-3 xs:p-4 sm:p-6">
  <button className="text-sm xxs:text-base xs:text-lg">
    Button Text
  </button>
</div>
\`\`\`

### Design Token Migration
\`\`\`jsx
// Before
<div className="bg-blue-600 text-white">

// After  
<div className="bg-jobblogg-primary text-jobblogg-text-strong">
\`\`\`

---

*Report generated by JobbLogg Mobile Audit Framework v${summary.version}*
`;
  }

  /**
   * Build viewport-specific section for Markdown report
   */
  private buildViewportSection(viewport: any, results: AuditResult[]): string {
    const viewportResults = results.filter(r => r.viewport.name === viewport.name);
    const passedCount = viewportResults.filter(r => r.status === 'passed').length;
    const totalCount = viewportResults.length;
    
    return `### ${viewport.description} (${viewport.width}×${viewport.height})

**Status**: ${passedCount}/${totalCount} routes passed

${viewportResults.map(result => `
#### ${result.routeName}
- **Status**: ${result.status === 'passed' ? '✅' : result.status === 'failed' ? '❌' : '⚠️'} ${result.status}
- **Touch Target Issues**: ${result.touchTargetIssues.length}
- **Layout Issues**: ${result.layoutIssues.length}  
- **Accessibility Issues**: ${result.accessibilityIssues.length}
- **Lighthouse Score**: ${result.performanceMetrics.lighthouseScore}/100
`).join('')}`;
  }

  /**
   * Build HTML report content
   */
  private buildHTMLReport(summary: SummaryReport, results: AuditResult[]): string {
    return `<!DOCTYPE html>
<html lang="nb-NO">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JobbLogg Mobile Audit Report</title>
    <style>
        body { font-family: Inter, system-ui, sans-serif; margin: 0; padding: 20px; background: #f8fafc; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 30px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .score { font-size: 3rem; font-weight: bold; color: ${summary.overallScore >= 80 ? '#10b981' : summary.overallScore >= 60 ? '#f59e0b' : '#ef4444'}; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .metric { background: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .metric-value { font-size: 2rem; font-weight: bold; margin-bottom: 5px; }
        .critical { color: #dc2626; }
        .high { color: #ea580c; }
        .medium { color: #d97706; }
        .low { color: #65a30d; }
        .section { background: white; margin: 20px 0; padding: 30px; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .viewport-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .viewport-card { border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; }
        .route-item { padding: 10px; border-left: 4px solid #e5e7eb; margin: 10px 0; }
        .route-passed { border-left-color: #10b981; }
        .route-failed { border-left-color: #ef4444; }
        .route-warning { border-left-color: #f59e0b; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 JobbLogg Mobile Responsiveness Audit</h1>
            <p>Generated: ${new Date(summary.generatedAt).toLocaleString('nb-NO')} | Environment: ${summary.environment}</p>
            <div class="score">${summary.overallScore}%</div>
            <p>${summary.passedTests}/${summary.totalTests} tests passed</p>
        </div>

        <div class="metrics">
            <div class="metric">
                <div class="metric-value critical">${summary.criticalIssues}</div>
                <div>Critical Issues</div>
            </div>
            <div class="metric">
                <div class="metric-value high">${summary.highIssues}</div>
                <div>High Priority</div>
            </div>
            <div class="metric">
                <div class="metric-value medium">${summary.mediumIssues}</div>
                <div>Medium Priority</div>
            </div>
            <div class="metric">
                <div class="metric-value">${summary.averageLighthouseScore}</div>
                <div>Avg Lighthouse Score</div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 Top Recommendations</h2>
            <ol>
                ${summary.topRecommendations.map(rec => `<li>${rec}</li>`).join('')}
            </ol>
        </div>

        <div class="section">
            <h2>📱 Results by Viewport</h2>
            <div class="viewport-grid">
                ${VIEWPORTS.map(viewport => {
                  const viewportResults = results.filter(r => r.viewport.name === viewport.name);
                  return `
                    <div class="viewport-card">
                        <h3>${viewport.description}</h3>
                        <p>${viewport.width}×${viewport.height}px</p>
                        ${viewportResults.map(result => `
                            <div class="route-item route-${result.status}">
                                <strong>${result.routeName}</strong><br>
                                Issues: ${result.touchTargetIssues.length + result.layoutIssues.length + result.accessibilityIssues.length} | 
                                Lighthouse: ${result.performanceMetrics.lighthouseScore}/100
                            </div>
                        `).join('')}
                    </div>
                  `;
                }).join('')}
            </div>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Ensure output directory exists
   */
  private async ensureDirectoryExists(dir: string): Promise<void> {
    try {
      await fs.access(dir);
    } catch {
      await fs.mkdir(dir, { recursive: true });
    }
  }
}
