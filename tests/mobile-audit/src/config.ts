import { ViewportConfig, RouteConfig, TestConfig } from './types.js';

/**
 * JobbLogg Mobile Audit Configuration
 * Defines viewports, routes, and test parameters for comprehensive mobile testing
 */

export const VIEWPORTS: ViewportConfig[] = [
  {
    name: 'ultra-narrow-mobile',
    width: 320,
    height: 568,
    deviceScaleFactor: 2,
    isMobile: true,
    hasTouch: true,
    description: 'iPhone SE, older Android phones - Critical narrow width testing'
  },
  {
    name: 'standard-mobile',
    width: 375,
    height: 812,
    deviceScaleFactor: 3,
    isMobile: true,
    hasTouch: true,
    description: 'iPhone 12/13 mini - Most common mobile viewport'
  },
  {
    name: 'large-mobile',
    width: 414,
    height: 896,
    deviceScaleFactor: 3,
    isMobile: true,
    hasTouch: true,
    description: 'iPhone 12/13 Pro Max - Large mobile screens'
  },
  {
    name: 'tablet-portrait',
    width: 768,
    height: 1024,
    deviceScaleFactor: 2,
    isMobile: false,
    hasTouch: true,
    description: 'iPad portrait - Tablet responsiveness'
  }
];

export const ROUTES: RouteConfig[] = [
  // Public routes
  {
    path: '/shared/{sharedId}',
    name: 'SharedProject',
    requiresAuth: false,
    dynamicParams: { sharedId: 'test123456' },
    description: 'Customer-facing shared project view',
    priority: 'critical'
  },
  
  // Authentication routes
  {
    path: '/sign-in',
    name: 'SignIn',
    requiresAuth: false,
    description: 'User authentication - sign in',
    priority: 'high'
  },
  {
    path: '/sign-up',
    name: 'SignUp',
    requiresAuth: false,
    description: 'User authentication - sign up',
    priority: 'high'
  },
  
  // Protected routes - require authentication
  {
    path: '/',
    name: 'Dashboard',
    requiresAuth: true,
    description: 'Main dashboard with project overview',
    priority: 'critical'
  },
  {
    path: '/create',
    name: 'CreateProject',
    requiresAuth: true,
    description: 'Project creation form',
    priority: 'high'
  },
  {
    path: '/create-wizard',
    name: 'CreateProjectWizard',
    requiresAuth: true,
    description: 'Multi-step project creation wizard',
    priority: 'high'
  },
  {
    path: '/project/{projectId}',
    name: 'ProjectLog',
    requiresAuth: true,
    dynamicParams: { projectId: 'test-project-1' },
    description: 'Project log entry and image upload',
    priority: 'critical'
  },
  {
    path: '/project/{projectId}/details',
    name: 'ProjectDetail',
    requiresAuth: true,
    dynamicParams: { projectId: 'test-project-1' },
    description: 'Contractor project management panel',
    priority: 'high'
  },
  {
    path: '/conversations',
    name: 'Conversations',
    requiresAuth: true,
    description: 'Chat conversations overview',
    priority: 'medium'
  },
  {
    path: '/archived-projects',
    name: 'ArchivedProjects',
    requiresAuth: true,
    description: 'Archived projects management',
    priority: 'medium'
  },
  {
    path: '/test-google-maps',
    name: 'GoogleMapsTest',
    requiresAuth: true,
    description: 'Google Maps integration testing',
    priority: 'low'
  }
];

export const TEST_CONFIG: TestConfig = {
  baseUrl: process.env.BASE_URL || 'http://localhost:5173',
  environment: (process.env.NODE_ENV as 'development' | 'production') || 'development',
  routes: ROUTES,
  viewports: VIEWPORTS,
  authConfig: {
    signInUrl: '/sign-in',
    signUpUrl: '/sign-up',
    testCredentials: {
      email: process.env.TEST_USER_EMAIL || '<EMAIL>',
      password: process.env.TEST_USER_PASSWORD || 'TestPassword123!'
    }
  },
  mockData: {
    // Test data from convex/testDataUtilities.ts
    projectIds: ['test-project-1', 'test-project-2'],
    sharedIds: ['test123456', 'test789012'],
    userIds: ['test-user-1', 'test-user-2']
  }
};

// JobbLogg-specific validation patterns
export const JOBBLOGG_PATTERNS = {
  // Design tokens that should be used instead of hardcoded values
  DESIGN_TOKENS: [
    'jobblogg-primary',
    'jobblogg-accent',
    'jobblogg-success',
    'jobblogg-warning',
    'jobblogg-error',
    'jobblogg-neutral',
    'jobblogg-text-strong',
    'jobblogg-text-medium',
    'jobblogg-text-muted',
    'jobblogg-border',
    'jobblogg-card'
  ],
  
  // Custom breakpoints that should be used
  BREAKPOINTS: ['xxs:', 'xs:', 'sm:', 'md:', 'lg:', 'xl:', '2xl:'],
  
  // Touch target classes
  TOUCH_CLASSES: ['touch-target', 'touch-target-compact'],
  
  // Norwegian text patterns to validate
  NORWEGIAN_PATTERNS: [
    'æ', 'ø', 'å', 'Æ', 'Ø', 'Å',
    'prosjekt', 'oppføring', 'leverandør', 'kunde'
  ]
};

// WCAG AA compliance thresholds
export const ACCESSIBILITY_THRESHOLDS = {
  MIN_CONTRAST_RATIO: 4.5,
  MIN_FONT_SIZE: 16, // pixels
  MIN_LINE_HEIGHT: 1.5,
  MIN_TOUCH_TARGET: 44, // pixels
  MIN_TOUCH_SPACING: 8 // pixels
};

// Performance thresholds for mobile
export const PERFORMANCE_THRESHOLDS = {
  LCP_GOOD: 2.5, // seconds
  FID_GOOD: 100, // milliseconds
  CLS_GOOD: 0.1,
  LIGHTHOUSE_MIN_SCORE: 80
};
