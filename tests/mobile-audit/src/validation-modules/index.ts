/**
 * Validation Modules Index
 * Exports all validation modules for the JobbLogg mobile audit framework
 */

export { LayoutValidation } from './layout-validation.js';
export { TouchTargetValidation } from './touch-target-validation.js';
export { AccessibilityValidation } from './accessibility-validation.js';
export { PerformanceValidation } from './performance-validation.js';

// Re-export types for convenience
export type { ValidationModule } from '../types.js';

/**
 * All available validation modules
 */
export const ALL_VALIDATION_MODULES = [
  'LayoutValidation',
  'TouchTargetValidation', 
  'AccessibilityValidation',
  'PerformanceValidation'
] as const;

export type ValidationModuleName = typeof ALL_VALIDATION_MODULES[number];
