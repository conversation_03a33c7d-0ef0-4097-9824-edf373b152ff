import { Page } from '@playwright/test';
import { ViewportConfig, AccessibilityIssue, ValidationModule } from '../types.js';
import { ACCESSIBILITY_THRESHOLDS, JOBBLOGG_PATTERNS } from '../config.js';

/**
 * Accessibility Validation Module
 * Validates WCAG AA compliance including contrast ratios, font sizes, and Norwegian text support
 */

export class AccessibilityValidation implements ValidationModule {
  name = 'Accessibility Validation';
  description = 'Validates WCAG AA compliance for contrast, typography, and Norwegian localization';

  async validate(page: Page, viewport: ViewportConfig): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];

    // Check contrast ratios
    const contrastIssues = await this.checkContrastRatios(page, viewport);
    issues.push(...contrastIssues);

    // Check font sizes
    const fontSizeIssues = await this.checkFontSizes(page, viewport);
    issues.push(...fontSizeIssues);

    // Check line heights
    const lineHeightIssues = await this.checkLineHeights(page, viewport);
    issues.push(...lineHeightIssues);

    // Check Norwegian text support
    const norwegianIssues = await this.checkNorwegianTextSupport(page, viewport);
    issues.push(...norwegianIssues);

    // Check missing alt text
    const altTextIssues = await this.checkMissingAltText(page, viewport);
    issues.push(...altTextIssues);

    // Check keyboard navigation
    const keyboardIssues = await this.checkKeyboardNavigation(page, viewport);
    issues.push(...keyboardIssues);

    return issues;
  }

  /**
   * Check contrast ratios for text elements
   */
  private async checkContrastRatios(page: Page, viewport: ViewportConfig): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];

    try {
      // Inject axe-core for contrast checking
      await page.addScriptTag({ path: './node_modules/axe-core/axe.min.js' });

      const contrastIssues = await page.evaluate((minContrast) => {
        const problems: Array<{
          selector: string;
          element: string;
          contrastRatio: number;
          foreground: string;
          background: string;
          fontSize: number;
          isLargeText: boolean;
        }> = [];

        // Helper function to calculate contrast ratio
        function calculateContrastRatio(color1: string, color2: string): number {
          // Simplified contrast calculation - in production would use proper WCAG algorithm
          // For now, return a mock value that will trigger some contrast issues for testing
          const rgb1 = parseRgb(color1);
          const rgb2 = parseRgb(color2);

          if (!rgb1 || !rgb2) return 4.5; // Default to passing ratio if parsing fails

          const l1 = getLuminance(rgb1);
          const l2 = getLuminance(rgb2);

          const lighter = Math.max(l1, l2);
          const darker = Math.min(l1, l2);

          return (lighter + 0.05) / (darker + 0.05);
        }

        function parseRgb(color: string): [number, number, number] | null {
          const match = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
          if (match) {
            return [parseInt(match[1]), parseInt(match[2]), parseInt(match[3])];
          }
          return null;
        }

        function getLuminance([r, g, b]: [number, number, number]): number {
          const [rs, gs, bs] = [r, g, b].map(c => {
            c = c / 255;
            return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
          });
          return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
        }

        // Get all text elements
        const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div, a, button, label, input, textarea');

        textElements.forEach((element, index) => {
          const computed = window.getComputedStyle(element);
          const textContent = element.textContent?.trim();

          // Skip empty elements
          if (!textContent || textContent.length === 0) return;

          // Get colors
          const color = computed.color;
          const backgroundColor = computed.backgroundColor;

          // Skip if no background color (transparent)
          if (backgroundColor === 'rgba(0, 0, 0, 0)' || backgroundColor === 'transparent') return;

          // Calculate contrast ratio
          const contrastRatio = calculateContrastRatio(color, backgroundColor);
          
          // Get font size
          const fontSize = parseFloat(computed.fontSize);
          const fontWeight = computed.fontWeight;
          const isLargeText = fontSize >= 18 || (fontSize >= 14 && (fontWeight === 'bold' || parseInt(fontWeight) >= 700));
          
          // Check if contrast meets requirements
          const requiredRatio = isLargeText ? 3.0 : minContrast;
          
          if (contrastRatio < requiredRatio) {
            let selector = element.tagName.toLowerCase();
            if (element.id) selector += `#${element.id}`;
            else if (element.className && typeof element.className === 'string') {
              const classes = element.className.toString().split(' ').filter(c => c);
              if (classes && classes.length > 0) selector += `.${classes.slice(0, 2).join('.')}`;
            } else {
              selector += `:nth-of-type(${index + 1})`;
            }
            
            problems.push({
              selector,
              element: element.tagName,
              contrastRatio,
              foreground: color,
              background: backgroundColor,
              fontSize,
              isLargeText
            });
          }
        });

        return problems;
      }, ACCESSIBILITY_THRESHOLDS.MIN_CONTRAST_RATIO);

      contrastIssues.forEach(issue => {
        const severity = issue.contrastRatio < 3.0 ? 'critical' : 'high';
        const requiredRatio = issue.isLargeText ? 3.0 : ACCESSIBILITY_THRESHOLDS.MIN_CONTRAST_RATIO;
        
        issues.push({
          type: 'contrast_ratio',
          severity,
          element: issue.element,
          selector: issue.selector,
          description: `Contrast ratio ${issue.contrastRatio.toFixed(2)}:1 is below required ${requiredRatio}:1 (${issue.isLargeText ? 'large text' : 'normal text'})`,
          wcagLevel: 'AA',
          recommendation: `Increase contrast between text (${issue.foreground}) and background (${issue.background}). Use JobbLogg design tokens for consistent accessible colors.`
        });
      });
    } catch (error) {
      console.error('Error checking contrast ratios:', error);
    }

    return issues;
  }

  /**
   * Simple contrast ratio calculation (simplified version)
   */
  private calculateContrastRatio(color1: string, color2: string): number {
    // This is a simplified version - real implementation would properly parse RGB values
    // and calculate luminance according to WCAG guidelines
    return 4.5; // Placeholder - would need proper color parsing and luminance calculation
  }

  /**
   * Check font sizes for mobile readability
   */
  private async checkFontSizes(page: Page, viewport: ViewportConfig): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];

    try {
      const fontSizeIssues = await page.evaluate((minFontSize) => {
        const problems: Array<{
          selector: string;
          element: string;
          fontSize: number;
          textContent: string;
        }> = [];

        const textElements = document.querySelectorAll('p, span, div, a, button, label, input, textarea');
        
        textElements.forEach((element, index) => {
          const computed = window.getComputedStyle(element);
          const fontSize = parseFloat(computed.fontSize);
          const textContent = element.textContent?.trim() || '';
          
          // Skip empty elements or very short text
          if (textContent.length < 3) return;
          
          // Check if font size is too small for mobile
          if (fontSize < minFontSize) {
            let selector = element.tagName.toLowerCase();
            if (element.id) selector += `#${element.id}`;
            else if (element.className && typeof element.className === 'string') {
              const classes = element.className.toString().split(' ').filter(c => c);
              if (classes && classes.length > 0) selector += `.${classes.slice(0, 2).join('.')}`;
            } else {
              selector += `:nth-of-type(${index + 1})`;
            }
            
            problems.push({
              selector,
              element: element.tagName,
              fontSize,
              textContent: textContent.substring(0, 50) + (textContent.length > 50 ? '...' : '')
            });
          }
        });

        return problems;
      }, ACCESSIBILITY_THRESHOLDS.MIN_FONT_SIZE);

      fontSizeIssues.forEach(issue => {
        const severity = issue.fontSize < 12 ? 'critical' : 'medium';
        
        issues.push({
          type: 'font_size',
          severity,
          element: issue.element,
          selector: issue.selector,
          description: `Font size ${issue.fontSize}px is below recommended minimum ${ACCESSIBILITY_THRESHOLDS.MIN_FONT_SIZE}px for mobile readability`,
          wcagLevel: 'AA',
          recommendation: `Increase font size to at least ${ACCESSIBILITY_THRESHOLDS.MIN_FONT_SIZE}px. Use responsive text classes like text-sm sm:text-base for mobile-first scaling.`
        });
      });
    } catch (error) {
      console.error('Error checking font sizes:', error);
    }

    return issues;
  }

  /**
   * Check line heights for readability
   */
  private async checkLineHeights(page: Page, viewport: ViewportConfig): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];

    try {
      const lineHeightIssues = await page.evaluate((minLineHeight) => {
        const problems: Array<{
          selector: string;
          element: string;
          lineHeight: number;
          fontSize: number;
        }> = [];

        const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, div');
        
        textElements.forEach((element, index) => {
          const computed = window.getComputedStyle(element);
          const lineHeight = parseFloat(computed.lineHeight);
          const fontSize = parseFloat(computed.fontSize);
          const textContent = element.textContent?.trim() || '';
          
          // Skip empty elements or single-line text
          if (textContent.length < 50) return;
          
          // Calculate line height ratio
          const lineHeightRatio = lineHeight / fontSize;
          
          if (lineHeightRatio < minLineHeight) {
            let selector = element.tagName.toLowerCase();
            if (element.id) selector += `#${element.id}`;
            else if (element.className && typeof element.className === 'string') {
              const classes = element.className.toString().split(' ').filter(c => c);
              if (classes && classes.length > 0) selector += `.${classes.slice(0, 2).join('.')}`;
            } else {
              selector += `:nth-of-type(${index + 1})`;
            }
            
            problems.push({
              selector,
              element: element.tagName,
              lineHeight: lineHeightRatio,
              fontSize
            });
          }
        });

        return problems;
      }, ACCESSIBILITY_THRESHOLDS.MIN_LINE_HEIGHT);

      lineHeightIssues.forEach(issue => {
        issues.push({
          type: 'line_height',
          severity: 'medium',
          element: issue.element,
          selector: issue.selector,
          description: `Line height ratio ${issue.lineHeight.toFixed(2)} is below recommended minimum ${ACCESSIBILITY_THRESHOLDS.MIN_LINE_HEIGHT}`,
          wcagLevel: 'AA',
          recommendation: `Increase line height to at least ${ACCESSIBILITY_THRESHOLDS.MIN_LINE_HEIGHT}x font size. Use leading-relaxed or leading-loose classes.`
        });
      });
    } catch (error) {
      console.error('Error checking line heights:', error);
    }

    return issues;
  }

  /**
   * Check Norwegian text support and character rendering
   */
  private async checkNorwegianTextSupport(page: Page, viewport: ViewportConfig): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];

    try {
      const norwegianIssues = await page.evaluate((norwegianPatterns) => {
        const problems: Array<{
          selector: string;
          element: string;
          missingChars: string[];
          textSample: string;
        }> = [];

        // Check if Norwegian characters are properly rendered
        const textElements = document.querySelectorAll('*');
        
        textElements.forEach((element, index) => {
          const textContent = element.textContent || '';
          
          // Check if element contains Norwegian text
          const hasNorwegianChars = norwegianPatterns.some((char: string) => textContent.includes(char));
          
          if (hasNorwegianChars) {
            const computed = window.getComputedStyle(element);
            const fontFamily = computed.fontFamily;
            
            // Check if font supports Norwegian characters (simplified check)
            const missingChars: string[] = [];
            norwegianPatterns.forEach((char: string) => {
              if (textContent.includes(char)) {
                // In a real implementation, we would check if the character renders properly
                // This is a simplified version
                if (!fontFamily.includes('Inter') && !fontFamily.includes('system-ui')) {
                  missingChars.push(char);
                }
              }
            });
            
            if (missingChars.length > 0) {
              let selector = element.tagName.toLowerCase();
              if (element.id) selector += `#${element.id}`;
              
              problems.push({
                selector,
                element: element.tagName,
                missingChars,
                textSample: textContent.substring(0, 100)
              });
            }
          }
        });

        return problems;
      }, JOBBLOGG_PATTERNS.NORWEGIAN_PATTERNS);

      norwegianIssues.forEach(issue => {
        issues.push({
          type: 'missing_alt',
          severity: 'medium',
          element: issue.element,
          selector: issue.selector,
          description: `Potential Norwegian character rendering issues with characters: ${issue.missingChars.join(', ')}`,
          wcagLevel: 'AA',
          recommendation: 'Ensure font stack includes Norwegian character support. JobbLogg uses Inter font which supports Norwegian characters.'
        });
      });
    } catch (error) {
      console.error('Error checking Norwegian text support:', error);
    }

    return issues;
  }

  /**
   * Check for missing alt text on images
   */
  private async checkMissingAltText(page: Page, viewport: ViewportConfig): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];

    try {
      const altTextIssues = await page.evaluate(() => {
        const problems: Array<{
          selector: string;
          src: string;
          hasAlt: boolean;
          altText: string;
        }> = [];

        const images = document.querySelectorAll('img');
        
        images.forEach((img, index) => {
          const src = img.src;
          const alt = img.alt;
          const hasAlt = alt !== undefined && alt.trim().length > 0;
          
          // Skip decorative images or those with proper alt text
          if (!hasAlt && src && !src.includes('data:image')) {
            let selector = 'img';
            if (img.id) selector += `#${img.id}`;
            else if (img.className && typeof img.className === 'string') {
              const classes = img.className.toString().split(' ').filter(c => c);
              if (classes && classes.length > 0) selector += `.${classes.slice(0, 2).join('.')}`;
            } else {
              selector += `:nth-of-type(${index + 1})`;
            }
            
            problems.push({
              selector,
              src: src.substring(0, 100),
              hasAlt,
              altText: alt || ''
            });
          }
        });

        return problems;
      });

      altTextIssues.forEach(issue => {
        issues.push({
          type: 'missing_alt',
          severity: 'high',
          element: 'IMG',
          selector: issue.selector,
          description: `Image missing alt text for screen readers`,
          wcagLevel: 'A',
          recommendation: 'Add descriptive alt text that explains the image content or purpose. Use alt="" for decorative images.'
        });
      });
    } catch (error) {
      console.error('Error checking alt text:', error);
    }

    return issues;
  }

  /**
   * Check keyboard navigation support
   */
  private async checkKeyboardNavigation(page: Page, viewport: ViewportConfig): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];

    try {
      const keyboardIssues = await page.evaluate(() => {
        const problems: Array<{
          selector: string;
          element: string;
          issue: string;
        }> = [];

        // Check interactive elements for keyboard accessibility
        const interactiveElements = document.querySelectorAll('button, a, input, textarea, select, [role="button"], [tabindex]');
        
        interactiveElements.forEach((element, index) => {
          const computed = window.getComputedStyle(element);
          const tabIndex = element.getAttribute('tabindex');
          
          // Check for negative tabindex on interactive elements
          if (tabIndex === '-1' && element.tagName !== 'DIV') {
            let selector = element.tagName.toLowerCase();
            if (element.id) selector += `#${element.id}`;
            
            problems.push({
              selector,
              element: element.tagName,
              issue: 'Interactive element has tabindex="-1" making it inaccessible via keyboard'
            });
          }
          
          // Check for missing focus styles
          const outlineStyle = computed.outline;
          const outlineWidth = computed.outlineWidth;
          
          if (outlineStyle === 'none' || outlineWidth === '0px') {
            let selector = element.tagName.toLowerCase();
            if (element.id) selector += `#${element.id}`;
            
            problems.push({
              selector,
              element: element.tagName,
              issue: 'Interactive element lacks visible focus indicator'
            });
          }
        });

        return problems;
      });

      keyboardIssues.forEach(issue => {
        const severity = issue.issue.includes('tabindex') ? 'critical' : 'medium';
        
        issues.push({
          type: 'keyboard_navigation',
          severity,
          element: issue.element,
          selector: issue.selector,
          description: issue.issue,
          wcagLevel: 'AA',
          recommendation: issue.issue.includes('tabindex') 
            ? 'Remove negative tabindex or ensure proper keyboard navigation flow'
            : 'Add visible focus styles using focus:outline-* or focus:ring-* classes'
        });
      });
    } catch (error) {
      console.error('Error checking keyboard navigation:', error);
    }

    return issues;
  }
}
