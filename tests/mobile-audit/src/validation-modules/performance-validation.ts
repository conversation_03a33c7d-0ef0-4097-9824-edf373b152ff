import { Page } from '@playwright/test';
import { ViewportConfig, PerformanceMetrics, ValidationModule } from '../types.js';
import { PERFORMANCE_THRESHOLDS } from '../config.js';

/**
 * Performance Validation Module
 * Measures Core Web Vitals and mobile performance metrics using Lighthouse
 */

export class PerformanceValidation implements ValidationModule {
  name = 'Performance Validation';
  description = 'Measures Core Web Vitals (LCP, FID, CLS) and mobile performance metrics';

  async validate(page: Page, viewport: ViewportConfig): Promise<PerformanceMetrics> {
    // Collect performance metrics
    const metrics = await this.collectPerformanceMetrics(page, viewport);
    
    // Run Lighthouse audit for mobile
    const lighthouseScore = await this.runLighthouseAudit(page, viewport);
    
    return {
      ...metrics,
      lighthouseScore
    };
  }

  /**
   * Collect Core Web Vitals and performance metrics
   */
  private async collectPerformanceMetrics(page: Page, viewport: ViewportConfig): Promise<Omit<PerformanceMetrics, 'lighthouseScore'>> {
    try {
      // Wait for page to be fully loaded
      await page.waitForLoadState('networkidle');

      const metrics = await page.evaluate(() => {
        return new Promise<{
          lcp: number;
          fid: number;
          cls: number;
          fcp: number;
          ttfb: number;
        }>((resolve) => {
          const metrics = {
            lcp: 0,
            fid: 0,
            cls: 0,
            fcp: 0,
            ttfb: 0
          };

          // Collect navigation timing
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          if (navigation) {
            metrics.ttfb = navigation.responseStart - navigation.requestStart;
          }

          // Use PerformanceObserver to collect Web Vitals
          let metricsCollected = 0;
          const totalMetrics = 3; // LCP, FID, CLS

          const observer = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              switch (entry.entryType) {
                case 'largest-contentful-paint':
                  metrics.lcp = entry.startTime;
                  metricsCollected++;
                  break;
                case 'first-input':
                  metrics.fid = (entry as any).processingStart - entry.startTime;
                  metricsCollected++;
                  break;
                case 'layout-shift':
                  if (!(entry as any).hadRecentInput) {
                    metrics.cls += (entry as any).value;
                  }
                  break;
                case 'paint':
                  if (entry.name === 'first-contentful-paint') {
                    metrics.fcp = entry.startTime;
                  }
                  break;
              }
            }

            // Resolve when we have collected enough metrics or after timeout
            if (metricsCollected >= 2) { // LCP and either FID or we'll timeout
              resolve(metrics);
            }
          });

          // Observe different entry types
          try {
            observer.observe({ entryTypes: ['largest-contentful-paint'] });
            observer.observe({ entryTypes: ['first-input'] });
            observer.observe({ entryTypes: ['layout-shift'] });
            observer.observe({ entryTypes: ['paint'] });
          } catch (e) {
            // Some browsers might not support all entry types
            console.warn('Some performance entry types not supported:', e);
          }

          // Timeout after 10 seconds
          setTimeout(() => {
            observer.disconnect();
            resolve(metrics);
          }, 10000);
        });
      });

      return metrics;
    } catch (error) {
      console.error('Error collecting performance metrics:', error);
      return {
        lcp: 0,
        fid: 0,
        cls: 0,
        fcp: 0,
        ttfb: 0
      };
    }
  }

  /**
   * Run Lighthouse audit for mobile performance
   */
  private async runLighthouseAudit(page: Page, viewport: ViewportConfig): Promise<number> {
    try {
      // For now, return a placeholder score
      // In a full implementation, you would integrate with Lighthouse programmatically
      // This would require additional setup and dependencies
      
      // Simulate Lighthouse scoring based on collected metrics
      const url = page.url();
      
      // Basic performance scoring based on our collected metrics
      const performanceMetrics = await this.collectPerformanceMetrics(page, viewport);
      
      let score = 100;
      
      // Deduct points based on Core Web Vitals thresholds
      if (performanceMetrics.lcp > PERFORMANCE_THRESHOLDS.LCP_GOOD * 1000) {
        score -= 20;
      }
      
      if (performanceMetrics.fid > PERFORMANCE_THRESHOLDS.FID_GOOD) {
        score -= 15;
      }
      
      if (performanceMetrics.cls > PERFORMANCE_THRESHOLDS.CLS_GOOD) {
        score -= 15;
      }
      
      // Additional deductions for mobile-specific issues
      if (viewport.isMobile) {
        // Check for mobile-specific performance issues
        const mobileIssues = await this.checkMobilePerformanceIssues(page);
        score -= mobileIssues * 5;
      }
      
      return Math.max(0, Math.min(100, score));
    } catch (error) {
      console.error('Error running Lighthouse audit:', error);
      return 0;
    }
  }

  /**
   * Check for mobile-specific performance issues
   */
  private async checkMobilePerformanceIssues(page: Page): Promise<number> {
    let issueCount = 0;

    try {
      const issues = await page.evaluate(() => {
        let problems = 0;

        // Check for large images without optimization
        const images = document.querySelectorAll('img');
        images.forEach(img => {
          const rect = img.getBoundingClientRect();
          // Check if image is larger than its display size (not optimized)
          if (img.naturalWidth > rect.width * 2 || img.naturalHeight > rect.height * 2) {
            problems++;
          }
        });

        // Check for excessive DOM size
        const domSize = document.querySelectorAll('*').length;
        if (domSize > 1500) {
          problems++;
        }

        // Check for blocking resources
        const scripts = document.querySelectorAll('script[src]');
        const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
        if (scripts.length > 10 || stylesheets.length > 5) {
          problems++;
        }

        // Check for missing viewport meta tag
        const viewportMeta = document.querySelector('meta[name="viewport"]');
        if (!viewportMeta) {
          problems++;
        }

        return problems;
      });

      issueCount = issues;
    } catch (error) {
      console.error('Error checking mobile performance issues:', error);
    }

    return issueCount;
  }

  /**
   * Generate performance recommendations based on metrics
   */
  generateRecommendations(metrics: PerformanceMetrics, viewport: ViewportConfig): string[] {
    const recommendations: string[] = [];

    // LCP recommendations
    if (metrics.lcp > PERFORMANCE_THRESHOLDS.LCP_GOOD * 1000) {
      recommendations.push('Optimize Largest Contentful Paint (LCP): Optimize images, remove unused CSS, improve server response times');
    }

    // FID recommendations
    if (metrics.fid > PERFORMANCE_THRESHOLDS.FID_GOOD) {
      recommendations.push('Improve First Input Delay (FID): Reduce JavaScript execution time, break up long tasks, use web workers');
    }

    // CLS recommendations
    if (metrics.cls > PERFORMANCE_THRESHOLDS.CLS_GOOD) {
      recommendations.push('Fix Cumulative Layout Shift (CLS): Set dimensions for images and embeds, avoid inserting content above existing content');
    }

    // Lighthouse score recommendations
    if (metrics.lighthouseScore < PERFORMANCE_THRESHOLDS.LIGHTHOUSE_MIN_SCORE) {
      recommendations.push('Improve overall performance score: Enable compression, minify resources, use efficient cache policies');
    }

    // Mobile-specific recommendations
    if (viewport.isMobile) {
      recommendations.push('Mobile optimization: Use responsive images, implement lazy loading, minimize main thread work');
    }

    return recommendations;
  }

  /**
   * Check if performance meets acceptable thresholds
   */
  isPerformanceAcceptable(metrics: PerformanceMetrics): boolean {
    return (
      metrics.lcp <= PERFORMANCE_THRESHOLDS.LCP_GOOD * 1000 &&
      metrics.fid <= PERFORMANCE_THRESHOLDS.FID_GOOD &&
      metrics.cls <= PERFORMANCE_THRESHOLDS.CLS_GOOD &&
      metrics.lighthouseScore >= PERFORMANCE_THRESHOLDS.LIGHTHOUSE_MIN_SCORE
    );
  }
}
