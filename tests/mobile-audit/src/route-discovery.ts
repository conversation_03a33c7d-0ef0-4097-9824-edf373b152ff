import { promises as fs } from 'fs';
import path from 'path';
import { glob } from 'glob';
import { RouteConfig, TestConfig } from './types.js';
import { TEST_CONFIG } from './config.js';

/**
 * Route Discovery Module
 * Automatically scans src/pages/ directory and generates test URLs with mock data
 */

export class RouteDiscovery {
  private baseDir: string;
  private testConfig: TestConfig;

  constructor(baseDir: string = '../../src', testConfig: TestConfig = TEST_CONFIG) {
    this.baseDir = baseDir;
    this.testConfig = testConfig;
  }

  /**
   * Scan src/pages/ directory to discover all React components
   */
  async discoverPageComponents(): Promise<string[]> {
    const pagesDir = path.join(this.baseDir, 'pages');
    const pattern = path.join(pagesDir, '**/*.tsx');
    
    try {
      const files = await glob(pattern);
      return files.filter(file => {
        const basename = path.basename(file, '.tsx');
        // Filter out index files and test files
        return !basename.toLowerCase().includes('index') && 
               !basename.toLowerCase().includes('test') &&
               !basename.toLowerCase().includes('spec');
      });
    } catch (error) {
      console.error('Error discovering page components:', error);
      return [];
    }
  }

  /**
   * Extract route patterns from App.tsx
   */
  async extractRoutePatterns(): Promise<RouteConfig[]> {
    const appPath = path.join(this.baseDir, 'App.tsx');
    
    try {
      const appContent = await fs.readFile(appPath, 'utf-8');
      const routes: RouteConfig[] = [];
      
      // Extract route patterns using regex
      const routeRegex = /<Route\s+path="([^"]+)"/g;
      let match;
      
      while ((match = routeRegex.exec(appContent)) !== null) {
        const routePath = match[1];
        const routeConfig = this.createRouteConfig(routePath, appContent);
        if (routeConfig) {
          routes.push(routeConfig);
        }
      }
      
      return routes;
    } catch (error) {
      console.error('Error extracting route patterns:', error);
      return this.testConfig.routes; // Fallback to predefined routes
    }
  }

  /**
   * Create route configuration from path and context
   */
  private createRouteConfig(routePath: string, appContent: string): RouteConfig | null {
    // Skip wildcard routes
    if (routePath === '*') return null;
    
    // Determine if route requires authentication
    const requiresAuth = this.isProtectedRoute(routePath, appContent);
    
    // Extract component name from route context
    const componentName = this.extractComponentName(routePath, appContent);
    
    // Determine priority based on route importance
    const priority = this.determinePriority(routePath);
    
    // Handle dynamic parameters
    const dynamicParams = this.extractDynamicParams(routePath);
    
    return {
      path: routePath,
      name: componentName || this.pathToName(routePath),
      requiresAuth,
      dynamicParams,
      description: this.generateDescription(routePath, componentName),
      priority
    };
  }

  /**
   * Check if route is protected by authentication
   */
  private isProtectedRoute(routePath: string, appContent: string): boolean {
    // Look for SignedIn wrapper around the route
    const routeIndex = appContent.indexOf(`path="${routePath}"`);
    if (routeIndex === -1) return false;
    
    // Check for SignedIn component before the route
    const beforeRoute = appContent.substring(Math.max(0, routeIndex - 500), routeIndex);
    return beforeRoute.includes('<SignedIn>');
  }

  /**
   * Extract component name from route context
   */
  private extractComponentName(routePath: string, appContent: string): string | null {
    const routeIndex = appContent.indexOf(`path="${routePath}"`);
    if (routeIndex === -1) return null;
    
    // Look for Lazy component after the route
    const afterRoute = appContent.substring(routeIndex, routeIndex + 300);
    const lazyMatch = afterRoute.match(/<(Lazy\w+)/);
    
    if (lazyMatch) {
      return lazyMatch[1].replace('Lazy', '');
    }
    
    return null;
  }

  /**
   * Determine route priority based on path
   */
  private determinePriority(routePath: string): 'critical' | 'high' | 'medium' | 'low' {
    if (routePath === '/' || routePath.includes('/shared/')) return 'critical';
    if (routePath.includes('/project/') || routePath.includes('/create')) return 'high';
    if (routePath.includes('/sign-') || routePath.includes('/conversations')) return 'medium';
    return 'low';
  }

  /**
   * Extract dynamic parameters from route path
   */
  private extractDynamicParams(routePath: string): Record<string, string> | undefined {
    const params: Record<string, string> = {};
    const paramMatches = routePath.match(/:(\w+)/g);
    
    if (!paramMatches) return undefined;
    
    paramMatches.forEach(param => {
      const paramName = param.substring(1); // Remove ':'
      
      // Map parameter names to test values
      switch (paramName) {
        case 'projectId':
          params[paramName] = this.testConfig.mockData.projectIds[0];
          break;
        case 'sharedId':
          params[paramName] = this.testConfig.mockData.sharedIds[0];
          break;
        case 'userId':
          params[paramName] = this.testConfig.mockData.userIds[0];
          break;
        default:
          params[paramName] = `test-${paramName}`;
      }
    });
    
    return Object.keys(params).length > 0 ? params : undefined;
  }

  /**
   * Convert path to readable name
   */
  private pathToName(routePath: string): string {
    if (routePath === '/') return 'Dashboard';

    return routePath
      .split('/')
      .filter(segment => segment && !segment.startsWith(':'))
      .map(segment => {
        // Safety check for undefined segments
        if (!segment || typeof segment !== 'string') return '';
        return segment.charAt(0).toUpperCase() + segment.slice(1);
      })
      .filter(segment => segment) // Remove empty segments
      .join('');
  }

  /**
   * Generate description for route
   */
  private generateDescription(routePath: string, componentName: string | null): string {
    const descriptions: Record<string, string> = {
      '/': 'Main dashboard with project overview',
      '/shared/:sharedId': 'Customer-facing shared project view',
      '/sign-in': 'User authentication - sign in',
      '/sign-up': 'User authentication - sign up',
      '/create': 'Project creation form',
      '/create-wizard': 'Multi-step project creation wizard',
      '/project/:projectId': 'Project log entry and image upload',
      '/project/:projectId/details': 'Contractor project management panel',
      '/conversations': 'Chat conversations overview',
      '/archived-projects': 'Archived projects management'
    };
    
    return descriptions[routePath] || `${componentName || 'Page'} component`;
  }

  /**
   * Generate test URLs with resolved parameters
   */
  generateTestUrls(routes: RouteConfig[]): Array<{ route: RouteConfig; url: string }> {
    return routes.map(route => ({
      route,
      url: this.resolveRouteUrl(route)
    }));
  }

  /**
   * Resolve route path with dynamic parameters
   */
  private resolveRouteUrl(route: RouteConfig): string {
    let url = route.path;
    
    if (route.dynamicParams) {
      Object.entries(route.dynamicParams).forEach(([param, value]) => {
        url = url.replace(`:${param}`, value).replace(`{${param}}`, value);
      });
    }
    
    return url;
  }

  /**
   * Validate that test data exists for dynamic routes
   */
  async validateTestData(): Promise<{ valid: boolean; missing: string[] }> {
    const missing: string[] = [];
    
    // Check if test project IDs exist
    if (!this.testConfig.mockData.projectIds.length) {
      missing.push('Project IDs for testing /project/:projectId routes');
    }
    
    // Check if test shared IDs exist
    if (!this.testConfig.mockData.sharedIds.length) {
      missing.push('Shared IDs for testing /shared/:sharedId routes');
    }
    
    // Check if test user IDs exist
    if (!this.testConfig.mockData.userIds.length) {
      missing.push('User IDs for testing user-specific routes');
    }
    
    return {
      valid: missing.length === 0,
      missing
    };
  }

  /**
   * Get all routes with resolved URLs for testing
   */
  async getAllTestRoutes(): Promise<Array<{ route: RouteConfig; url: string }>> {
    const routes = await this.extractRoutePatterns();
    return this.generateTestUrls(routes);
  }
}
