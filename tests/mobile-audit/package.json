{"name": "jobblogg-mobile-audit", "version": "1.0.0", "description": "Comprehensive mobile responsiveness audit for JobbLogg React application", "type": "module", "scripts": {"audit": "tsx src/audit.ts", "audit:dev": "tsx src/audit.ts --env=development", "audit:prod": "tsx src/audit.ts --env=production", "audit:route": "tsx src/audit.ts --route", "audit:viewport": "tsx src/audit.ts --viewport", "report": "tsx src/report-generator.ts", "test": "playwright test", "test:headed": "playwright test --headed", "install-browsers": "playwright install"}, "dependencies": {"@playwright/test": "^1.40.0", "axe-core": "^4.8.0", "lighthouse": "^11.4.0", "tsx": "^4.6.0", "typescript": "^5.3.0", "chalk": "^5.3.0", "fs-extra": "^11.2.0", "glob": "^10.3.0", "yargs": "^17.7.0"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/node": "^20.10.0", "@types/yargs": "^17.0.32"}}