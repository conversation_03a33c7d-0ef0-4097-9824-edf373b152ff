# JobbLogg Mobile Responsiveness Audit Report

Generated: 9.7.2025, 12:56:32
Environment: development

## 📊 Executive Summary

- **Overall Score**: 0% (0/44 tests passed)
- **Routes Tested**: 11
- **Viewports Tested**: 4
- **Total Issues**: 57

### Issue Breakdown
- 🔴 Critical: 43
- 🟠 High: 0
- 🟡 Medium: 14
- 🟢 Low: 0

### Performance Metrics
- **Average Lighthouse Score**: 2/100
- **Average LCP**: 0ms
- **Average CLS**: 0

### JobbLogg Compliance
- **Design Token Usage**: 0%
- **Responsive Breakpoints**: 0%
- **Norwegian Localization**: 2%

## 🎯 Top Recommendations

1. Check browser console and network connectivity
2. Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units
3. Add responsive classes using JobbLogg breakpoints: xxs:, xs:, sm:, md:, lg:, xl:, 2xl:
4. Increase touch target size to minimum 44x44px. For ultra-narrow screens, consider using touch-target-compact class (36x36px minimum).
5. Add visible focus styles using focus:outline-* or focus:ring-* classes

## ⚡ Quick Fixes

- Add touch-target utility classes to buttons and interactive elements
- Update components to use JobbLogg design system tokens
- Add responsive breakpoint classes (xxs:, xs:, sm:, md:, lg:)

## 📱 Detailed Results by Viewport

### iPhone SE, older Android phones - Critical narrow width testing (320×568)

**Status**: 0/11 routes passed


#### PageWrapper
- **Status**: ⚠️ warning
- **Touch Target Issues**: 1
- **Layout Issues**: 12  
- **Accessibility Issues**: 1
- **Lighthouse Score**: 100/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100


### iPhone 12/13 mini - Most common mobile viewport (375×812)

**Status**: 0/11 routes passed


#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100


### iPhone 12/13 Pro Max - Large mobile screens (414×896)

**Status**: 0/11 routes passed


#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100


### iPad portrait - Tablet responsiveness (768×1024)

**Status**: 0/11 routes passed


#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100

#### PageWrapper
- **Status**: ❌ failed
- **Touch Target Issues**: 0
- **Layout Issues**: 1  
- **Accessibility Issues**: 0
- **Lighthouse Score**: 0/100


## 🔧 Implementation Guide

### Adding Touch Target Classes
```css
/* Add to components with small interactive elements */
.touch-target {
  min-width: 44px;
  min-height: 44px;
}

.touch-target-compact {
  min-width: 36px;
  min-height: 36px;
}
```

### Responsive Breakpoint Usage
```jsx
// Use JobbLogg custom breakpoints
<div className="p-2 xxs:p-3 xs:p-4 sm:p-6">
  <button className="text-sm xxs:text-base xs:text-lg">
    Button Text
  </button>
</div>
```

### Design Token Migration
```jsx
// Before
<div className="bg-blue-600 text-white">

// After  
<div className="bg-jobblogg-primary text-jobblogg-text-strong">
```

---

*Report generated by JobbLogg Mobile Audit Framework v1.0.0*
