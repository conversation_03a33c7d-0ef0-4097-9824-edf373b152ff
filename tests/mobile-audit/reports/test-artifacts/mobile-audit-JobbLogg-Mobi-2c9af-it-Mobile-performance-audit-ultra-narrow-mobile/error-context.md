# Page snapshot

```yaml
- img
- heading "Velkommen tilbake! 👋" [level=1]
- paragraph: Logg inn på JobbLogg-kontoen din og fortsett dokumenteringen
- heading "Velkommen tilbake! 👋" [level=1]
- paragraph: Logg inn på JobbLogg-kontoen din og fortsett dokumenteringen
- button "Sign in with Apple Apple":
  - img "Sign in with Apple"
  - text: Apple
- button "Sign in with Google Google":
  - img "Sign in with Google"
  - text: Google
- paragraph: eller
- text: E-postadresse
- textbox "E-postadresse"
- text: Passord
- textbox "Passord"
- button "Show password":
  - img
- button "Fortsett":
  - text: Fortsett
  - img
- text: Har du ikke konto ennå?
- link "Opprett konto":
  - /url: http://localhost:5173/sign-up
- paragraph: Secured by
- link "Clerk logo":
  - /url: https://go.clerk.com/components
  - img
- paragraph: Development mode
- paragraph: Har du ikke konto ennå? 🚀
- link "Opprett konto":
  - /url: /sign-up
  - button "Opprett konto":
    - img
    - text: Opprett konto
- img
- text: JobbLogg - Dokumenter arbeidet ditt enkelt
```