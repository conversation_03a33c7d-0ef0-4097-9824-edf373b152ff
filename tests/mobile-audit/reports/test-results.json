{"config": {"configFile": "/Users/<USER>/JobbLogg/tests/mobile-audit/playwright.config.ts", "rootDir": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 5}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "reports/playwright-html"}], ["json", {"outputFile": "reports/test-results.json"}], ["junit", {"outputFile": "reports/junit.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts", "repeatEach": 1, "retries": 1, "metadata": {"actualWorkers": 5}, "id": "ultra-narrow-mobile", "name": "ultra-narrow-mobile", "testDir": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts", "repeatEach": 1, "retries": 1, "metadata": {"actualWorkers": 5}, "id": "standard-mobile", "name": "standard-mobile", "testDir": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts", "repeatEach": 1, "retries": 1, "metadata": {"actualWorkers": 5}, "id": "large-mobile", "name": "large-mobile", "testDir": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts", "repeatEach": 1, "retries": 1, "metadata": {"actualWorkers": 5}, "id": "tablet-portrait", "name": "tablet-portrait", "testDir": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.2", "workers": 5, "webServer": {"command": "npm run dev", "url": "http://localhost:5173", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "mobile-audit.test.ts", "file": "mobile-audit.test.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "JobbLogg Mobile Responsiveness Audit", "file": "mobile-audit.test.ts", "line": 10, "column": 6, "specs": [{"title": "Complete mobile audit across all routes and viewports", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "ultra-narrow-mobile", "projectName": "ultra-narrow-mobile", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "timedOut", "duration": 30098, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}], "stdout": [{"text": "🚀 Starting JobbLogg Mobile Responsiveness Audit...\n"}, {"text": "📍 Found 11 routes to test\n"}, {"text": "📱 Testing viewport: iPhone SE, older Android phones - Critical narrow width testing (320×568)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "    ⚠️ warning - 14 issues\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPhone 12/13 mini - Most common mobile viewport (375×812)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPhone 12/13 Pro Max - Large mobile screens (414×896)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPad portrait - Tablet responsiveness (768×1024)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "\n📊 Audit completed: 44 tests run\n"}, {"text": "📝 Generating reports...\n"}], "stderr": [{"text": "    ❌ Error testing PageWrapper: browserContext.close: Test ended.\nBrowser logs:\n\n<launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window\n<launched> pid=69674\n[pid=69674] <gracefully close start>\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:171:7\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}], "retry": 0, "startTime": "2025-07-09T10:55:29.296Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile/test-failed-3.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile/error-context.md"}]}, {"workerIndex": 8, "parallelIndex": 0, "status": "timedOut", "duration": 30216, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}], "stdout": [{"text": "🚀 Starting JobbLogg Mobile Responsiveness Audit...\n"}, {"text": "📍 Found 11 routes to test\n"}, {"text": "📱 Testing viewport: iPhone SE, older Android phones - Critical narrow width testing (320×568)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "    ⚠️ warning - 14 issues\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPhone 12/13 mini - Most common mobile viewport (375×812)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPhone 12/13 Pro Max - Large mobile screens (414×896)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPad portrait - Tablet responsiveness (768×1024)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "\n📊 Audit completed: 44 tests run\n"}, {"text": "📝 Generating reports...\n"}, {"text": "✅ Reports generated:\n"}, {"text": "  📄 JSON: reports/mobile-audit-report.json\n"}, {"text": "  📝 Markdown: reports/mobile-audit-report.md\n"}, {"text": "  🌐 HTML: reports/mobile-audit-report.html\n"}, {"text": "🔧 Generating automated fix suggestions...\n"}, {"text": "✅ Generated 0 fix suggestions in 0 patch files\n"}, {"text": "\n📊 Audit Summary:\n"}, {"text": "  ✅ Passed: 0\n"}, {"text": "  ❌ Failed: 43\n"}, {"text": "  ⚠️  Warnings: 1\n"}, {"text": "  📄 Reports: 3\n"}, {"text": "  🔧 Fix suggestions: 0 files\n"}, {"text": "\n❌ 43 routes have critical mobile responsiveness issues\n"}, {"text": "📝 Check the generated reports for detailed recommendations\n"}], "stderr": [{"text": "Error collecting performance metrics: page.evaluate: Test ended.\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:16:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:136:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.checkMobilePerformanceIssues \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:174:33\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:156:41\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: page.evaluate: Target page, context or browser has been closed\n    at MobileAuditRunner.checkJobbLoggTokenUsage \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:179:23\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:137:45\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:17:21\n"}], "retry": 1, "startTime": "2025-07-09T10:56:01.536Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/test-failed-3.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/trace.zip"}]}], "status": "unexpected"}], "id": "93e1df048fe9de73fa28-3800f115e96c3913c825", "file": "mobile-audit.test.ts", "line": 12, "column": 3}, {"title": "Mobile audit for iPhone SE, older Android phones - Critical narrow width testing", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "ultra-narrow-mobile", "projectName": "ultra-narrow-mobile", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "timedOut", "duration": 30097, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 7, "line": 171}, "message": "Error: browserContext.close: Test ended.\nBrowser logs:\n\n<launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window\n<launched> pid=69675\n[pid=69675] <gracefully close start>\n\n\u001b[90m   at \u001b[39m../audit-runner.ts:171\n\n\u001b[0m \u001b[90m 169 |\u001b[39m\n \u001b[90m 170 |\u001b[39m     } \u001b[36mfinally\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 171 |\u001b[39m       \u001b[36mawait\u001b[39m context\u001b[33m.\u001b[39mclose()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 172 |\u001b[39m     }\n \u001b[90m 173 |\u001b[39m   }\n \u001b[90m 174 |\u001b[39m\u001b[0m\n\u001b[2m    at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)\u001b[22m\n\u001b[2m    at MobileAuditRunner.runViewportAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:339:22)\u001b[22m\n\u001b[2m    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:58:23\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-09T10:55:29.295Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile/test-failed-3.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile/error-context.md"}]}, {"workerIndex": 5, "parallelIndex": 1, "status": "timedOut", "duration": 30218, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 7, "line": 171}, "message": "Error: browserContext.close: Test ended.\nBrowser logs:\n\n<launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window\n<launched> pid=69779\n[pid=69779] <gracefully close start>\n\n\u001b[90m   at \u001b[39m../audit-runner.ts:171\n\n\u001b[0m \u001b[90m 169 |\u001b[39m\n \u001b[90m 170 |\u001b[39m     } \u001b[36mfinally\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 171 |\u001b[39m       \u001b[36mawait\u001b[39m context\u001b[33m.\u001b[39mclose()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 172 |\u001b[39m     }\n \u001b[90m 173 |\u001b[39m   }\n \u001b[90m 174 |\u001b[39m\u001b[0m\n\u001b[2m    at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)\u001b[22m\n\u001b[2m    at MobileAuditRunner.runViewportAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:339:22)\u001b[22m\n\u001b[2m    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:58:23\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-09T10:56:01.535Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/test-failed-3.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/trace.zip"}]}], "status": "unexpected"}], "id": "93e1df048fe9de73fa28-a57fb9a7f5a380834ee1", "file": "mobile-audit.test.ts", "line": 56, "column": 5}, {"title": "Mobile audit for iPhone 12/13 mini - Most common mobile viewport", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "ultra-narrow-mobile", "projectName": "ultra-narrow-mobile", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "timedOut", "duration": 30095, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 7, "line": 171}, "message": "Error: browserContext.close: Test ended.\nBrowser logs:\n\n<launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window\n<launched> pid=69676\n[pid=69676] <gracefully close start>\n\n\u001b[90m   at \u001b[39m../audit-runner.ts:171\n\n\u001b[0m \u001b[90m 169 |\u001b[39m\n \u001b[90m 170 |\u001b[39m     } \u001b[36mfinally\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 171 |\u001b[39m       \u001b[36mawait\u001b[39m context\u001b[33m.\u001b[39mclose()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 172 |\u001b[39m     }\n \u001b[90m 173 |\u001b[39m   }\n \u001b[90m 174 |\u001b[39m\u001b[0m\n\u001b[2m    at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)\u001b[22m\n\u001b[2m    at MobileAuditRunner.runViewportAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:339:22)\u001b[22m\n\u001b[2m    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:58:23\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-09T10:55:29.297Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile/test-failed-3.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile/error-context.md"}]}, {"workerIndex": 6, "parallelIndex": 2, "status": "timedOut", "duration": 30186, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 7, "line": 171}, "message": "Error: browserContext.close: Target page, context or browser has been closed\n\n\u001b[90m   at \u001b[39m../audit-runner.ts:171\n\n\u001b[0m \u001b[90m 169 |\u001b[39m\n \u001b[90m 170 |\u001b[39m     } \u001b[36mfinally\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 171 |\u001b[39m       \u001b[36mawait\u001b[39m context\u001b[33m.\u001b[39mclose()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 172 |\u001b[39m     }\n \u001b[90m 173 |\u001b[39m   }\n \u001b[90m 174 |\u001b[39m\u001b[0m\n\u001b[2m    at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)\u001b[22m\n\u001b[2m    at MobileAuditRunner.runViewportAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:339:22)\u001b[22m\n\u001b[2m    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:58:23\u001b[22m"}], "stdout": [], "stderr": [{"text": "Error collecting performance metrics: page.evaluate: Test ended.\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:16:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runViewportAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:339:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:58:23\n"}, {"text": "Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:136:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runViewportAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:339:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:58:23\n"}, {"text": "Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.checkMobilePerformanceIssues \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:174:33\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:156:41\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runViewportAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:339:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:58:23\n"}], "retry": 1, "startTime": "2025-07-09T10:56:01.537Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/test-failed-3.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/trace.zip"}]}], "status": "unexpected"}], "id": "93e1df048fe9de73fa28-906ff84ec342f1b2372c", "file": "mobile-audit.test.ts", "line": 56, "column": 5}, {"title": "Mobile audit for iPhone 12/13 Pro Max - Large mobile screens", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "ultra-narrow-mobile", "projectName": "ultra-narrow-mobile", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "timedOut", "duration": 30123, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 7, "line": 171}, "message": "Error: browserContext.close: Test ended.\nBrowser logs:\n\n<launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window\n<launched> pid=69677\n[pid=69677] <gracefully close start>\n\n\u001b[90m   at \u001b[39m../audit-runner.ts:171\n\n\u001b[0m \u001b[90m 169 |\u001b[39m\n \u001b[90m 170 |\u001b[39m     } \u001b[36mfinally\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 171 |\u001b[39m       \u001b[36mawait\u001b[39m context\u001b[33m.\u001b[39mclose()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 172 |\u001b[39m     }\n \u001b[90m 173 |\u001b[39m   }\n \u001b[90m 174 |\u001b[39m\u001b[0m\n\u001b[2m    at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)\u001b[22m\n\u001b[2m    at MobileAuditRunner.runViewportAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:339:22)\u001b[22m\n\u001b[2m    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:58:23\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-09T10:55:29.297Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile/test-failed-3.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile/error-context.md"}]}, {"workerIndex": 7, "parallelIndex": 3, "status": "timedOut", "duration": 30195, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 7, "line": 171}, "message": "Error: browserContext.close: Target page, context or browser has been closed\n\n\u001b[90m   at \u001b[39m../audit-runner.ts:171\n\n\u001b[0m \u001b[90m 169 |\u001b[39m\n \u001b[90m 170 |\u001b[39m     } \u001b[36mfinally\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 171 |\u001b[39m       \u001b[36mawait\u001b[39m context\u001b[33m.\u001b[39mclose()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 172 |\u001b[39m     }\n \u001b[90m 173 |\u001b[39m   }\n \u001b[90m 174 |\u001b[39m\u001b[0m\n\u001b[2m    at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)\u001b[22m\n\u001b[2m    at MobileAuditRunner.runViewportAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:339:22)\u001b[22m\n\u001b[2m    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:58:23\u001b[22m"}], "stdout": [], "stderr": [{"text": "Error collecting performance metrics: page.evaluate: Test ended.\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:16:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runViewportAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:339:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:58:23\n"}, {"text": "Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:136:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runViewportAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:339:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:58:23\n"}, {"text": "Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.checkMobilePerformanceIssues \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:174:33\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:156:41\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runViewportAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:339:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:58:23\n"}], "retry": 1, "startTime": "2025-07-09T10:56:01.535Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/test-failed-3.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/trace.zip"}]}], "status": "unexpected"}], "id": "93e1df048fe9de73fa28-e33da2ff9c0db4069d5f", "file": "mobile-audit.test.ts", "line": 56, "column": 5}, {"title": "Mobile audit for iPad portrait - Tablet responsiveness", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "ultra-narrow-mobile", "projectName": "ultra-narrow-mobile", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "timedOut", "duration": 30226, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 7, "line": 171}, "message": "Error: browserContext.close: Test ended.\nBrowser logs:\n\n<launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window\n<launched> pid=69673\n[pid=69673] <gracefully close start>\n\n\u001b[90m   at \u001b[39m../audit-runner.ts:171\n\n\u001b[0m \u001b[90m 169 |\u001b[39m\n \u001b[90m 170 |\u001b[39m     } \u001b[36mfinally\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 171 |\u001b[39m       \u001b[36mawait\u001b[39m context\u001b[33m.\u001b[39mclose()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 172 |\u001b[39m     }\n \u001b[90m 173 |\u001b[39m   }\n \u001b[90m 174 |\u001b[39m\u001b[0m\n\u001b[2m    at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)\u001b[22m\n\u001b[2m    at MobileAuditRunner.runViewportAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:339:22)\u001b[22m\n\u001b[2m    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:58:23\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-09T10:55:29.294Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile/test-failed-3.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile/error-context.md"}]}, {"workerIndex": 9, "parallelIndex": 4, "status": "timedOut", "duration": 30389, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 7, "line": 171}, "message": "Error: browserContext.close: Test ended.\nBrowser logs:\n\n<launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window\n<launched> pid=69776\n[pid=69776] <gracefully close start>\n\n\u001b[90m   at \u001b[39m../audit-runner.ts:171\n\n\u001b[0m \u001b[90m 169 |\u001b[39m\n \u001b[90m 170 |\u001b[39m     } \u001b[36mfinally\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 171 |\u001b[39m       \u001b[36mawait\u001b[39m context\u001b[33m.\u001b[39mclose()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 172 |\u001b[39m     }\n \u001b[90m 173 |\u001b[39m   }\n \u001b[90m 174 |\u001b[39m\u001b[0m\n\u001b[2m    at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)\u001b[22m\n\u001b[2m    at MobileAuditRunner.runViewportAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:339:22)\u001b[22m\n\u001b[2m    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:58:23\u001b[22m"}], "stdout": [], "stderr": [{"text": "Error collecting performance metrics: page.evaluate: Test ended.\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:16:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runViewportAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:339:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:58:23\n"}, {"text": "Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:136:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runViewportAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:339:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:58:23\n"}], "retry": 1, "startTime": "2025-07-09T10:56:01.535Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/test-failed-3.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/trace.zip"}]}], "status": "unexpected"}], "id": "93e1df048fe9de73fa28-a455ff2e8cd10a528ef9", "file": "mobile-audit.test.ts", "line": 56, "column": 5}, {"title": "Critical route audit: /dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "ultra-narrow-mobile", "projectName": "ultra-narrow-mobile", "results": [{"workerIndex": 10, "parallelIndex": 2, "status": "failed", "duration": 744, "error": {"message": "Error: Route not found: /dashboard", "stack": "Error: Route not found: /dashboard\n    at MobileAuditRunner.runRouteAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:308:13)\n    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:86:23", "location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 13, "line": 308}, "snippet": "\u001b[90m   at \u001b[39m../audit-runner.ts:308\n\n\u001b[0m \u001b[90m 306 |\u001b[39m\n \u001b[90m 307 |\u001b[39m     \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mtargetRouteInfo) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 308 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Route not found: ${routePath}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 309 |\u001b[39m     }\n \u001b[90m 310 |\u001b[39m\n \u001b[90m 311 |\u001b[39m     \u001b[36mconst\u001b[39m viewports \u001b[33m=\u001b[39m viewportName\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 13, "line": 308}, "message": "Error: Route not found: /dashboard\n\n\u001b[90m   at \u001b[39m../audit-runner.ts:308\n\n\u001b[0m \u001b[90m 306 |\u001b[39m\n \u001b[90m 307 |\u001b[39m     \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mtargetRouteInfo) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 308 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Route not found: ${routePath}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 309 |\u001b[39m     }\n \u001b[90m 310 |\u001b[39m\n \u001b[90m 311 |\u001b[39m     \u001b[36mconst\u001b[39m viewports \u001b[33m=\u001b[39m viewportName\u001b[0m\n\u001b[2m    at MobileAuditRunner.runRouteAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:308:13)\u001b[22m\n\u001b[2m    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:86:23\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-09T10:56:32.876Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-93b46-tical-route-audit-dashboard-ultra-narrow-mobile/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-93b46-tical-route-audit-dashboard-ultra-narrow-mobile/video.webm"}], "errorLocation": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 13, "line": 308}}, {"workerIndex": 15, "parallelIndex": 2, "status": "failed", "duration": 261, "error": {"message": "Error: Route not found: /dashboard", "stack": "Error: Route not found: /dashboard\n    at MobileAuditRunner.runRouteAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:308:13)\n    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:86:23", "location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 13, "line": 308}, "snippet": "\u001b[90m   at \u001b[39m../audit-runner.ts:308\n\n\u001b[0m \u001b[90m 306 |\u001b[39m\n \u001b[90m 307 |\u001b[39m     \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mtargetRouteInfo) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 308 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Route not found: ${routePath}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 309 |\u001b[39m     }\n \u001b[90m 310 |\u001b[39m\n \u001b[90m 311 |\u001b[39m     \u001b[36mconst\u001b[39m viewports \u001b[33m=\u001b[39m viewportName\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 13, "line": 308}, "message": "Error: Route not found: /dashboard\n\n\u001b[90m   at \u001b[39m../audit-runner.ts:308\n\n\u001b[0m \u001b[90m 306 |\u001b[39m\n \u001b[90m 307 |\u001b[39m     \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mtargetRouteInfo) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 308 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Route not found: ${routePath}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 309 |\u001b[39m     }\n \u001b[90m 310 |\u001b[39m\n \u001b[90m 311 |\u001b[39m     \u001b[36mconst\u001b[39m viewports \u001b[33m=\u001b[39m viewportName\u001b[0m\n\u001b[2m    at MobileAuditRunner.runRouteAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:308:13)\u001b[22m\n\u001b[2m    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:86:23\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-09T10:56:34.745Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-93b46-tical-route-audit-dashboard-ultra-narrow-mobile-retry1/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-93b46-tical-route-audit-dashboard-ultra-narrow-mobile-retry1/video.webm"}, {"name": "trace", "contentType": "application/zip", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-93b46-tical-route-audit-dashboard-ultra-narrow-mobile-retry1/trace.zip"}], "errorLocation": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 13, "line": 308}}], "status": "unexpected"}], "id": "93e1df048fe9de73fa28-57adc88568821fb7e937", "file": "mobile-audit.test.ts", "line": 84, "column": 5}, {"title": "Critical route audit: /project/:projectId", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "ultra-narrow-mobile", "projectName": "ultra-narrow-mobile", "results": [{"workerIndex": 11, "parallelIndex": 1, "status": "timedOut", "duration": 30124, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 23, "line": 179}, "message": "Error: page.evaluate: Target page, context or browser has been closed\n\n\u001b[90m   at \u001b[39m../audit-runner.ts:179\n\n\u001b[0m \u001b[90m 177 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 178 |\u001b[39m   \u001b[36mprivate\u001b[39m \u001b[36masync\u001b[39m checkJobbLoggTokenUsage(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m)\u001b[33m:\u001b[39m \u001b[33mPromise\u001b[39m\u001b[33m<\u001b[39m\u001b[33mboolean\u001b[39m\u001b[33m>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 179 |\u001b[39m     \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mevaluate(() \u001b[33m=>\u001b[39m {\n \u001b[90m     |\u001b[39m                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 180 |\u001b[39m       \u001b[36mconst\u001b[39m elements \u001b[33m=\u001b[39m document\u001b[33m.\u001b[39mquerySelectorAll(\u001b[32m'*'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 181 |\u001b[39m       \u001b[36mlet\u001b[39m jobbLoggTokenCount \u001b[33m=\u001b[39m \u001b[35m0\u001b[39m\u001b[33m;\u001b[39m\n \u001b[90m 182 |\u001b[39m       \u001b[36mlet\u001b[39m totalColorClasses \u001b[33m=\u001b[39m \u001b[35m0\u001b[39m\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at MobileAuditRunner.checkJobbLoggTokenUsage (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:179:23)\u001b[22m\n\u001b[2m    at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:137:45)\u001b[22m\n\u001b[2m    at MobileAuditRunner.runRouteAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:318:22)\u001b[22m\n\u001b[2m    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:86:23\u001b[22m"}], "stdout": [], "stderr": [{"text": "Error collecting performance metrics: page.evaluate: Test ended.\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:16:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runRouteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:318:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:86:23\n"}, {"text": "Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:136:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runRouteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:318:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:86:23\n"}, {"text": "Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.checkMobilePerformanceIssues \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:174:33\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:156:41\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runRouteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:318:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:86:23\n"}], "retry": 0, "startTime": "2025-07-09T10:56:32.874Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile/test-failed-3.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile/error-context.md"}]}, {"workerIndex": 20, "parallelIndex": 1, "status": "interrupted", "duration": 29302, "error": {"message": "Error: page.evaluate: Target page, context or browser has been closed", "stack": "Error: page.evaluate: Target page, context or browser has been closed\n    at MobileAuditRunner.checkJobbLoggTokenUsage (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:179:23)\n    at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:137:45)\n    at MobileAuditRunner.runRouteAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:318:22)\n    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:86:23", "location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 23, "line": 179}, "snippet": "\u001b[90m   at \u001b[39m../audit-runner.ts:179\n\n\u001b[0m \u001b[90m 177 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 178 |\u001b[39m   \u001b[36mprivate\u001b[39m \u001b[36masync\u001b[39m checkJobbLoggTokenUsage(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m)\u001b[33m:\u001b[39m \u001b[33mPromise\u001b[39m\u001b[33m<\u001b[39m\u001b[33mboolean\u001b[39m\u001b[33m>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 179 |\u001b[39m     \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mevaluate(() \u001b[33m=>\u001b[39m {\n \u001b[90m     |\u001b[39m                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 180 |\u001b[39m       \u001b[36mconst\u001b[39m elements \u001b[33m=\u001b[39m document\u001b[33m.\u001b[39mquerySelectorAll(\u001b[32m'*'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 181 |\u001b[39m       \u001b[36mlet\u001b[39m jobbLoggTokenCount \u001b[33m=\u001b[39m \u001b[35m0\u001b[39m\u001b[33m;\u001b[39m\n \u001b[90m 182 |\u001b[39m       \u001b[36mlet\u001b[39m totalColorClasses \u001b[33m=\u001b[39m \u001b[35m0\u001b[39m\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 23, "line": 179}, "message": "Error: page.evaluate: Target page, context or browser has been closed\n\n\u001b[90m   at \u001b[39m../audit-runner.ts:179\n\n\u001b[0m \u001b[90m 177 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 178 |\u001b[39m   \u001b[36mprivate\u001b[39m \u001b[36masync\u001b[39m checkJobbLoggTokenUsage(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m)\u001b[33m:\u001b[39m \u001b[33mPromise\u001b[39m\u001b[33m<\u001b[39m\u001b[33mboolean\u001b[39m\u001b[33m>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 179 |\u001b[39m     \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mevaluate(() \u001b[33m=>\u001b[39m {\n \u001b[90m     |\u001b[39m                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 180 |\u001b[39m       \u001b[36mconst\u001b[39m elements \u001b[33m=\u001b[39m document\u001b[33m.\u001b[39mquerySelectorAll(\u001b[32m'*'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 181 |\u001b[39m       \u001b[36mlet\u001b[39m jobbLoggTokenCount \u001b[33m=\u001b[39m \u001b[35m0\u001b[39m\u001b[33m;\u001b[39m\n \u001b[90m 182 |\u001b[39m       \u001b[36mlet\u001b[39m totalColorClasses \u001b[33m=\u001b[39m \u001b[35m0\u001b[39m\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at MobileAuditRunner.checkJobbLoggTokenUsage (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:179:23)\u001b[22m\n\u001b[2m    at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:137:45)\u001b[22m\n\u001b[2m    at MobileAuditRunner.runRouteAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:318:22)\u001b[22m\n\u001b[2m    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:86:23\u001b[22m"}], "stdout": [], "stderr": [{"text": "Error collecting performance metrics: page.evaluate: Test ended.\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:16:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runRouteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:318:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:86:23\n"}, {"text": "Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:136:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runRouteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:318:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:86:23\n"}, {"text": "Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.checkMobilePerformanceIssues \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:174:33\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:156:41\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runRouteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:318:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:86:23\n"}], "retry": 1, "startTime": "2025-07-09T10:57:03.828Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile-retry1/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile-retry1/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile-retry1/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile-retry1/test-failed-3.png"}, {"name": "trace", "contentType": "application/zip", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile-retry1/trace.zip"}], "errorLocation": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 23, "line": 179}}], "status": "unexpected"}], "id": "93e1df048fe9de73fa28-bf6e728b24538f1090d6", "file": "mobile-audit.test.ts", "line": 84, "column": 5}, {"title": "Critical route audit: /shared/:sharedId", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "ultra-narrow-mobile", "projectName": "ultra-narrow-mobile", "results": [{"workerIndex": 12, "parallelIndex": 3, "status": "timedOut", "duration": 30091, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 7, "line": 171}, "message": "Error: browserContext.close: Test ended.\nBrowser logs:\n\n<launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window\n<launched> pid=69875\n[pid=69875] <gracefully close start>\n\n\u001b[90m   at \u001b[39m../audit-runner.ts:171\n\n\u001b[0m \u001b[90m 169 |\u001b[39m\n \u001b[90m 170 |\u001b[39m     } \u001b[36mfinally\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 171 |\u001b[39m       \u001b[36mawait\u001b[39m context\u001b[33m.\u001b[39mclose()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 172 |\u001b[39m     }\n \u001b[90m 173 |\u001b[39m   }\n \u001b[90m 174 |\u001b[39m\u001b[0m\n\u001b[2m    at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)\u001b[22m\n\u001b[2m    at MobileAuditRunner.runRouteAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:318:22)\u001b[22m\n\u001b[2m    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:86:23\u001b[22m"}], "stdout": [], "stderr": [{"text": "Error collecting performance metrics: page.evaluate: Test ended.\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:16:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runRouteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:318:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:86:23\n"}, {"text": "Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:136:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runRouteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:318:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:86:23\n"}, {"text": "Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.checkMobilePerformanceIssues \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:174:33\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:156:41\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runRouteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:318:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:86:23\n"}], "retry": 0, "startTime": "2025-07-09T10:56:32.874Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile/test-failed-3.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile/error-context.md"}]}, {"workerIndex": 17, "parallelIndex": 3, "status": "interrupted", "duration": 29511, "error": {"message": "Error: page.evaluate: Target page, context or browser has been closed", "stack": "Error: page.evaluate: Target page, context or browser has been closed\n    at MobileAuditRunner.checkJobbLoggTokenUsage (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:179:23)\n    at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:137:45)\n    at MobileAuditRunner.runRouteAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:318:22)\n    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:86:23", "location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 23, "line": 179}, "snippet": "\u001b[90m   at \u001b[39m../audit-runner.ts:179\n\n\u001b[0m \u001b[90m 177 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 178 |\u001b[39m   \u001b[36mprivate\u001b[39m \u001b[36masync\u001b[39m checkJobbLoggTokenUsage(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m)\u001b[33m:\u001b[39m \u001b[33mPromise\u001b[39m\u001b[33m<\u001b[39m\u001b[33mboolean\u001b[39m\u001b[33m>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 179 |\u001b[39m     \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mevaluate(() \u001b[33m=>\u001b[39m {\n \u001b[90m     |\u001b[39m                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 180 |\u001b[39m       \u001b[36mconst\u001b[39m elements \u001b[33m=\u001b[39m document\u001b[33m.\u001b[39mquerySelectorAll(\u001b[32m'*'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 181 |\u001b[39m       \u001b[36mlet\u001b[39m jobbLoggTokenCount \u001b[33m=\u001b[39m \u001b[35m0\u001b[39m\u001b[33m;\u001b[39m\n \u001b[90m 182 |\u001b[39m       \u001b[36mlet\u001b[39m totalColorClasses \u001b[33m=\u001b[39m \u001b[35m0\u001b[39m\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 23, "line": 179}, "message": "Error: page.evaluate: Target page, context or browser has been closed\n\n\u001b[90m   at \u001b[39m../audit-runner.ts:179\n\n\u001b[0m \u001b[90m 177 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 178 |\u001b[39m   \u001b[36mprivate\u001b[39m \u001b[36masync\u001b[39m checkJobbLoggTokenUsage(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m)\u001b[33m:\u001b[39m \u001b[33mPromise\u001b[39m\u001b[33m<\u001b[39m\u001b[33mboolean\u001b[39m\u001b[33m>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 179 |\u001b[39m     \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mevaluate(() \u001b[33m=>\u001b[39m {\n \u001b[90m     |\u001b[39m                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 180 |\u001b[39m       \u001b[36mconst\u001b[39m elements \u001b[33m=\u001b[39m document\u001b[33m.\u001b[39mquerySelectorAll(\u001b[32m'*'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 181 |\u001b[39m       \u001b[36mlet\u001b[39m jobbLoggTokenCount \u001b[33m=\u001b[39m \u001b[35m0\u001b[39m\u001b[33m;\u001b[39m\n \u001b[90m 182 |\u001b[39m       \u001b[36mlet\u001b[39m totalColorClasses \u001b[33m=\u001b[39m \u001b[35m0\u001b[39m\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at MobileAuditRunner.checkJobbLoggTokenUsage (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:179:23)\u001b[22m\n\u001b[2m    at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:137:45)\u001b[22m\n\u001b[2m    at MobileAuditRunner.runRouteAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:318:22)\u001b[22m\n\u001b[2m    at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:86:23\u001b[22m"}], "stdout": [], "stderr": [{"text": "Error collecting performance metrics: page.evaluate: Test ended.\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:16:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runRouteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:318:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:86:23\n"}, {"text": "Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:136:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runRouteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:318:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:86:23\n"}, {"text": "Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.checkMobilePerformanceIssues \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:174:33\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:156:41\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runRouteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:318:22\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:86:23\n"}], "retry": 1, "startTime": "2025-07-09T10:57:03.829Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile-retry1/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile-retry1/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile-retry1/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile-retry1/test-failed-3.png"}, {"name": "trace", "contentType": "application/zip", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile-retry1/trace.zip"}], "errorLocation": {"file": "/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts", "column": 23, "line": 179}}], "status": "unexpected"}], "id": "93e1df048fe9de73fa28-5626ad85b186c4e112b7", "file": "mobile-audit.test.ts", "line": 84, "column": 5}, {"title": "Mobile performance audit", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "ultra-narrow-mobile", "projectName": "ultra-narrow-mobile", "results": [{"workerIndex": 13, "parallelIndex": 0, "status": "timedOut", "duration": 30112, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}], "stdout": [{"text": "🚀 Starting JobbLogg Mobile Responsiveness Audit...\n"}, {"text": "📍 Found 11 routes to test\n"}, {"text": "📱 Testing viewport: iPhone SE, older Android phones - Critical narrow width testing (320×568)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "    ⚠️ warning - 14 issues\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPhone 12/13 mini - Most common mobile viewport (375×812)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPhone 12/13 Pro Max - Large mobile screens (414×896)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPad portrait - Tablet responsiveness (768×1024)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}], "stderr": [{"text": "Error collecting performance metrics: page.evaluate: Test ended.\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:16:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:136:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.checkMobilePerformanceIssues \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:174:33\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:156:41\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: page.evaluate: Target page, context or browser has been closed\n    at MobileAuditRunner.checkJobbLoggTokenUsage \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:179:23\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:137:45\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}], "retry": 0, "startTime": "2025-07-09T10:56:32.875Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile/test-failed-3.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile/error-context.md"}]}, {"workerIndex": 19, "parallelIndex": 0, "status": "interrupted", "duration": 29479, "errors": [], "stdout": [{"text": "🚀 Starting JobbLogg Mobile Responsiveness Audit...\n"}, {"text": "📍 Found 11 routes to test\n"}, {"text": "📱 Testing viewport: iPhone SE, older Android phones - Critical narrow width testing (320×568)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "    ⚠️ warning - 14 issues\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPhone 12/13 mini - Most common mobile viewport (375×812)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPhone 12/13 Pro Max - Large mobile screens (414×896)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPad portrait - Tablet responsiveness (768×1024)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "\n📊 Audit completed: 44 tests run\n"}, {"text": "\n⚡ Performance Metrics:\n"}, {"text": "  LCP (Largest Contentful Paint): 0ms\n"}, {"text": "  CLS (Cumulative Layout Shift): 0.000\n"}, {"text": "  Average Lighthouse Score: 2/100\n"}, {"text": "  LCP Status: ✅ Good\n"}, {"text": "  CLS Status: ✅ Good\n"}, {"text": "  Lighthouse Status: ⚠️ Needs improvement\n"}], "stderr": [{"text": "Error collecting performance metrics: page.evaluate: Test ended.\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:16:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:136:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.checkMobilePerformanceIssues \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:174:33\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:156:41\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browserContext.close: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:171:7\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:108:21\n"}], "retry": 1, "startTime": "2025-07-09T10:57:03.828Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile-retry1/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile-retry1/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile-retry1/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile-retry1/test-failed-3.png"}, {"name": "trace", "contentType": "application/zip", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile-retry1/trace.zip"}]}], "status": "unexpected"}], "id": "93e1df048fe9de73fa28-f14f405bf10aba51d151", "file": "mobile-audit.test.ts", "line": 106, "column": 3}, {"title": "JobbLogg design system compliance", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "ultra-narrow-mobile", "projectName": "ultra-narrow-mobile", "results": [{"workerIndex": 14, "parallelIndex": 4, "status": "timedOut", "duration": 30116, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}], "stdout": [{"text": "🚀 Starting JobbLogg Mobile Responsiveness Audit...\n"}, {"text": "📍 Found 11 routes to test\n"}, {"text": "📱 Testing viewport: iPhone SE, older Android phones - Critical narrow width testing (320×568)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "    ⚠️ warning - 14 issues\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPhone 12/13 mini - Most common mobile viewport (375×812)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPhone 12/13 Pro Max - Large mobile screens (414×896)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPad portrait - Tablet responsiveness (768×1024)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "\n📊 Audit completed: 44 tests run\n"}, {"text": "\n🎨 JobbLogg Design System Compliance:\n"}, {"text": "  Design Token Usage: 0%\n"}, {"text": "  Responsive Breakpoints: 0%\n"}, {"text": "  Norwegian Text Support: 2%\n"}, {"text": "  Token Status: ⚠️ Needs improvement\n"}, {"text": "  Responsive Status: ⚠️ Needs improvement\n"}, {"text": "  Norwegian Status: ⚠️ Needs improvement\n"}], "stderr": [{"text": "Error collecting performance metrics: page.evaluate: Test ended.\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:16:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:136:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.checkMobilePerformanceIssues \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:174:33\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:156:41\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browserContext.close: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:171:7\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}], "retry": 0, "startTime": "2025-07-09T10:56:32.876Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile/test-failed-3.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile/error-context.md"}]}, {"workerIndex": 18, "parallelIndex": 4, "status": "interrupted", "duration": 29314, "errors": [], "stdout": [{"text": "🚀 Starting JobbLogg Mobile Responsiveness Audit...\n"}, {"text": "📍 Found 11 routes to test\n"}, {"text": "📱 Testing viewport: iPhone SE, older Android phones - Critical narrow width testing (320×568)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "    ⚠️ warning - 14 issues\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPhone 12/13 mini - Most common mobile viewport (375×812)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPhone 12/13 Pro Max - Large mobile screens (414×896)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPad portrait - Tablet responsiveness (768×1024)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "\n📊 Audit completed: 44 tests run\n"}, {"text": "\n🎨 JobbLogg Design System Compliance:\n"}, {"text": "  Design Token Usage: 0%\n"}, {"text": "  Responsive Breakpoints: 0%\n"}, {"text": "  Norwegian Text Support: 2%\n"}, {"text": "  Token Status: ⚠️ Needs improvement\n"}, {"text": "  Responsive Status: ⚠️ Needs improvement\n"}, {"text": "  Norwegian Status: ⚠️ Needs improvement\n"}], "stderr": [{"text": "Error collecting performance metrics: page.evaluate: Test ended.\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:16:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:136:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.checkMobilePerformanceIssues \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:174:33\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:156:41\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: page.evaluate: Target page, context or browser has been closed\n    at MobileAuditRunner.checkJobbLoggTokenUsage \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:179:23\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:137:45\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:133:21\n"}], "retry": 1, "startTime": "2025-07-09T10:57:03.829Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile-retry1/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile-retry1/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile-retry1/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile-retry1/test-failed-3.png"}, {"name": "trace", "contentType": "application/zip", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile-retry1/trace.zip"}]}], "status": "unexpected"}], "id": "93e1df048fe9de73fa28-012b64672bf02c69bb6a", "file": "mobile-audit.test.ts", "line": 131, "column": 3}, {"title": "Touch target WCAG AA compliance", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "ultra-narrow-mobile", "projectName": "ultra-narrow-mobile", "results": [{"workerIndex": 16, "parallelIndex": 2, "status": "timedOut", "duration": 30054, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}], "stdout": [{"text": "🚀 Starting JobbLogg Mobile Responsiveness Audit...\n"}, {"text": "📍 Found 11 routes to test\n"}, {"text": "📱 Testing viewport: iPhone SE, older Android phones - Critical narrow width testing (320×568)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "    ⚠️ warning - 14 issues\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPhone 12/13 mini - Most common mobile viewport (375×812)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPhone 12/13 Pro Max - Large mobile screens (414×896)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPad portrait - Tablet responsiveness (768×1024)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}], "stderr": [{"text": "Error collecting performance metrics: page.evaluate: Test ended.\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:16:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:136:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.checkMobilePerformanceIssues \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:174:33\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:156:41\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browserContext.close: Test ended.\nBrowser logs:\n\n<launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window\n<launched> pid=69952\n[pid=69952] <gracefully close start>\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:171:7\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}], "retry": 0, "startTime": "2025-07-09T10:56:35.619Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile/test-failed-3.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile/error-context.md"}]}, {"workerIndex": 21, "parallelIndex": 2, "status": "interrupted", "duration": 26922, "errors": [], "stdout": [{"text": "🚀 Starting JobbLogg Mobile Responsiveness Audit...\n"}, {"text": "📍 Found 11 routes to test\n"}, {"text": "📱 Testing viewport: iPhone SE, older Android phones - Critical narrow width testing (320×568)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "    ⚠️ warning - 14 issues\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPhone 12/13 mini - Most common mobile viewport (375×812)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPhone 12/13 Pro Max - Large mobile screens (414×896)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "📱 Testing viewport: iPad portrait - Tablet responsiveness (768×1024)\n"}, {"text": "  🔍 Testing route: PageWrapper (/shared/:sharedId)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-in)\n"}, {"text": "  🔍 Testing route: PageWrapper (/sign-up)\n"}, {"text": "  🔍 Testing route: PageWrapper (/)\n"}, {"text": "  🔍 Testing route: PageWrapper (/archived-projects)\n"}, {"text": "  🔍 Testing route: PageWrapper (/conversations)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create)\n"}, {"text": "  🔍 Testing route: PageWrapper (/create-wizard)\n"}, {"text": "  🔍 Testing route: PageWrapper (/test-google-maps)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId/details)\n"}, {"text": "  🔍 Testing route: PageWrapper (/project/:projectId)\n"}, {"text": "\n📊 Audit completed: 44 tests run\n"}, {"text": "\n👆 Touch Target Compliance:\n"}, {"text": "  Total touch target issues: 1\n"}, {"text": "  Critical touch target issues: 0\n"}, {"text": "\n⚠️  Touch target issues found. Common fixes:\n"}, {"text": "  - Add 'touch-target' class to small buttons\n"}, {"text": "  - Increase padding on interactive elements\n"}, {"text": "  - Ensure minimum 44x44px touch area\n"}], "stderr": [{"text": "Error collecting performance metrics: page.evaluate: Test ended.\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:16:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.collectPerformanceMetrics \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:35:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:136:34\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed\n    at PerformanceValidation.checkMobilePerformanceIssues \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:174:33\u001b[90m)\u001b[39m\n    at PerformanceValidation.runLighthouseAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:156:41\u001b[90m)\u001b[39m\n    at PerformanceValidation.validate \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/validation-modules/performance-validation.ts:19:29\u001b[90m)\u001b[39m\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:129:90\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browserContext.close: Test ended.\nBrowser logs:\n\n<launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window\n<launched> pid=70056\n[pid=70056] <gracefully close start>\n[pid=70056] <process did exit: exitCode=0, signal=null>\n[pid=70056] starting temporary directories cleanup\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:171:7\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}, {"text": "    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed\n    at MobileAuditRunner.auditRoute \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:107:21\u001b[90m)\u001b[39m\n    at MobileAuditRunner.runCompleteAudit \u001b[90m(/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/audit-runner.ts:57:26\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/JobbLogg/tests/mobile-audit/\u001b[39msrc/tests/mobile-audit.test.ts:157:21\n"}], "retry": 1, "startTime": "2025-07-09T10:57:06.304Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile-retry1/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile-retry1/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile-retry1/video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile-retry1/test-failed-3.png"}, {"name": "trace", "contentType": "application/zip", "path": "/Users/<USER>/JobbLogg/tests/mobile-audit/reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile-retry1/trace.zip"}]}], "status": "unexpected"}], "id": "93e1df048fe9de73fa28-d0f4c5fb70215442bff4", "file": "mobile-audit.test.ts", "line": 155, "column": 3}, {"title": "Mobile layout overflow prevention", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "ultra-narrow-mobile", "projectName": "ultra-narrow-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-c426912f6d7e05b3b73d", "file": "mobile-audit.test.ts", "line": 180, "column": 3}, {"title": "Complete mobile audit across all routes and viewports", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "standard-mobile", "projectName": "standard-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-e7a123870568b7a982a6", "file": "mobile-audit.test.ts", "line": 12, "column": 3}, {"title": "Mobile audit for iPhone SE, older Android phones - Critical narrow width testing", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "standard-mobile", "projectName": "standard-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-82d74f285ddc747f031f", "file": "mobile-audit.test.ts", "line": 56, "column": 5}, {"title": "Mobile audit for iPhone 12/13 mini - Most common mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "standard-mobile", "projectName": "standard-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-5a6ca2a7fa66385f725e", "file": "mobile-audit.test.ts", "line": 56, "column": 5}, {"title": "Mobile audit for iPhone 12/13 Pro Max - Large mobile screens", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "standard-mobile", "projectName": "standard-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-82949eb0b32ff6a3189a", "file": "mobile-audit.test.ts", "line": 56, "column": 5}, {"title": "Mobile audit for iPad portrait - Tablet responsiveness", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "standard-mobile", "projectName": "standard-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-2e138050d7aa0dbc1527", "file": "mobile-audit.test.ts", "line": 56, "column": 5}, {"title": "Critical route audit: /dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "standard-mobile", "projectName": "standard-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-3d20c41a9eef92d2c60b", "file": "mobile-audit.test.ts", "line": 84, "column": 5}, {"title": "Critical route audit: /project/:projectId", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "standard-mobile", "projectName": "standard-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-33aa26fbd5036c89a815", "file": "mobile-audit.test.ts", "line": 84, "column": 5}, {"title": "Critical route audit: /shared/:sharedId", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "standard-mobile", "projectName": "standard-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-025d679891826e6c5a46", "file": "mobile-audit.test.ts", "line": 84, "column": 5}, {"title": "Mobile performance audit", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "standard-mobile", "projectName": "standard-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-97ba401d8e4b2cba26c9", "file": "mobile-audit.test.ts", "line": 106, "column": 3}, {"title": "JobbLogg design system compliance", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "standard-mobile", "projectName": "standard-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-ca85bd2499e5fe02f191", "file": "mobile-audit.test.ts", "line": 131, "column": 3}, {"title": "Touch target WCAG AA compliance", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "standard-mobile", "projectName": "standard-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-0e096530ff769b0d96d2", "file": "mobile-audit.test.ts", "line": 155, "column": 3}, {"title": "Mobile layout overflow prevention", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "standard-mobile", "projectName": "standard-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-794d2127f4b15444c718", "file": "mobile-audit.test.ts", "line": 180, "column": 3}, {"title": "Complete mobile audit across all routes and viewports", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "large-mobile", "projectName": "large-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-2ab7b76a179ee90c378d", "file": "mobile-audit.test.ts", "line": 12, "column": 3}, {"title": "Mobile audit for iPhone SE, older Android phones - Critical narrow width testing", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "large-mobile", "projectName": "large-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-b9044145c61bbc42e4ce", "file": "mobile-audit.test.ts", "line": 56, "column": 5}, {"title": "Mobile audit for iPhone 12/13 mini - Most common mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "large-mobile", "projectName": "large-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-f23b83558d7bcbabc077", "file": "mobile-audit.test.ts", "line": 56, "column": 5}, {"title": "Mobile audit for iPhone 12/13 Pro Max - Large mobile screens", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "large-mobile", "projectName": "large-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-9a25b17439e05a8e5e7e", "file": "mobile-audit.test.ts", "line": 56, "column": 5}, {"title": "Mobile audit for iPad portrait - Tablet responsiveness", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "large-mobile", "projectName": "large-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-db5f186dc4643a3ed484", "file": "mobile-audit.test.ts", "line": 56, "column": 5}, {"title": "Critical route audit: /dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "large-mobile", "projectName": "large-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-7aa1785ac28ca5f872ab", "file": "mobile-audit.test.ts", "line": 84, "column": 5}, {"title": "Critical route audit: /project/:projectId", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "large-mobile", "projectName": "large-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-48958e35bff91daf8795", "file": "mobile-audit.test.ts", "line": 84, "column": 5}, {"title": "Critical route audit: /shared/:sharedId", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "large-mobile", "projectName": "large-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-6c9919b880bf37e8b23f", "file": "mobile-audit.test.ts", "line": 84, "column": 5}, {"title": "Mobile performance audit", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "large-mobile", "projectName": "large-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-f47043ed0da0789c823a", "file": "mobile-audit.test.ts", "line": 106, "column": 3}, {"title": "JobbLogg design system compliance", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "large-mobile", "projectName": "large-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-36aa2e0ef95e1ec36a08", "file": "mobile-audit.test.ts", "line": 131, "column": 3}, {"title": "Touch target WCAG AA compliance", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "large-mobile", "projectName": "large-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-05ba5ae9956494c98460", "file": "mobile-audit.test.ts", "line": 155, "column": 3}, {"title": "Mobile layout overflow prevention", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "large-mobile", "projectName": "large-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-7f6001ac76771daa62af", "file": "mobile-audit.test.ts", "line": 180, "column": 3}, {"title": "Complete mobile audit across all routes and viewports", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "tablet-portrait", "projectName": "tablet-portrait", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-74c21c6a70ea0eb4110d", "file": "mobile-audit.test.ts", "line": 12, "column": 3}, {"title": "Mobile audit for iPhone SE, older Android phones - Critical narrow width testing", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "tablet-portrait", "projectName": "tablet-portrait", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-270db7540757575fac11", "file": "mobile-audit.test.ts", "line": 56, "column": 5}, {"title": "Mobile audit for iPhone 12/13 mini - Most common mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "tablet-portrait", "projectName": "tablet-portrait", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-8f6adb6b6bda7405f4bf", "file": "mobile-audit.test.ts", "line": 56, "column": 5}, {"title": "Mobile audit for iPhone 12/13 Pro Max - Large mobile screens", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "tablet-portrait", "projectName": "tablet-portrait", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-ddd1ceac87e7e54f5c49", "file": "mobile-audit.test.ts", "line": 56, "column": 5}, {"title": "Mobile audit for iPad portrait - Tablet responsiveness", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "tablet-portrait", "projectName": "tablet-portrait", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-7e7ffbe51413780ddbd8", "file": "mobile-audit.test.ts", "line": 56, "column": 5}, {"title": "Critical route audit: /dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "tablet-portrait", "projectName": "tablet-portrait", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-ba28a1044e1ba3c80680", "file": "mobile-audit.test.ts", "line": 84, "column": 5}, {"title": "Critical route audit: /project/:projectId", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "tablet-portrait", "projectName": "tablet-portrait", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-d14726a94db864aa8ae9", "file": "mobile-audit.test.ts", "line": 84, "column": 5}, {"title": "Critical route audit: /shared/:sharedId", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "tablet-portrait", "projectName": "tablet-portrait", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-6e4751809a52200e7a6a", "file": "mobile-audit.test.ts", "line": 84, "column": 5}, {"title": "Mobile performance audit", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "tablet-portrait", "projectName": "tablet-portrait", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-bf05ef7db4d218b1d888", "file": "mobile-audit.test.ts", "line": 106, "column": 3}, {"title": "JobbLogg design system compliance", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "tablet-portrait", "projectName": "tablet-portrait", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-02e4b1f50f01a72b2026", "file": "mobile-audit.test.ts", "line": 131, "column": 3}, {"title": "Touch target WCAG AA compliance", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "tablet-portrait", "projectName": "tablet-portrait", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-9d789d56b85931692ef2", "file": "mobile-audit.test.ts", "line": 155, "column": 3}, {"title": "Mobile layout overflow prevention", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "tablet-portrait", "projectName": "tablet-portrait", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-ecfb27659f6807aa5987", "file": "mobile-audit.test.ts", "line": 180, "column": 3}]}, {"title": "Debug Utilities", "file": "mobile-audit.test.ts", "line": 206, "column": 6, "specs": [{"title": "Debug specific route and viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [{"type": "skip"}], "expectedStatus": "skipped", "projectId": "ultra-narrow-mobile", "projectName": "ultra-narrow-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-27da82aca46e8c09e570", "file": "mobile-audit.test.ts", "line": 208, "column": 8}, {"title": "Debug touch targets on narrow screens", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [{"type": "skip"}], "expectedStatus": "skipped", "projectId": "ultra-narrow-mobile", "projectName": "ultra-narrow-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-168fd404766ce227a4ad", "file": "mobile-audit.test.ts", "line": 214, "column": 8}, {"title": "Debug specific route and viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [{"type": "skip"}], "expectedStatus": "skipped", "projectId": "standard-mobile", "projectName": "standard-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-c31bbfe1bdc9101e050f", "file": "mobile-audit.test.ts", "line": 208, "column": 8}, {"title": "Debug touch targets on narrow screens", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [{"type": "skip"}], "expectedStatus": "skipped", "projectId": "standard-mobile", "projectName": "standard-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-6c479da0739cf0c0ce27", "file": "mobile-audit.test.ts", "line": 214, "column": 8}, {"title": "Debug specific route and viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [{"type": "skip"}], "expectedStatus": "skipped", "projectId": "large-mobile", "projectName": "large-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-8eb9ab97cd6a322d3291", "file": "mobile-audit.test.ts", "line": 208, "column": 8}, {"title": "Debug touch targets on narrow screens", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [{"type": "skip"}], "expectedStatus": "skipped", "projectId": "large-mobile", "projectName": "large-mobile", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-c7bfe9ef60b0b20b337b", "file": "mobile-audit.test.ts", "line": 214, "column": 8}, {"title": "Debug specific route and viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [{"type": "skip"}], "expectedStatus": "skipped", "projectId": "tablet-portrait", "projectName": "tablet-portrait", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-4626201546307a20e54d", "file": "mobile-audit.test.ts", "line": 208, "column": 8}, {"title": "Debug touch targets on narrow screens", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [{"type": "skip"}], "expectedStatus": "skipped", "projectId": "tablet-portrait", "projectName": "tablet-portrait", "results": [], "status": "skipped"}], "id": "93e1df048fe9de73fa28-51c0ecac1769e87e144c", "file": "mobile-audit.test.ts", "line": 214, "column": 8}]}]}], "errors": [], "stats": {"startTime": "2025-07-09T10:55:28.650Z", "duration": 125264.666, "expected": 0, "skipped": 45, "unexpected": 11, "flaky": 0}}