{"summary": {"totalRoutes": 11, "totalViewports": 4, "totalTests": 44, "passedTests": 0, "failedTests": 43, "warningTests": 1, "overallScore": 0, "criticalIssues": 43, "highIssues": 0, "mediumIssues": 14, "lowIssues": 0, "touchTargetIssueCount": 1, "layoutIssueCount": 55, "accessibilityIssueCount": 1, "averageLighthouseScore": 2, "averageLCP": 0, "averageCLS": 0, "jobbLoggTokenCompliance": 0, "responsiveBreakpointCompliance": 0, "norwegianLocalizationSupport": 2, "topRecommendations": ["Check browser console and network connectivity", "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "Add responsive classes using JobbLogg breakpoints: xxs:, xs:, sm:, md:, lg:, xl:, 2xl:", "Increase touch target size to minimum 44x44px. For ultra-narrow screens, consider using touch-target-compact class (36x36px minimum).", "Add visible focus styles using focus:outline-* or focus:ring-* classes"], "quickFixes": ["Add touch-target utility classes to buttons and interactive elements", "Update components to use JobbLogg design system tokens", "Add responsive breakpoint classes (xxs:, xs:, sm:, md:, lg:)"], "results": [{"routeName": "PageWrapper", "url": "/shared/test123456", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "warning", "touchTargetIssues": [{"type": "touch_target_too_small", "severity": "medium", "element": "BUTTON", "selector": "button.px-4.py-2", "dimensions": {"width": 148.4375, "height": 40}, "position": {"x": 85.78125, "y": -712}, "recommendation": "Increase touch target size to minimum 44x44px. For ultra-narrow screens, consider using touch-target-compact class (36x36px minimum).", "wcagReference": "WCAG 2.1 AA - Target Size (2.5.5)"}], "layoutIssues": [{"type": "fixed_width_element", "severity": "medium", "element": "HTML", "selector": "html", "description": "Element has fixed width of 320px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "BODY", "selector": "body", "description": "Element has fixed width of 320px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "DIV", "selector": "div#root", "description": "Element has fixed width of 320px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "DIV", "selector": "div.min-h-screen.bg-white", "description": "Element has fixed width of 320px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "DIV", "selector": "div.min-h-screen.bg-white", "description": "Element has fixed width of 320px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "DIV", "selector": "div.text-center.p-6", "description": "Element has fixed width of 320px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "DIV", "selector": "div.text-red-500.mb-4", "description": "Element has fixed width of 272px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "H2", "selector": "h2.text-xl.font-semibold", "description": "Element has fixed width of 272px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "P", "selector": "p.text-jobblogg-text-muted.mb-4", "description": "Element has fixed width of 272px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "DIV", "selector": "div#clerk-components", "description": "Element has fixed width of 320px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "responsive_missing", "severity": "medium", "element": "BUTTON", "selector": "button.px-4.py-2", "description": "Element lacks responsive breakpoint classes for mobile adaptation", "recommendation": "Add responsive classes using JobbLogg breakpoints: xxs:, xs:, sm:, md:, lg:, xl:, 2xl:", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "responsive_missing", "severity": "medium", "element": "DIV", "selector": "div.min-h-screen.bg-white", "description": "Element lacks responsive breakpoint classes for mobile adaptation", "recommendation": "Add responsive classes using JobbLogg breakpoints: xxs:, xs:, sm:, md:, lg:, xl:, 2xl:", "affectedViewports": ["ultra-narrow-mobile"]}], "accessibilityIssues": [{"type": "keyboard_navigation", "severity": "medium", "element": "BUTTON", "selector": "button", "description": "Interactive element lacks visible focus indicator", "wcagLevel": "AA", "recommendation": "Add visible focus styles using focus:outline-* or focus:ring-* classes"}], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 5, "lighthouseScore": 100}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": true, "testedAt": "2025-07-09T10:56:26.156Z"}, {"routeName": "PageWrapper", "url": "/sign-in", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: page.evaluate: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.114Z"}, {"routeName": "PageWrapper", "url": "/sign-up", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.114Z"}, {"routeName": "PageWrapper", "url": "/", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.115Z"}, {"routeName": "PageWrapper", "url": "/archived-projects", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.115Z"}, {"routeName": "PageWrapper", "url": "/conversations", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.116Z"}, {"routeName": "PageWrapper", "url": "/create", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.117Z"}, {"routeName": "PageWrapper", "url": "/create-wizard", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.117Z"}, {"routeName": "PageWrapper", "url": "/test-google-maps", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.118Z"}, {"routeName": "PageWrapper", "url": "/project/test-project-1/details", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.118Z"}, {"routeName": "PageWrapper", "url": "/project/test-project-1", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.119Z"}, {"routeName": "PageWrapper", "url": "/shared/test123456", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.119Z"}, {"routeName": "PageWrapper", "url": "/sign-in", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.120Z"}, {"routeName": "PageWrapper", "url": "/sign-up", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.121Z"}, {"routeName": "PageWrapper", "url": "/", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.122Z"}, {"routeName": "PageWrapper", "url": "/archived-projects", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.123Z"}, {"routeName": "PageWrapper", "url": "/conversations", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.123Z"}, {"routeName": "PageWrapper", "url": "/create", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.124Z"}, {"routeName": "PageWrapper", "url": "/create-wizard", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.124Z"}, {"routeName": "PageWrapper", "url": "/test-google-maps", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.124Z"}, {"routeName": "PageWrapper", "url": "/project/test-project-1/details", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.126Z"}, {"routeName": "PageWrapper", "url": "/project/test-project-1", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.127Z"}, {"routeName": "PageWrapper", "url": "/shared/test123456", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.129Z"}, {"routeName": "PageWrapper", "url": "/sign-in", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.132Z"}, {"routeName": "PageWrapper", "url": "/sign-up", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.135Z"}, {"routeName": "PageWrapper", "url": "/", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.136Z"}, {"routeName": "PageWrapper", "url": "/archived-projects", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.136Z"}, {"routeName": "PageWrapper", "url": "/conversations", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.136Z"}, {"routeName": "PageWrapper", "url": "/create", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.137Z"}, {"routeName": "PageWrapper", "url": "/create-wizard", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.137Z"}, {"routeName": "PageWrapper", "url": "/test-google-maps", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.137Z"}, {"routeName": "PageWrapper", "url": "/project/test-project-1/details", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.137Z"}, {"routeName": "PageWrapper", "url": "/project/test-project-1", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.138Z"}, {"routeName": "PageWrapper", "url": "/shared/test123456", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.138Z"}, {"routeName": "PageWrapper", "url": "/sign-in", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.138Z"}, {"routeName": "PageWrapper", "url": "/sign-up", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.139Z"}, {"routeName": "PageWrapper", "url": "/", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.139Z"}, {"routeName": "PageWrapper", "url": "/archived-projects", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.141Z"}, {"routeName": "PageWrapper", "url": "/conversations", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.141Z"}, {"routeName": "PageWrapper", "url": "/create", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.141Z"}, {"routeName": "PageWrapper", "url": "/create-wizard", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.142Z"}, {"routeName": "PageWrapper", "url": "/test-google-maps", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.143Z"}, {"routeName": "PageWrapper", "url": "/project/test-project-1/details", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.144Z"}, {"routeName": "PageWrapper", "url": "/project/test-project-1", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.144Z"}], "generatedAt": "2025-07-09T10:56:32.149Z", "environment": "development", "version": "1.0.0"}, "results": [{"routeName": "PageWrapper", "url": "/shared/test123456", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "warning", "touchTargetIssues": [{"type": "touch_target_too_small", "severity": "medium", "element": "BUTTON", "selector": "button.px-4.py-2", "dimensions": {"width": 148.4375, "height": 40}, "position": {"x": 85.78125, "y": -712}, "recommendation": "Increase touch target size to minimum 44x44px. For ultra-narrow screens, consider using touch-target-compact class (36x36px minimum).", "wcagReference": "WCAG 2.1 AA - Target Size (2.5.5)"}], "layoutIssues": [{"type": "fixed_width_element", "severity": "medium", "element": "HTML", "selector": "html", "description": "Element has fixed width of 320px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "BODY", "selector": "body", "description": "Element has fixed width of 320px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "DIV", "selector": "div#root", "description": "Element has fixed width of 320px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "DIV", "selector": "div.min-h-screen.bg-white", "description": "Element has fixed width of 320px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "DIV", "selector": "div.min-h-screen.bg-white", "description": "Element has fixed width of 320px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "DIV", "selector": "div.text-center.p-6", "description": "Element has fixed width of 320px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "DIV", "selector": "div.text-red-500.mb-4", "description": "Element has fixed width of 272px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "H2", "selector": "h2.text-xl.font-semibold", "description": "Element has fixed width of 272px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "P", "selector": "p.text-jobblogg-text-muted.mb-4", "description": "Element has fixed width of 272px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "fixed_width_element", "severity": "medium", "element": "DIV", "selector": "div#clerk-components", "description": "Element has fixed width of 320px which may not adapt to different screen sizes", "recommendation": "Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "responsive_missing", "severity": "medium", "element": "BUTTON", "selector": "button.px-4.py-2", "description": "Element lacks responsive breakpoint classes for mobile adaptation", "recommendation": "Add responsive classes using JobbLogg breakpoints: xxs:, xs:, sm:, md:, lg:, xl:, 2xl:", "affectedViewports": ["ultra-narrow-mobile"]}, {"type": "responsive_missing", "severity": "medium", "element": "DIV", "selector": "div.min-h-screen.bg-white", "description": "Element lacks responsive breakpoint classes for mobile adaptation", "recommendation": "Add responsive classes using JobbLogg breakpoints: xxs:, xs:, sm:, md:, lg:, xl:, 2xl:", "affectedViewports": ["ultra-narrow-mobile"]}], "accessibilityIssues": [{"type": "keyboard_navigation", "severity": "medium", "element": "BUTTON", "selector": "button", "description": "Interactive element lacks visible focus indicator", "wcagLevel": "AA", "recommendation": "Add visible focus styles using focus:outline-* or focus:ring-* classes"}], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 5, "lighthouseScore": 100}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": true, "testedAt": "2025-07-09T10:56:26.156Z"}, {"routeName": "PageWrapper", "url": "/sign-in", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: page.evaluate: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.114Z"}, {"routeName": "PageWrapper", "url": "/sign-up", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.114Z"}, {"routeName": "PageWrapper", "url": "/", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.115Z"}, {"routeName": "PageWrapper", "url": "/archived-projects", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.115Z"}, {"routeName": "PageWrapper", "url": "/conversations", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.116Z"}, {"routeName": "PageWrapper", "url": "/create", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.117Z"}, {"routeName": "PageWrapper", "url": "/create-wizard", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.117Z"}, {"routeName": "PageWrapper", "url": "/test-google-maps", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.118Z"}, {"routeName": "PageWrapper", "url": "/project/test-project-1/details", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.118Z"}, {"routeName": "PageWrapper", "url": "/project/test-project-1", "viewport": {"name": "ultra-narrow-mobile", "width": 320, "height": 568, "deviceScaleFactor": 2, "isMobile": true, "hasTouch": true, "description": "iPhone SE, older Android phones - Critical narrow width testing"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.119Z"}, {"routeName": "PageWrapper", "url": "/shared/test123456", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.119Z"}, {"routeName": "PageWrapper", "url": "/sign-in", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.120Z"}, {"routeName": "PageWrapper", "url": "/sign-up", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.121Z"}, {"routeName": "PageWrapper", "url": "/", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.122Z"}, {"routeName": "PageWrapper", "url": "/archived-projects", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.123Z"}, {"routeName": "PageWrapper", "url": "/conversations", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.123Z"}, {"routeName": "PageWrapper", "url": "/create", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.124Z"}, {"routeName": "PageWrapper", "url": "/create-wizard", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.124Z"}, {"routeName": "PageWrapper", "url": "/test-google-maps", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.124Z"}, {"routeName": "PageWrapper", "url": "/project/test-project-1/details", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.126Z"}, {"routeName": "PageWrapper", "url": "/project/test-project-1", "viewport": {"name": "standard-mobile", "width": 375, "height": 812, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 mini - Most common mobile viewport"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.127Z"}, {"routeName": "PageWrapper", "url": "/shared/test123456", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.129Z"}, {"routeName": "PageWrapper", "url": "/sign-in", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.132Z"}, {"routeName": "PageWrapper", "url": "/sign-up", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.135Z"}, {"routeName": "PageWrapper", "url": "/", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.136Z"}, {"routeName": "PageWrapper", "url": "/archived-projects", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.136Z"}, {"routeName": "PageWrapper", "url": "/conversations", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.136Z"}, {"routeName": "PageWrapper", "url": "/create", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.137Z"}, {"routeName": "PageWrapper", "url": "/create-wizard", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.137Z"}, {"routeName": "PageWrapper", "url": "/test-google-maps", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.137Z"}, {"routeName": "PageWrapper", "url": "/project/test-project-1/details", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.137Z"}, {"routeName": "PageWrapper", "url": "/project/test-project-1", "viewport": {"name": "large-mobile", "width": 414, "height": 896, "deviceScaleFactor": 3, "isMobile": true, "hasTouch": true, "description": "iPhone 12/13 Pro Max - Large mobile screens"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.138Z"}, {"routeName": "PageWrapper", "url": "/shared/test123456", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.138Z"}, {"routeName": "PageWrapper", "url": "/sign-in", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.138Z"}, {"routeName": "PageWrapper", "url": "/sign-up", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.139Z"}, {"routeName": "PageWrapper", "url": "/", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.139Z"}, {"routeName": "PageWrapper", "url": "/archived-projects", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.141Z"}, {"routeName": "PageWrapper", "url": "/conversations", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.141Z"}, {"routeName": "PageWrapper", "url": "/create", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.141Z"}, {"routeName": "PageWrapper", "url": "/create-wizard", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.142Z"}, {"routeName": "PageWrapper", "url": "/test-google-maps", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.143Z"}, {"routeName": "PageWrapper", "url": "/project/test-project-1/details", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.144Z"}, {"routeName": "PageWrapper", "url": "/project/test-project-1", "viewport": {"name": "tablet-portrait", "width": 768, "height": 1024, "deviceScaleFactor": 2, "isMobile": false, "hasTouch": true, "description": "iPad portrait - Tablet responsiveness"}, "status": "failed", "touchTargetIssues": [], "layoutIssues": [{"type": "test_error", "severity": "critical", "element": "PAGE", "selector": "body", "description": "Test failed with error: browser.newContext: Target page, context or browser has been closed", "recommendation": "Check browser console and network connectivity"}], "accessibilityIssues": [], "performanceMetrics": {"lcp": 0, "fid": 0, "cls": 0, "fcp": 0, "ttfb": 0, "lighthouseScore": 0}, "usesJobbLoggTokens": false, "hasResponsiveBreakpoints": false, "norwegianTextSupport": false, "testedAt": "2025-07-09T10:56:32.144Z"}], "metadata": {"generatedAt": "2025-07-09T10:56:32.149Z", "version": "1.0.0", "framework": "JobbLogg Mobile Audit", "totalResults": 44}}