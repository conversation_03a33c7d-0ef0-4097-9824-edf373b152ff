<testsuites id="" name="" tests="56" failures="11" skipped="45" errors="0" time="125.26466599999999">
<testsuite name="mobile-audit.test.ts" timestamp="2025-07-09T10:55:28.831Z" hostname="ultra-narrow-mobile" tests="14" failures="11" skipped="3" time="597.873" errors="0">
<testcase name="JobbLogg Mobile Responsiveness Audit › Complete mobile audit across all routes and viewports" classname="mobile-audit.test.ts" time="60.314">
<failure message="mobile-audit.test.ts:12:3 Complete mobile audit across all routes and viewports" type="FAILURE">
<![CDATA[  [ultra-narrow-mobile] › mobile-audit.test.ts:12:3 › JobbLogg Mobile Responsiveness Audit › Complete mobile audit across all routes and viewports 

    Test timeout of 30000ms exceeded.

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Test timeout of 30000ms exceeded.

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/error-context.md

    attachment #6: trace (application/zip) ─────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/trace.zip
    Usage:

        npx playwright show-trace reports/test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🚀 Starting JobbLogg Mobile Responsiveness Audit...
📍 Found 11 routes to test
📱 Testing viewport: iPhone SE, older Android phones - Critical narrow width testing (320×568)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
    ⚠️ warning - 14 issues
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPhone 12/13 mini - Most common mobile viewport (375×812)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPhone 12/13 Pro Max - Large mobile screens (414×896)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPad portrait - Tablet responsiveness (768×1024)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)

📊 Audit completed: 44 tests run
📝 Generating reports...

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile/error-context.md]]
🚀 Starting JobbLogg Mobile Responsiveness Audit...
📍 Found 11 routes to test
📱 Testing viewport: iPhone SE, older Android phones - Critical narrow width testing (320×568)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
    ⚠️ warning - 14 issues
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPhone 12/13 mini - Most common mobile viewport (375×812)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPhone 12/13 Pro Max - Large mobile screens (414×896)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPad portrait - Tablet responsiveness (768×1024)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)

📊 Audit completed: 44 tests run
📝 Generating reports...
✅ Reports generated:
  📄 JSON: reports/mobile-audit-report.json
  📝 Markdown: reports/mobile-audit-report.md
  🌐 HTML: reports/mobile-audit-report.html
🔧 Generating automated fix suggestions...
✅ Generated 0 fix suggestions in 0 patch files

📊 Audit Summary:
  ✅ Passed: 0
  ❌ Failed: 43
  ⚠️  Warnings: 1
  📄 Reports: 3
  🔧 Fix suggestions: 0 files

❌ 43 routes have critical mobile responsiveness issues
📝 Check the generated reports for detailed recommendations

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/error-context.md]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-654e5-ss-all-routes-and-viewports-ultra-narrow-mobile-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[    ❌ Error testing PageWrapper: browserContext.close: Test ended.
Browser logs:

<launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window
<launched> pid=69674
[pid=69674] <gracefully close start>
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:171:7[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
Error collecting performance metrics: page.evaluate: Test ended.
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:16:21[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:136:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.checkMobilePerformanceIssues [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:174:33[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:156:41[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: page.evaluate: Target page, context or browser has been closed
    at MobileAuditRunner.checkJobbLoggTokenUsage [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:179:23[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:137:45[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:17:21
]]>
</system-err>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile audit for iPhone SE, older Android phones - Critical narrow width testing" classname="mobile-audit.test.ts" time="60.315">
<failure message="mobile-audit.test.ts:56:5 Mobile audit for iPhone SE, older Android phones - Critical narrow width testing" type="FAILURE">
<![CDATA[  [ultra-narrow-mobile] › mobile-audit.test.ts:56:5 › JobbLogg Mobile Responsiveness Audit › Mobile audit for iPhone SE, older Android phones - Critical narrow width testing 

    Test timeout of 30000ms exceeded.

    Error: browserContext.close: Test ended.
    Browser logs:

    <launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window
    <launched> pid=69675
    [pid=69675] <gracefully close start>

       at ../audit-runner.ts:171

      169 |
      170 |     } finally {
    > 171 |       await context.close();
          |       ^
      172 |     }
      173 |   }
      174 |
        at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)
        at MobileAuditRunner.runViewportAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:339:22)
        at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:58:23

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Test timeout of 30000ms exceeded.

    Error: browserContext.close: Test ended.
    Browser logs:

    <launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window
    <launched> pid=69779
    [pid=69779] <gracefully close start>

       at ../audit-runner.ts:171

      169 |
      170 |     } finally {
    > 171 |       await context.close();
          |       ^
      172 |     }
      173 |   }
      174 |
        at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)
        at MobileAuditRunner.runViewportAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:339:22)
        at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:58:23

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/error-context.md

    attachment #6: trace (application/zip) ─────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/trace.zip
    Usage:

        npx playwright show-trace reports/test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile/error-context.md]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/error-context.md]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-de2fe-itical-narrow-width-testing-ultra-narrow-mobile-retry1/trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile audit for iPhone 12/13 mini - Most common mobile viewport" classname="mobile-audit.test.ts" time="60.281">
<failure message="mobile-audit.test.ts:56:5 Mobile audit for iPhone 12/13 mini - Most common mobile viewport" type="FAILURE">
<![CDATA[  [ultra-narrow-mobile] › mobile-audit.test.ts:56:5 › JobbLogg Mobile Responsiveness Audit › Mobile audit for iPhone 12/13 mini - Most common mobile viewport 

    Test timeout of 30000ms exceeded.

    Error: browserContext.close: Test ended.
    Browser logs:

    <launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window
    <launched> pid=69676
    [pid=69676] <gracefully close start>

       at ../audit-runner.ts:171

      169 |
      170 |     } finally {
    > 171 |       await context.close();
          |       ^
      172 |     }
      173 |   }
      174 |
        at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)
        at MobileAuditRunner.runViewportAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:339:22)
        at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:58:23

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Test timeout of 30000ms exceeded.

    Error: browserContext.close: Target page, context or browser has been closed

       at ../audit-runner.ts:171

      169 |
      170 |     } finally {
    > 171 |       await context.close();
          |       ^
      172 |     }
      173 |   }
      174 |
        at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)
        at MobileAuditRunner.runViewportAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:339:22)
        at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:58:23

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/error-context.md

    attachment #6: trace (application/zip) ─────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/trace.zip
    Usage:

        npx playwright show-trace reports/test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile/error-context.md]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/error-context.md]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-265e1-Most-common-mobile-viewport-ultra-narrow-mobile-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[Error collecting performance metrics: page.evaluate: Test ended.
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:16:21[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runViewportAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:339:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:58:23
Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:136:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runViewportAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:339:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:58:23
Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.checkMobilePerformanceIssues [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:174:33[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:156:41[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runViewportAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:339:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:58:23
]]>
</system-err>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile audit for iPhone 12/13 Pro Max - Large mobile screens" classname="mobile-audit.test.ts" time="60.318">
<failure message="mobile-audit.test.ts:56:5 Mobile audit for iPhone 12/13 Pro Max - Large mobile screens" type="FAILURE">
<![CDATA[  [ultra-narrow-mobile] › mobile-audit.test.ts:56:5 › JobbLogg Mobile Responsiveness Audit › Mobile audit for iPhone 12/13 Pro Max - Large mobile screens 

    Test timeout of 30000ms exceeded.

    Error: browserContext.close: Test ended.
    Browser logs:

    <launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window
    <launched> pid=69677
    [pid=69677] <gracefully close start>

       at ../audit-runner.ts:171

      169 |
      170 |     } finally {
    > 171 |       await context.close();
          |       ^
      172 |     }
      173 |   }
      174 |
        at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)
        at MobileAuditRunner.runViewportAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:339:22)
        at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:58:23

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Test timeout of 30000ms exceeded.

    Error: browserContext.close: Target page, context or browser has been closed

       at ../audit-runner.ts:171

      169 |
      170 |     } finally {
    > 171 |       await context.close();
          |       ^
      172 |     }
      173 |   }
      174 |
        at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)
        at MobileAuditRunner.runViewportAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:339:22)
        at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:58:23

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/error-context.md

    attachment #6: trace (application/zip) ─────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/trace.zip
    Usage:

        npx playwright show-trace reports/test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile/error-context.md]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/error-context.md]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-8bc54--Max---Large-mobile-screens-ultra-narrow-mobile-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[Error collecting performance metrics: page.evaluate: Test ended.
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:16:21[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runViewportAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:339:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:58:23
Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:136:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runViewportAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:339:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:58:23
Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.checkMobilePerformanceIssues [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:174:33[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:156:41[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runViewportAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:339:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:58:23
]]>
</system-err>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile audit for iPad portrait - Tablet responsiveness" classname="mobile-audit.test.ts" time="60.615">
<failure message="mobile-audit.test.ts:56:5 Mobile audit for iPad portrait - Tablet responsiveness" type="FAILURE">
<![CDATA[  [ultra-narrow-mobile] › mobile-audit.test.ts:56:5 › JobbLogg Mobile Responsiveness Audit › Mobile audit for iPad portrait - Tablet responsiveness 

    Test timeout of 30000ms exceeded.

    Error: browserContext.close: Test ended.
    Browser logs:

    <launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window
    <launched> pid=69673
    [pid=69673] <gracefully close start>

       at ../audit-runner.ts:171

      169 |
      170 |     } finally {
    > 171 |       await context.close();
          |       ^
      172 |     }
      173 |   }
      174 |
        at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)
        at MobileAuditRunner.runViewportAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:339:22)
        at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:58:23

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Test timeout of 30000ms exceeded.

    Error: browserContext.close: Test ended.
    Browser logs:

    <launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window
    <launched> pid=69776
    [pid=69776] <gracefully close start>

       at ../audit-runner.ts:171

      169 |
      170 |     } finally {
    > 171 |       await context.close();
          |       ^
      172 |     }
      173 |   }
      174 |
        at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)
        at MobileAuditRunner.runViewportAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:339:22)
        at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:58:23

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/error-context.md

    attachment #6: trace (application/zip) ─────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/trace.zip
    Usage:

        npx playwright show-trace reports/test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile/error-context.md]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/error-context.md]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-bb1b9-ait---Tablet-responsiveness-ultra-narrow-mobile-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[Error collecting performance metrics: page.evaluate: Test ended.
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:16:21[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runViewportAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:339:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:58:23
Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:136:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runViewportAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:339:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:58:23
]]>
</system-err>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Critical route audit: /dashboard" classname="mobile-audit.test.ts" time="1.005">
<failure message="mobile-audit.test.ts:84:5 Critical route audit: /dashboard" type="FAILURE">
<![CDATA[  [ultra-narrow-mobile] › mobile-audit.test.ts:84:5 › JobbLogg Mobile Responsiveness Audit › Critical route audit: /dashboard 

    Error: Route not found: /dashboard

       at ../audit-runner.ts:308

      306 |
      307 |     if (!targetRouteInfo) {
    > 308 |       throw new Error(`Route not found: ${routePath}`);
          |             ^
      309 |     }
      310 |
      311 |     const viewports = viewportName
        at MobileAuditRunner.runRouteAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:308:13)
        at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:86:23

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-93b46-tical-route-audit-dashboard-ultra-narrow-mobile/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-93b46-tical-route-audit-dashboard-ultra-narrow-mobile/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: Route not found: /dashboard

       at ../audit-runner.ts:308

      306 |
      307 |     if (!targetRouteInfo) {
    > 308 |       throw new Error(`Route not found: ${routePath}`);
          |             ^
      309 |     }
      310 |
      311 |     const viewports = viewportName
        at MobileAuditRunner.runRouteAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:308:13)
        at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:86:23

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-93b46-tical-route-audit-dashboard-ultra-narrow-mobile-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-93b46-tical-route-audit-dashboard-ultra-narrow-mobile-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-93b46-tical-route-audit-dashboard-ultra-narrow-mobile-retry1/trace.zip
    Usage:

        npx playwright show-trace reports/test-artifacts/mobile-audit-JobbLogg-Mobi-93b46-tical-route-audit-dashboard-ultra-narrow-mobile-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-93b46-tical-route-audit-dashboard-ultra-narrow-mobile/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-93b46-tical-route-audit-dashboard-ultra-narrow-mobile/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-93b46-tical-route-audit-dashboard-ultra-narrow-mobile-retry1/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-93b46-tical-route-audit-dashboard-ultra-narrow-mobile-retry1/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-93b46-tical-route-audit-dashboard-ultra-narrow-mobile-retry1/trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Critical route audit: /project/:projectId" classname="mobile-audit.test.ts" time="59.426">
<failure message="mobile-audit.test.ts:84:5 Critical route audit: /project/:projectId" type="FAILURE">
<![CDATA[  [ultra-narrow-mobile] › mobile-audit.test.ts:84:5 › JobbLogg Mobile Responsiveness Audit › Critical route audit: /project/:projectId 

    Test timeout of 30000ms exceeded.

    Error: page.evaluate: Target page, context or browser has been closed

       at ../audit-runner.ts:179

      177 |    */
      178 |   private async checkJobbLoggTokenUsage(page: Page): Promise<boolean> {
    > 179 |     return await page.evaluate(() => {
          |                       ^
      180 |       const elements = document.querySelectorAll('*');
      181 |       let jobbLoggTokenCount = 0;
      182 |       let totalColorClasses = 0;
        at MobileAuditRunner.checkJobbLoggTokenUsage (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:179:23)
        at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:137:45)
        at MobileAuditRunner.runRouteAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:318:22)
        at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:86:23

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Test was interrupted.

    Error: page.evaluate: Target page, context or browser has been closed

       at ../audit-runner.ts:179

      177 |    */
      178 |   private async checkJobbLoggTokenUsage(page: Page): Promise<boolean> {
    > 179 |     return await page.evaluate(() => {
          |                       ^
      180 |       const elements = document.querySelectorAll('*');
      181 |       let jobbLoggTokenCount = 0;
      182 |       let totalColorClasses = 0;
        at MobileAuditRunner.checkJobbLoggTokenUsage (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:179:23)
        at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:137:45)
        at MobileAuditRunner.runRouteAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:318:22)
        at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:86:23

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile-retry1/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile-retry1/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: trace (application/zip) ─────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile-retry1/trace.zip
    Usage:

        npx playwright show-trace reports/test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile/error-context.md]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile-retry1/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile-retry1/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile-retry1/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile-retry1/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-a92df-ute-audit-project-projectId-ultra-narrow-mobile-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[Error collecting performance metrics: page.evaluate: Test ended.
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:16:21[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runRouteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:318:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:86:23
Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:136:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runRouteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:318:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:86:23
Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.checkMobilePerformanceIssues [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:174:33[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:156:41[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runRouteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:318:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:86:23
Error collecting performance metrics: page.evaluate: Test ended.
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:16:21[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runRouteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:318:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:86:23
Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:136:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runRouteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:318:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:86:23
Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.checkMobilePerformanceIssues [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:174:33[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:156:41[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runRouteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:318:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:86:23
]]>
</system-err>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Critical route audit: /shared/:sharedId" classname="mobile-audit.test.ts" time="59.602">
<failure message="mobile-audit.test.ts:84:5 Critical route audit: /shared/:sharedId" type="FAILURE">
<![CDATA[  [ultra-narrow-mobile] › mobile-audit.test.ts:84:5 › JobbLogg Mobile Responsiveness Audit › Critical route audit: /shared/:sharedId 

    Test timeout of 30000ms exceeded.

    Error: browserContext.close: Test ended.
    Browser logs:

    <launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window
    <launched> pid=69875
    [pid=69875] <gracefully close start>

       at ../audit-runner.ts:171

      169 |
      170 |     } finally {
    > 171 |       await context.close();
          |       ^
      172 |     }
      173 |   }
      174 |
        at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:171:7)
        at MobileAuditRunner.runRouteAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:318:22)
        at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:86:23

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Test was interrupted.

    Error: page.evaluate: Target page, context or browser has been closed

       at ../audit-runner.ts:179

      177 |    */
      178 |   private async checkJobbLoggTokenUsage(page: Page): Promise<boolean> {
    > 179 |     return await page.evaluate(() => {
          |                       ^
      180 |       const elements = document.querySelectorAll('*');
      181 |       let jobbLoggTokenCount = 0;
      182 |       let totalColorClasses = 0;
        at MobileAuditRunner.checkJobbLoggTokenUsage (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:179:23)
        at MobileAuditRunner.auditRoute (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:137:45)
        at MobileAuditRunner.runRouteAudit (/Users/<USER>/JobbLogg/tests/mobile-audit/src/audit-runner.ts:318:22)
        at /Users/<USER>/JobbLogg/tests/mobile-audit/src/tests/mobile-audit.test.ts:86:23

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile-retry1/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile-retry1/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: trace (application/zip) ─────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile-retry1/trace.zip
    Usage:

        npx playwright show-trace reports/test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile/error-context.md]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile-retry1/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile-retry1/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile-retry1/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile-retry1/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-b109e-route-audit-shared-sharedId-ultra-narrow-mobile-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[Error collecting performance metrics: page.evaluate: Test ended.
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:16:21[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runRouteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:318:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:86:23
Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:136:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runRouteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:318:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:86:23
Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.checkMobilePerformanceIssues [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:174:33[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:156:41[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runRouteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:318:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:86:23
Error collecting performance metrics: page.evaluate: Test ended.
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:16:21[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runRouteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:318:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:86:23
Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:136:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runRouteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:318:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:86:23
Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.checkMobilePerformanceIssues [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:174:33[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:156:41[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runRouteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:318:22[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:86:23
]]>
</system-err>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile performance audit" classname="mobile-audit.test.ts" time="59.591">
<failure message="mobile-audit.test.ts:106:3 Mobile performance audit" type="FAILURE">
<![CDATA[  [ultra-narrow-mobile] › mobile-audit.test.ts:106:3 › JobbLogg Mobile Responsiveness Audit › Mobile performance audit 

    Test timeout of 30000ms exceeded.

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Test was interrupted.

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile-retry1/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile-retry1/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: trace (application/zip) ─────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile-retry1/trace.zip
    Usage:

        npx playwright show-trace reports/test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🚀 Starting JobbLogg Mobile Responsiveness Audit...
📍 Found 11 routes to test
📱 Testing viewport: iPhone SE, older Android phones - Critical narrow width testing (320×568)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
    ⚠️ warning - 14 issues
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPhone 12/13 mini - Most common mobile viewport (375×812)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPhone 12/13 Pro Max - Large mobile screens (414×896)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPad portrait - Tablet responsiveness (768×1024)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile/error-context.md]]
🚀 Starting JobbLogg Mobile Responsiveness Audit...
📍 Found 11 routes to test
📱 Testing viewport: iPhone SE, older Android phones - Critical narrow width testing (320×568)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
    ⚠️ warning - 14 issues
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPhone 12/13 mini - Most common mobile viewport (375×812)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPhone 12/13 Pro Max - Large mobile screens (414×896)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPad portrait - Tablet responsiveness (768×1024)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)

📊 Audit completed: 44 tests run

⚡ Performance Metrics:
  LCP (Largest Contentful Paint): 0ms
  CLS (Cumulative Layout Shift): 0.000
  Average Lighthouse Score: 2/100
  LCP Status: ✅ Good
  CLS Status: ✅ Good
  Lighthouse Status: ⚠️ Needs improvement

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile-retry1/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile-retry1/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile-retry1/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile-retry1/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-2c9af-it-Mobile-performance-audit-ultra-narrow-mobile-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[Error collecting performance metrics: page.evaluate: Test ended.
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:16:21[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:136:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.checkMobilePerformanceIssues [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:174:33[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:156:41[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: page.evaluate: Target page, context or browser has been closed
    at MobileAuditRunner.checkJobbLoggTokenUsage [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:179:23[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:137:45[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
Error collecting performance metrics: page.evaluate: Test ended.
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:16:21[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:136:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.checkMobilePerformanceIssues [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:174:33[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:156:41[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browserContext.close: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:171:7[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:108:21
]]>
</system-err>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › JobbLogg design system compliance" classname="mobile-audit.test.ts" time="59.43">
<failure message="mobile-audit.test.ts:131:3 JobbLogg design system compliance" type="FAILURE">
<![CDATA[  [ultra-narrow-mobile] › mobile-audit.test.ts:131:3 › JobbLogg Mobile Responsiveness Audit › JobbLogg design system compliance 

    Test timeout of 30000ms exceeded.

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Test was interrupted.

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile-retry1/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile-retry1/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: trace (application/zip) ─────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile-retry1/trace.zip
    Usage:

        npx playwright show-trace reports/test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🚀 Starting JobbLogg Mobile Responsiveness Audit...
📍 Found 11 routes to test
📱 Testing viewport: iPhone SE, older Android phones - Critical narrow width testing (320×568)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
    ⚠️ warning - 14 issues
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPhone 12/13 mini - Most common mobile viewport (375×812)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPhone 12/13 Pro Max - Large mobile screens (414×896)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPad portrait - Tablet responsiveness (768×1024)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)

📊 Audit completed: 44 tests run

🎨 JobbLogg Design System Compliance:
  Design Token Usage: 0%
  Responsive Breakpoints: 0%
  Norwegian Text Support: 2%
  Token Status: ⚠️ Needs improvement
  Responsive Status: ⚠️ Needs improvement
  Norwegian Status: ⚠️ Needs improvement

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile/error-context.md]]
🚀 Starting JobbLogg Mobile Responsiveness Audit...
📍 Found 11 routes to test
📱 Testing viewport: iPhone SE, older Android phones - Critical narrow width testing (320×568)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
    ⚠️ warning - 14 issues
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPhone 12/13 mini - Most common mobile viewport (375×812)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPhone 12/13 Pro Max - Large mobile screens (414×896)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPad portrait - Tablet responsiveness (768×1024)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)

📊 Audit completed: 44 tests run

🎨 JobbLogg Design System Compliance:
  Design Token Usage: 0%
  Responsive Breakpoints: 0%
  Norwegian Text Support: 2%
  Token Status: ⚠️ Needs improvement
  Responsive Status: ⚠️ Needs improvement
  Norwegian Status: ⚠️ Needs improvement

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile-retry1/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile-retry1/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile-retry1/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile-retry1/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-f092d-gg-design-system-compliance-ultra-narrow-mobile-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[Error collecting performance metrics: page.evaluate: Test ended.
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:16:21[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:136:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.checkMobilePerformanceIssues [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:174:33[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:156:41[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browserContext.close: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:171:7[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
Error collecting performance metrics: page.evaluate: Test ended.
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:16:21[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:136:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.checkMobilePerformanceIssues [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:174:33[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:156:41[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: page.evaluate: Target page, context or browser has been closed
    at MobileAuditRunner.checkJobbLoggTokenUsage [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:179:23[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:137:45[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:133:21
]]>
</system-err>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Touch target WCAG AA compliance" classname="mobile-audit.test.ts" time="56.976">
<failure message="mobile-audit.test.ts:155:3 Touch target WCAG AA compliance" type="FAILURE">
<![CDATA[  [ultra-narrow-mobile] › mobile-audit.test.ts:155:3 › JobbLogg Mobile Responsiveness Audit › Touch target WCAG AA compliance 

    Test timeout of 30000ms exceeded.

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Test was interrupted.

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile-retry1/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile-retry1/test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: trace (application/zip) ─────────────────────────────────────────────────────────
    reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile-retry1/trace.zip
    Usage:

        npx playwright show-trace reports/test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🚀 Starting JobbLogg Mobile Responsiveness Audit...
📍 Found 11 routes to test
📱 Testing viewport: iPhone SE, older Android phones - Critical narrow width testing (320×568)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
    ⚠️ warning - 14 issues
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPhone 12/13 mini - Most common mobile viewport (375×812)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPhone 12/13 Pro Max - Large mobile screens (414×896)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPad portrait - Tablet responsiveness (768×1024)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile/error-context.md]]
🚀 Starting JobbLogg Mobile Responsiveness Audit...
📍 Found 11 routes to test
📱 Testing viewport: iPhone SE, older Android phones - Critical narrow width testing (320×568)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
    ⚠️ warning - 14 issues
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPhone 12/13 mini - Most common mobile viewport (375×812)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPhone 12/13 Pro Max - Large mobile screens (414×896)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)
📱 Testing viewport: iPad portrait - Tablet responsiveness (768×1024)
  🔍 Testing route: PageWrapper (/shared/:sharedId)
  🔍 Testing route: PageWrapper (/sign-in)
  🔍 Testing route: PageWrapper (/sign-up)
  🔍 Testing route: PageWrapper (/)
  🔍 Testing route: PageWrapper (/archived-projects)
  🔍 Testing route: PageWrapper (/conversations)
  🔍 Testing route: PageWrapper (/create)
  🔍 Testing route: PageWrapper (/create-wizard)
  🔍 Testing route: PageWrapper (/test-google-maps)
  🔍 Testing route: PageWrapper (/project/:projectId/details)
  🔍 Testing route: PageWrapper (/project/:projectId)

📊 Audit completed: 44 tests run

👆 Touch Target Compliance:
  Total touch target issues: 1
  Critical touch target issues: 0

⚠️  Touch target issues found. Common fixes:
  - Add 'touch-target' class to small buttons
  - Increase padding on interactive elements
  - Ensure minimum 44x44px touch area

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile-retry1/test-failed-1.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile-retry1/test-failed-2.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile-retry1/video.webm]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile-retry1/test-failed-3.png]]

[[ATTACHMENT|test-artifacts/mobile-audit-JobbLogg-Mobi-c32ad-h-target-WCAG-AA-compliance-ultra-narrow-mobile-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[Error collecting performance metrics: page.evaluate: Test ended.
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:16:21[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:136:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.checkMobilePerformanceIssues [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:174:33[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:156:41[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browserContext.close: Test ended.
Browser logs:

<launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window
<launched> pid=69952
[pid=69952] <gracefully close start>
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:171:7[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
Error collecting performance metrics: page.evaluate: Test ended.
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:16:21[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
Error collecting performance metrics: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.collectPerformanceMetrics [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:35:34[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:136:34[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
Error checking mobile performance issues: page.evaluate: Target page, context or browser has been closed
    at PerformanceValidation.checkMobilePerformanceIssues [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:174:33[90m)[39m
    at PerformanceValidation.runLighthouseAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:156:41[90m)[39m
    at PerformanceValidation.validate [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/validation-modules/performance-validation.ts:19:29[90m)[39m
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:129:90[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browserContext.close: Test ended.
Browser logs:

<launching> /Users/<USER>/Library/Caches/ms-playwright/webkit-2182/pw_run.sh --inspector-pipe --headless --no-startup-window
<launched> pid=70056
[pid=70056] <gracefully close start>
[pid=70056] <process did exit: exitCode=0, signal=null>
[pid=70056] starting temporary directories cleanup
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:171:7[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
    ❌ Error testing PageWrapper: browser.newContext: Target page, context or browser has been closed
    at MobileAuditRunner.auditRoute [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:107:21[90m)[39m
    at MobileAuditRunner.runCompleteAudit [90m(/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/audit-runner.ts:57:26[90m)[39m
    at [90m/Users/<USER>/JobbLogg/tests/mobile-audit/[39msrc/tests/mobile-audit.test.ts:157:21
]]>
</system-err>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile layout overflow prevention" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Debug Utilities › Debug specific route and viewport" classname="mobile-audit.test.ts" time="0">
<properties>
<property name="skip" value="">
</property>
</properties>
<skipped>
</skipped>
</testcase>
<testcase name="Debug Utilities › Debug touch targets on narrow screens" classname="mobile-audit.test.ts" time="0">
<properties>
<property name="skip" value="">
</property>
</properties>
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="mobile-audit.test.ts" timestamp="2025-07-09T10:55:28.831Z" hostname="standard-mobile" tests="14" failures="0" skipped="14" time="0" errors="0">
<testcase name="JobbLogg Mobile Responsiveness Audit › Complete mobile audit across all routes and viewports" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile audit for iPhone SE, older Android phones - Critical narrow width testing" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile audit for iPhone 12/13 mini - Most common mobile viewport" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile audit for iPhone 12/13 Pro Max - Large mobile screens" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile audit for iPad portrait - Tablet responsiveness" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Critical route audit: /dashboard" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Critical route audit: /project/:projectId" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Critical route audit: /shared/:sharedId" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile performance audit" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › JobbLogg design system compliance" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Touch target WCAG AA compliance" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile layout overflow prevention" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Debug Utilities › Debug specific route and viewport" classname="mobile-audit.test.ts" time="0">
<properties>
<property name="skip" value="">
</property>
</properties>
<skipped>
</skipped>
</testcase>
<testcase name="Debug Utilities › Debug touch targets on narrow screens" classname="mobile-audit.test.ts" time="0">
<properties>
<property name="skip" value="">
</property>
</properties>
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="mobile-audit.test.ts" timestamp="2025-07-09T10:55:28.831Z" hostname="large-mobile" tests="14" failures="0" skipped="14" time="0" errors="0">
<testcase name="JobbLogg Mobile Responsiveness Audit › Complete mobile audit across all routes and viewports" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile audit for iPhone SE, older Android phones - Critical narrow width testing" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile audit for iPhone 12/13 mini - Most common mobile viewport" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile audit for iPhone 12/13 Pro Max - Large mobile screens" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile audit for iPad portrait - Tablet responsiveness" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Critical route audit: /dashboard" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Critical route audit: /project/:projectId" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Critical route audit: /shared/:sharedId" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile performance audit" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › JobbLogg design system compliance" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Touch target WCAG AA compliance" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile layout overflow prevention" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Debug Utilities › Debug specific route and viewport" classname="mobile-audit.test.ts" time="0">
<properties>
<property name="skip" value="">
</property>
</properties>
<skipped>
</skipped>
</testcase>
<testcase name="Debug Utilities › Debug touch targets on narrow screens" classname="mobile-audit.test.ts" time="0">
<properties>
<property name="skip" value="">
</property>
</properties>
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="mobile-audit.test.ts" timestamp="2025-07-09T10:55:28.831Z" hostname="tablet-portrait" tests="14" failures="0" skipped="14" time="0" errors="0">
<testcase name="JobbLogg Mobile Responsiveness Audit › Complete mobile audit across all routes and viewports" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile audit for iPhone SE, older Android phones - Critical narrow width testing" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile audit for iPhone 12/13 mini - Most common mobile viewport" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile audit for iPhone 12/13 Pro Max - Large mobile screens" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile audit for iPad portrait - Tablet responsiveness" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Critical route audit: /dashboard" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Critical route audit: /project/:projectId" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Critical route audit: /shared/:sharedId" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile performance audit" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › JobbLogg design system compliance" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Touch target WCAG AA compliance" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="JobbLogg Mobile Responsiveness Audit › Mobile layout overflow prevention" classname="mobile-audit.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Debug Utilities › Debug specific route and viewport" classname="mobile-audit.test.ts" time="0">
<properties>
<property name="skip" value="">
</property>
</properties>
<skipped>
</skipped>
</testcase>
<testcase name="Debug Utilities › Debug touch targets on narrow screens" classname="mobile-audit.test.ts" time="0">
<properties>
<property name="skip" value="">
</property>
</properties>
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>