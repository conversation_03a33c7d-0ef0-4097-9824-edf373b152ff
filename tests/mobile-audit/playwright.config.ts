import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright configuration for JobbLogg mobile responsiveness audit
 * Optimized for testing across critical mobile viewports with Norwegian locale
 */
export default defineConfig({
  testDir: './src/tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 1,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html', { outputFolder: 'reports/playwright-html' }],
    ['json', { outputFile: 'reports/test-results.json' }],
    ['junit', { outputFile: 'reports/junit.xml' }]
  ],
  use: {
    baseURL: process.env.BASE_URL || 'http://localhost:5173',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    locale: 'nb-NO', // Norwegian locale
    timezoneId: 'Europe/Oslo',
    colorScheme: 'light', // JobbLogg uses white-only design
  },
  timeout: 120000, // 120 seconds for comprehensive audits
  projects: [
    // Ultra-narrow mobile (iPhone SE, older Android)
    {
      name: 'ultra-narrow-mobile',
      use: {
        ...devices['iPhone SE'],
        viewport: { width: 320, height: 568 },
        deviceScaleFactor: 2,
        isMobile: true,
        hasTouch: true,
      },
    },
    // Standard mobile (iPhone 12/13 mini)
    {
      name: 'standard-mobile',
      use: {
        ...devices['iPhone 12 mini'],
        viewport: { width: 375, height: 812 },
        deviceScaleFactor: 3,
        isMobile: true,
        hasTouch: true,
      },
    },
    // Large mobile (iPhone 12/13 Pro Max)
    {
      name: 'large-mobile',
      use: {
        ...devices['iPhone 12 Pro Max'],
        viewport: { width: 414, height: 896 },
        deviceScaleFactor: 3,
        isMobile: true,
        hasTouch: true,
      },
    },
    // Tablet portrait (iPad)
    {
      name: 'tablet-portrait',
      use: {
        ...devices['iPad'],
        viewport: { width: 768, height: 1024 },
        deviceScaleFactor: 2,
        isMobile: false,
        hasTouch: true,
      },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:5173',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000,
  },
  outputDir: 'reports/test-artifacts',
  timeout: 30 * 1000,
  expect: {
    timeout: 10 * 1000,
  },
});
