# JobbLogg Mobile Responsiveness Audit Framework

A comprehensive Playwright-based testing framework for auditing mobile responsiveness across the JobbLogg React application.

## 🎯 Overview

This framework systematically tests all routes across multiple mobile viewports to ensure WCAG AA compliance, optimal touch targets, responsive design, and JobbLogg design system adherence.

## 📱 Test Coverage

### Viewports Tested
- **Ultra-narrow mobile**: 320×568px (iPhone SE, older devices)
- **Standard mobile**: 375×812px (iPhone 12/13/14)
- **Large mobile**: 414×896px (iPhone 12/13/14 Plus)
- **Tablet portrait**: 768×1024px (iPad)

### Validation Categories
1. **Layout Overflow Detection** - Horizontal scrolling, fixed-width elements
2. **Touch Target Compliance** - WCAG AA 44×44px minimum requirements
3. **Responsive Design Validation** - Breakpoint usage, mobile-first design
4. **Typography & Accessibility** - Font sizes, contrast ratios, line heights
5. **Media & Images** - Responsive images, alt text, optimization
6. **Interactive Component Testing** - Button states, form inputs, navigation
7. **Performance & Accessibility Audits** - Core Web Vitals, Lighthouse scores
8. **JobbLogg Design System Compliance** - Token usage, Norwegian localization

## 🚀 Quick Start

### Installation

```bash
cd tests/mobile-audit
npm install
npx playwright install
```

### Run Complete Audit

```bash
# Run full audit across all routes and viewports
npm run audit

# Run in development mode (localhost:5173)
npm run audit:dev

# Run in production mode
npm run audit:prod
```

### Targeted Testing

```bash
# Test specific route
npm run audit:route -- --route="/dashboard"

# Test specific viewport
npm run audit:viewport -- --viewport="ultra-narrow-mobile"

# Generate reports only
npm run report
```

## 📊 Reports Generated

### 1. JSON Report (`reports/mobile-audit-report.json`)
Complete machine-readable results with detailed issue tracking.

### 2. Markdown Report (`reports/mobile-audit-report.md`)
Human-readable summary with actionable recommendations.

### 3. HTML Report (`reports/mobile-audit-report.html`)
Interactive dashboard with visual metrics and filtering.

## 🔧 Automated Fix Suggestions

The framework generates automated patches for common issues:

### Touch Target Fixes
```jsx
// Before
<button className="p-1">Small Button</button>

// After (auto-generated)
<button className="p-1 touch-target">Small Button</button>
```

### Layout Overflow Fixes
```jsx
// Before
<div className="w-96">Fixed width content</div>

// After (auto-generated)
<div className="w-96 max-w-full overflow-hidden">Fixed width content</div>
```

### Design Token Migration
```jsx
// Before
<div className="bg-blue-600 text-white">

// After (auto-generated)
<div className="bg-jobblogg-primary text-jobblogg-text-strong">
```

## 📋 Configuration

### Custom Breakpoints
```typescript
// config.ts
export const JOBBLOGG_PATTERNS = {
  BREAKPOINTS: ['xxs:', 'xs:', 'sm:', 'md:', 'lg:', 'xl:'],
  DESIGN_TOKENS: ['jobblogg-primary', 'jobblogg-success', 'jobblogg-warning']
};
```

### WCAG AA Thresholds
```typescript
export const ACCESSIBILITY_THRESHOLDS = {
  MIN_CONTRAST_RATIO: 4.5,
  MIN_FONT_SIZE: 16, // pixels
  MIN_TOUCH_TARGET: 44, // pixels
  MIN_TOUCH_SPACING: 8 // pixels
};
```

## 🧪 Test Structure

### Complete Audit Test
```typescript
test('Complete mobile audit across all routes and viewports', async () => {
  const results = await auditRunner.runCompleteAudit();
  const reports = await auditRunner.generateReports(results);
  const patchFiles = await auditRunner.generateFixSuggestions(results);
});
```

### Viewport-Specific Tests
```typescript
VIEWPORTS.forEach(viewport => {
  test(`Mobile audit for ${viewport.description}`, async () => {
    const results = await auditRunner.runViewportAudit(viewport.name);
    // Validate viewport-specific requirements
  });
});
```

### Critical Route Tests
```typescript
const criticalRoutes = ['/dashboard', '/project/:projectId', '/shared/:sharedId'];
criticalRoutes.forEach(routePath => {
  test(`Critical route audit: ${routePath}`, async () => {
    const results = await auditRunner.runRouteAudit(routePath);
    // Enforce stricter requirements for critical routes
  });
});
```

## 🎨 JobbLogg-Specific Validations

### Design Token Compliance
- Validates usage of `jobblogg-` prefixed tokens
- Identifies hardcoded colors that should use design system
- Ensures consistent styling across components

### Norwegian Localization Support
- Validates Norwegian character rendering (æ, ø, å)
- Checks font support for Norwegian text
- Ensures proper locale formatting

### Ultra-Narrow Screen Support
- Tests custom `xxs:352px` breakpoint
- Validates touch targets on constrained screens
- Ensures functionality at 320px width

## 📈 Performance Metrics

### Core Web Vitals
- **LCP (Largest Contentful Paint)**: < 2.5s
- **FID (First Input Delay)**: < 100ms
- **CLS (Cumulative Layout Shift)**: < 0.1

### Lighthouse Scores
- **Performance**: ≥ 80/100
- **Accessibility**: ≥ 90/100
- **Best Practices**: ≥ 90/100

## 🔍 Debugging

### Debug Specific Issues
```typescript
// Uncomment in mobile-audit.test.ts
test.skip('Debug specific route and viewport', async () => {
  const results = await auditRunner.runRouteAudit('/dashboard', 'ultra-narrow-mobile');
  console.log('Debug results:', JSON.stringify(results, null, 2));
});
```

### Common Issue Patterns
1. **Touch targets too small**: Add `touch-target` utility class
2. **Horizontal overflow**: Add `max-w-full overflow-hidden`
3. **Missing responsive classes**: Add breakpoint variants
4. **Hardcoded colors**: Replace with `jobblogg-` tokens
5. **Poor contrast**: Use high-contrast design tokens

## 🚦 CI/CD Integration

### GitHub Actions Example
```yaml
- name: Run Mobile Audit
  run: |
    cd tests/mobile-audit
    npm install
    npx playwright install
    npm run audit:prod
    
- name: Upload Reports
  uses: actions/upload-artifact@v3
  with:
    name: mobile-audit-reports
    path: tests/mobile-audit/reports/
```

## 📚 Architecture

### Modular Validation System
- `LayoutValidation`: Overflow detection, responsive design
- `TouchTargetValidation`: WCAG AA touch target compliance
- `AccessibilityValidation`: Contrast, font sizes, alt text
- `PerformanceValidation`: Core Web Vitals, Lighthouse integration

### Route Discovery
- Automatic scanning of `src/pages/` directory
- Dynamic parameter resolution using test data
- Authentication pattern detection

### Report Generation
- Multi-format output (JSON, Markdown, HTML)
- Actionable recommendations
- Performance trend tracking

## 🤝 Contributing

1. Add new validation modules in `src/validation-modules/`
2. Extend route patterns in `src/config.ts`
3. Update test thresholds based on requirements
4. Add new fix patterns in `src/auto-fix-generator.ts`

## 📄 License

Part of the JobbLogg project - internal development tool.

---

*Generated by JobbLogg Mobile Audit Framework v1.0.0*
