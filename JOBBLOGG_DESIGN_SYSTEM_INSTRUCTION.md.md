📘 PROSJEKTNAVN: JobbLogg (2025)
🎯 FORMÅL: Moderne webapplikasjon for håndverkere med mobiltilpasset dokumentasjonssystem
🎨 DESIGN: Flat design, minimalistisk, WCAG AA-tilgjengelighet, mobile-first, white-only (ingen dark mode)

---

✅ DESIGN- OG KOMPONENTSYSTEM
- Bruk **kun ferdige komponenter og CSS-klasser** definert i `src/components/ui/*` og `index.css`
- **Aldri** bruk inline styling eller hardkodede Tailwind-klasser – bruk `jobblogg-*` tokens og eksisterende utility-klasser
- Alle komponenter bygger på:
  - `.btn-modern`, `.card-elevated`, `.input-modern`, `.alert-success`, `text-heading-2`, `space-section`, `mobile-padding`, `hover-lift`, etc.
- Layout bruker `PageLayout`, `DashboardLayout` og `.container-*` klasser med responsiv spacing
- Typografi: Kun `Inter`, med `text-heading-*`, `text-body`, `text-muted`, etc.

---

🎨 FARGESYSTEM OG KONTRAST
- Alle farger hentes fra `tailwind.config.js` via `jobblogg-*` tokens:
  - `jobblogg-primary`: CTA-blå (`#1D4ED8`)
  - `jobblogg-accent`: Suksess-grønn (`#10B981`)
  - `jobblogg-text-strong`, `jobblogg-text-medium`, `jobblogg-text-muted`: Tekstfarger
- Bakgrunner: `white`, `jobblogg-neutral`, `jobblogg-neutral-secondary`
- Gradienter: Bruk `gradient-blue-soft`, `gradient-card-hover` ved behov

---

📐 STIL OG TILGJENGELIGHET
- WCAG AA-kontrastkrav skal alltid oppfylles (4.5:1 minimum)
- Touch targets skal være ≥ 44px
- Fokus-stater skal bruke `.focus-ring` eller `.focus-ring-enhanced`
- Formkomponenter har ARIA, `aria-live`, validering og tydelige feilmeldinger

---

✨ INTERAKSJONER OG ANIMASJON
- Bruk micro-interaction-klasser som:
  - `hover-scale`, `hover-glow`, `interactive-press`, `animate-fade-in`, `animate-slide-up`
- Bevegelse skal være subtil og ytelseseffektiv (transform/opacity)
- Alle interaktive komponenter skal respondere på hover, focus og active

---

📱 MOBILFOKUS
- Alt er designet **mobile-first** med responsive klasser (`sm:`, `md:`, `lg:`)
- Bruk `.grid-mobile-cards`, `.mobile-padding`, `.mobile-gap`, `.touch-feedback` på relevante elementer
- Ikke bruk media queries manuelt – bruk Tailwind utilities

---

🚫 IKKE TILLATT:
- ❌ Inline styles (`style={...}`)
- ❌ Hardkodede Tailwind-farger (`bg-blue-500`)
- ❌ Bruk av daisyUI eller base-100/base-content
- ❌ Dark mode
- ❌ Komponenter som ikke bruker `jobblogg-*` tokens eller utiliteter

---

📦 ØVRIG STRUKTUR:
- Komponenter: `src/components/ui/`
- Layout: `PageLayout.tsx`, `DashboardLayout.tsx`
- Tokens: `tailwind.config.js`
- CSS: `index.css`
- Autentisering: Clerk
- Backend: Convex.dev
- Designsystem: 100 % ferdig og skal brukes videre som det er

📌 Husk: Bruk kun eksisterende struktur og tokens. Ingen nye tokens, klasser eller designprinsipper skal introduseres uten eksplisitt godkjenning.
