<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Draft Functionality</title>
    <style>
        body {
            font-family: Inter, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        }
        .draft-card {
            background: white;
            border: 2px dashed #f59e0b;
            border-radius: 12px;
            padding: 20px;
            margin: 10px 0;
        }
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            background-color: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.2);
            border-radius: 9999px;
            font-size: 12px;
            font-weight: 500;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: 500;
        }
        .success { background-color: #dcfce7; color: #166534; }
        .error { background-color: #fef2f2; color: #dc2626; }
        .warning { background-color: #fef3c7; color: #d97706; }
        button {
            background-color: #2563eb;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            min-height: 44px;
        }
        button:hover {
            background-color: #1d4ed8;
        }
        button.danger {
            background-color: #dc2626;
        }
        button.danger:hover {
            background-color: #b91c1c;
        }
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            margin: 5px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>JobbLogg Draft Functionality Test</h1>
    
    <div class="test-section">
        <h2>Create Draft Data</h2>
        <p>Fill out this form to simulate creating a draft project:</p>
        
        <div class="form-group">
            <label for="projectName">Prosjektnavn *</label>
            <input type="text" id="projectName" placeholder="F.eks. Kjøkkenrenovering">
        </div>
        
        <div class="form-group">
            <label for="customerName">Kundenavn</label>
            <input type="text" id="customerName" placeholder="F.eks. Ola Nordmann">
        </div>
        
        <div class="form-group">
            <label for="customerType">Kundetype</label>
            <select id="customerType">
                <option value="privat">Privatkunde</option>
                <option value="firma">Bedriftskunde</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="address">Adresse</label>
            <input type="text" id="address" placeholder="F.eks. Storgata 15, 0123 Oslo">
        </div>
        
        <div class="form-group">
            <label for="description">Beskrivelse</label>
            <textarea id="description" rows="3" placeholder="Beskriv prosjektet..."></textarea>
        </div>
        
        <button onclick="createDraft()">Opprett kladd</button>
        <button onclick="clearDraft()" class="danger">Slett kladd</button>
    </div>

    <div class="test-section">
        <h2>Draft Display (simulates Dashboard)</h2>
        <div id="draftDisplay">
            <p>Ingen kladd funnet</p>
        </div>
        <button onclick="loadAndDisplayDraft()">Oppdater visning</button>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="results"></div>
    </div>

    <script>
        const STORAGE_KEY = 'jobblogg-create-project-form';
        
        function createDraft() {
            const formData = {
                projectName: document.getElementById('projectName').value,
                description: document.getElementById('description').value,
                customerName: document.getElementById('customerName').value,
                customerType: document.getElementById('customerType').value,
                contactPerson: '',
                phone: '',
                email: '',
                address: document.getElementById('address').value,
                orgNumber: '',
                notes: ''
            };
            
            const draftData = {
                formData,
                useExistingCustomer: false,
                selectedCustomerId: '',
                timestamp: Date.now()
            };
            
            try {
                localStorage.setItem(STORAGE_KEY, JSON.stringify(draftData));
                showResult('Kladd opprettet!', 'success');
                loadAndDisplayDraft();
            } catch (error) {
                showResult('Feil ved opprettelse av kladd: ' + error.message, 'error');
            }
        }
        
        function clearDraft() {
            try {
                localStorage.removeItem(STORAGE_KEY);
                showResult('Kladd slettet!', 'success');
                loadAndDisplayDraft();
            } catch (error) {
                showResult('Feil ved sletting av kladd: ' + error.message, 'error');
            }
        }
        
        function loadAndDisplayDraft() {
            try {
                const savedData = localStorage.getItem(STORAGE_KEY);
                const displayDiv = document.getElementById('draftDisplay');
                
                if (savedData) {
                    const parsed = JSON.parse(savedData);
                    
                    // Check if draft is valid
                    if (parsed.timestamp && parsed.formData?.projectName?.trim()) {
                        const daysSinceCreated = (Date.now() - parsed.timestamp) / (1000 * 60 * 60 * 24);
                        
                        if (daysSinceCreated <= 7) {
                            displayDiv.innerHTML = createDraftCard(parsed);
                            showResult('Kladd lastet inn!', 'success');
                        } else {
                            displayDiv.innerHTML = '<p>Kladd er utløpt (eldre enn 7 dager)</p>';
                            showResult('Kladd er utløpt', 'warning');
                        }
                    } else {
                        displayDiv.innerHTML = '<p>Ugyldig kladd-data</p>';
                        showResult('Ugyldig kladd-data', 'error');
                    }
                } else {
                    displayDiv.innerHTML = '<p>Ingen kladd funnet</p>';
                    showResult('Ingen kladd funnet', 'warning');
                }
            } catch (error) {
                showResult('Feil ved lasting av kladd: ' + error.message, 'error');
            }
        }
        
        function createDraftCard(draftData) {
            const timeAgo = formatTimestamp(draftData.timestamp);
            
            return `
                <div class="draft-card">
                    <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 15px;">
                        <span class="badge">📝 Kladd</span>
                        <small style="color: #6b7280;">${timeAgo}</small>
                    </div>
                    
                    <h3 style="margin: 0 0 10px 0; font-size: 18px; font-weight: 600;">
                        ${draftData.formData.projectName || 'Uten navn'}
                    </h3>
                    
                    ${draftData.formData.customerName ? `
                        <p style="margin: 5px 0; color: #6b7280; font-size: 14px;">
                            👤 ${draftData.formData.customerName}
                            ${draftData.formData.customerType === 'firma' ? '<span style="background: #f3f4f6; padding: 2px 6px; border-radius: 4px; font-size: 12px;">Bedrift</span>' : ''}
                        </p>
                    ` : ''}
                    
                    ${draftData.formData.address ? `
                        <p style="margin: 5px 0; color: #6b7280; font-size: 14px;">
                            📍 ${draftData.formData.address}
                        </p>
                    ` : ''}
                    
                    ${draftData.formData.description ? `
                        <p style="margin: 10px 0; color: #4b5563; font-size: 14px;">
                            ${draftData.formData.description}
                        </p>
                    ` : ''}
                    
                    <div style="margin-top: 15px; display: flex; gap: 10px;">
                        <button onclick="continueDraft()" style="flex: 1;">Fortsett redigering</button>
                        <button onclick="confirmDeleteDraft()" class="danger">Slett kladd</button>
                    </div>
                </div>
            `;
        }
        
        function formatTimestamp(timestamp) {
            const now = Date.now();
            const diffMinutes = Math.floor((now - timestamp) / (1000 * 60));
            const diffHours = Math.floor(diffMinutes / 60);
            const diffDays = Math.floor(diffHours / 24);

            if (diffMinutes < 60) {
                return `${diffMinutes} min siden`;
            } else if (diffHours < 24) {
                return `${diffHours} timer siden`;
            } else {
                return `${diffDays} dager siden`;
            }
        }
        
        function continueDraft() {
            showResult('Ville navigert til /create (simulert)', 'success');
        }
        
        function confirmDeleteDraft() {
            if (confirm('Er du sikker på at du vil slette kladden? Denne handlingen kan ikke angres.')) {
                clearDraft();
            }
        }
        
        function showResult(message, type) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            results.appendChild(div);
            
            // Keep only last 5 results
            while (results.children.length > 5) {
                results.removeChild(results.firstChild);
            }
        }
        
        // Load draft on page load
        window.onload = function() {
            loadAndDisplayDraft();
            showResult('Test side lastet. Prøv å opprette en kladd og test funksjonaliteten.', 'success');
        };
    </script>
</body>
</html>
