/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    screens: {
      'xxs': '352px',  // Ultra-narrow screens (≤351px handled by default)
      'xs': '378px',   // Narrow mobile screens
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    extend: {
      colors: {
        'jobblogg': {
          // Primary colors - Modern blue system
          'primary': '#2563EB',         // Dypere blå for CTAs og active states
          'primary-light': '#3B82F6',   // Lighter blue for hover states
          'primary-dark': '#1E40AF',    // Darker blue for pressed states
          'primary-soft': '#DBEAFE',    // Soft blue for backgrounds

          // Accent colors - Success green system
          'accent': '#10B981',          // Green for success states
          'accent-light': '#34D399',    // Lighter green for hover states
          'accent-dark': '#059669',     // Darker green for pressed states
          'accent-soft': '#D1FAE5',     // Soft green for backgrounds

          // Highlight color - Visual variety for tags, badges etc.
          'highlight': '#6366F1',       // Indigo-lilla aksentfarge

          // Warning colors - Amber system
          'warning': '#F59E0B',         // Dypere amber for warning states
          'warning-light': '#FCD34D',   // Lighter amber for hover states
          'warning-dark': '#D97706',    // Darker amber for pressed states
          'warning-soft': '#FEF3C7',    // Soft amber for backgrounds

          // Error colors - Red system
          'error': '#B91C1C',           // Dypere rødfarge for error states
          'error-light': '#EF4444',     // Lighter red for hover states
          'error-dark': '#991B1B',      // Even darker for contrast
          'error-soft': '#FEE2E2',      // Soft red for backgrounds

          // Neutral colors - Light mode optimized
          'neutral': '#FAFAF9',         // Ny varm nøytral bakgrunn
          'neutral-secondary': '#F3F4F6', // Secondary background with better contrast
          'neutral-light': '#FDFDFD',   // Lightest neutral for subtle surfaces
          'neutral-dark': '#D1D5DB',    // Darker neutral for borders

          // Surface - Light warm surface for cards/sections
          'surface': '#FAFAF6',         // Til seksjoner, alerts og cards

          // Text colors - WCAG AA compliant
          'text-strong': '#111827',     // Dyp heading-farge (16.8:1 kontrast)
          'text-medium': '#4B5563',     // Medium gray for body text (7.3:1)
          'text-muted': '#6B7280',      // Sekundærtekst (4.9:1 kontrast)

          // Border colors
          'border': '#E5E7EB',          // Default border color
          'border-light': '#F3F4F6',    // Light border color
          'border-dark': '#D1D5DB',     // Dark border color

          // Additional harmonious color variants for gradients
          'blue-50': '#EFF6FF',         // Very light blue for subtle backgrounds
          'blue-100': '#DBEAFE',        // Light blue for card backgrounds
          'indigo-50': '#EEF2FF',       // Very light indigo for gradient backgrounds
          'indigo-100': '#E0E7FF',      // Light indigo for hover states
        }
      },
      fontFamily: {
        'sans': ['Inter', 'system-ui', 'sans-serif'],
      },
      fontSize: {
        // Typography hierarchy - Modern 2025 design
        'heading-1': ['2.5rem', { lineHeight: '1.2', fontWeight: '700' }],  // 40px
        'heading-2': ['2rem', { lineHeight: '1.25', fontWeight: '600' }],   // 32px
        'heading-3': ['1.5rem', { lineHeight: '1.3', fontWeight: '600' }],  // 24px
        'body': ['1rem', { lineHeight: '1.6', fontWeight: '400' }],         // 16px
        'small': ['0.875rem', { lineHeight: '1.5', fontWeight: '400' }],    // 14px
        'caption': ['0.75rem', { lineHeight: '1.4', fontWeight: '400' }],   // 12px
      },
      spacing: {
        '18': '4.5rem',   // 72px
        '88': '22rem',    // 352px
        // Enhanced spacing system for modern design
        '15': '3.75rem',  // 60px
        '22': '5.5rem',   // 88px
        '30': '7.5rem',   // 120px
      },
      borderRadius: {
        'xl': '0.75rem',  // 12px - Standard for cards and buttons
        '2xl': '1rem',    // 16px - For larger containers
        '3xl': '1.5rem',  // 24px - For hero sections
      },
      boxShadow: {
        'soft': '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
        'medium': '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
        'large': '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'bounce-soft': 'bounceSoft 0.6s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        bounceSoft: {
          '0%, 20%, 53%, 80%, 100%': { transform: 'translate3d(0,0,0)' },
          '40%, 43%': { transform: 'translate3d(0, -8px, 0)' },
          '70%': { transform: 'translate3d(0, -4px, 0)' },
          '90%': { transform: 'translate3d(0, -2px, 0)' },
        },
      },
    },
  },
  plugins: [],
}
