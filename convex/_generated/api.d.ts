/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as clearAllProjectData from "../clearAllProjectData.js";
import type * as customers from "../customers.js";
import type * as imageLikes from "../imageLikes.js";
import type * as logEntries from "../logEntries.js";
import type * as messages from "../messages.js";
import type * as projects from "../projects.js";
import type * as testDataUtilities from "../testDataUtilities.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  clearAllProjectData: typeof clearAllProjectData;
  customers: typeof customers;
  imageLikes: typeof imageLikes;
  logEntries: typeof logEntries;
  messages: typeof messages;
  projects: typeof projects;
  testDataUtilities: typeof testDataUtilities;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
