import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { nanoid } from "nanoid";

// Toggle like on an image (add if not liked, remove if already liked)
export const toggleLike = mutation({
  args: {
    logEntryId: v.id("logEntries"),
    projectId: v.id("projects"),
    sharedId: v.string(),
    customerSessionId: v.string(),
    customerName: v.optional(v.string()),
    customerEmail: v.optional(v.string()),
    ipAddress: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    // Verify the project exists and is publicly shared
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (!project.isPubliclyShared) {
      throw new Error("Dette prosjektet er ikke delt offentlig");
    }

    if (project.sharedId !== args.sharedId) {
      throw new Error("Ugyldig deling-ID");
    }

    // Verify the log entry exists and belongs to the project
    const logEntry = await ctx.db.get(args.logEntryId);
    if (!logEntry) {
      throw new Error("Loggoppføring ikke funnet");
    }

    if (logEntry.projectId !== args.projectId) {
      throw new Error("Loggoppføring tilhører ikke dette prosjektet");
    }

    // Verify the log entry has an image
    if (!logEntry.imageId) {
      throw new Error("Denne loggoppføringen har ikke et bilde");
    }

    // Check if customer has already liked this image
    const existingLike = await ctx.db
      .query("imageLikes")
      .withIndex("by_log_entry_and_customer", (q) => 
        q.eq("logEntryId", args.logEntryId).eq("customerSessionId", args.customerSessionId)
      )
      .first();

    if (existingLike) {
      // Unlike - remove the existing like
      await ctx.db.delete(existingLike._id);
      return { success: true, action: "unliked", liked: false };
    } else {
      // Like - add a new like
      const likeId = await ctx.db.insert("imageLikes", {
        logEntryId: args.logEntryId,
        projectId: args.projectId,
        sharedId: args.sharedId,
        customerSessionId: args.customerSessionId,
        customerName: args.customerName?.trim(),
        customerEmail: args.customerEmail?.trim(),
        createdAt: Date.now(),
        ipAddress: args.ipAddress
      });

      return { success: true, action: "liked", liked: true, likeId };
    }
  }
});

// Get like status for a specific image and customer
export const getLikeStatus = query({
  args: {
    logEntryId: v.id("logEntries"),
    customerSessionId: v.string()
  },
  handler: async (ctx, args) => {
    const existingLike = await ctx.db
      .query("imageLikes")
      .withIndex("by_log_entry_and_customer", (q) => 
        q.eq("logEntryId", args.logEntryId).eq("customerSessionId", args.customerSessionId)
      )
      .first();

    return {
      liked: !!existingLike,
      likeId: existingLike?._id || null
    };
  }
});

// Get like count for a specific image
export const getLikeCount = query({
  args: {
    logEntryId: v.id("logEntries")
  },
  handler: async (ctx, args) => {
    const likes = await ctx.db
      .query("imageLikes")
      .withIndex("by_log_entry", (q) => q.eq("logEntryId", args.logEntryId))
      .collect();

    return {
      count: likes.length,
      likes: likes.map(like => ({
        customerName: like.customerName,
        createdAt: like.createdAt
      }))
    };
  }
});

// Get like counts for multiple images (for contractor view)
export const getLikeCountsForProject = query({
  args: {
    projectId: v.id("projects"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Verify the project belongs to the user
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til dette prosjektet");
    }

    // Get all likes for this project
    const likes = await ctx.db
      .query("imageLikes")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .collect();

    // Group likes by log entry
    const likesByEntry = new Map();
    
    for (const like of likes) {
      if (!likesByEntry.has(like.logEntryId)) {
        likesByEntry.set(like.logEntryId, []);
      }
      likesByEntry.get(like.logEntryId).push({
        customerName: like.customerName,
        createdAt: like.createdAt
      });
    }

    // Convert to object format
    const result: Record<string, { count: number; likes: Array<{ customerName?: string; createdAt: number }> }> = {};
    
    for (const [logEntryId, entryLikes] of likesByEntry.entries()) {
      result[logEntryId] = {
        count: entryLikes.length,
        likes: entryLikes
      };
    }

    return result;
  }
});

// Get like statistics for a project (for contractor dashboard)
export const getProjectLikeStats = query({
  args: {
    projectId: v.id("projects"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Verify the project belongs to the user
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til dette prosjektet");
    }

    // Get all likes for this project
    const likes = await ctx.db
      .query("imageLikes")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .collect();

    // Calculate statistics
    const totalLikes = likes.length;
    const uniqueCustomers = new Set(likes.map(like => like.customerSessionId)).size;
    const mostRecentLike = likes.length > 0 
      ? Math.max(...likes.map(like => like.createdAt))
      : null;

    // Get most liked image
    const likesByEntry = new Map();
    for (const like of likes) {
      likesByEntry.set(like.logEntryId, (likesByEntry.get(like.logEntryId) || 0) + 1);
    }

    let mostLikedEntry = null;
    let maxLikes = 0;
    for (const [logEntryId, count] of likesByEntry.entries()) {
      if (count > maxLikes) {
        maxLikes = count;
        mostLikedEntry = logEntryId;
      }
    }

    return {
      totalLikes,
      uniqueCustomers,
      mostRecentLike,
      mostLikedEntry,
      mostLikedCount: maxLikes
    };
  }
});

// Get likes for shared project view (public access)
export const getLikesForSharedProject = query({
  args: {
    projectId: v.id("projects"),
    sharedId: v.string()
  },
  handler: async (ctx, args) => {
    // Verify the project exists and is publicly shared
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      return {};
    }

    if (!project.isPubliclyShared || project.sharedId !== args.sharedId) {
      return {};
    }

    // Get all likes for this project
    const likes = await ctx.db
      .query("imageLikes")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .collect();

    // Group likes by log entry
    const likesByEntry = new Map();
    
    for (const like of likes) {
      if (!likesByEntry.has(like.logEntryId)) {
        likesByEntry.set(like.logEntryId, 0);
      }
      likesByEntry.set(like.logEntryId, likesByEntry.get(like.logEntryId) + 1);
    }

    // Convert to object format
    const result: Record<string, number> = {};
    
    for (const [logEntryId, count] of likesByEntry.entries()) {
      result[logEntryId] = count;
    }

    return result;
  }
});

// Generate a customer session ID for anonymous customers
export const generateCustomerSessionId = mutation({
  args: {},
  handler: async (ctx, args) => {
    return {
      sessionId: nanoid(16)
    };
  }
});
