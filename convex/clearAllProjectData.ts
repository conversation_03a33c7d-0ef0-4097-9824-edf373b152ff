import { mutation } from './_generated/server';
import { v } from 'convex/values';

/**
 * DEVELOPMENT UTILITY: Clear All Project Data
 * 
 * This script safely deletes all project-related data from the Convex database
 * while preserving user accounts and authentication data.
 * 
 * ⚠️  WARNING: This is a destructive operation that cannot be undone!
 * ⚠️  Only use in development/testing environments!
 * 
 * WHAT GETS DELETED:
 * - All projects from the `projects` table
 * - All project logs from the `logEntries` table  
 * - All chat messages from the `messages` table
 * - All typing indicators from the `typingIndicators` table
 * - All image likes from the `imageLikes` table
 * - All customer records from the `customers` table
 * 
 * WHAT IS PRESERVED:
 * - User accounts and authentication data (Clerk users)
 * - File storage (images/attachments remain in Convex storage)
 * - System configuration and settings
 * 
 * USAGE:
 * From Convex Dashboard:
 * 1. Go to your project dashboard
 * 2. Navigate to "Functions" tab
 * 3. Find "clearAllProjectData" mutation
 * 4. Execute with: { confirmationCode: "DELETE_ALL_PROJECT_DATA" }
 * 
 * From CLI:
 * npx convex run clearAllProjectData --arg confirmationCode "DELETE_ALL_PROJECT_DATA"
 */
export const clearAllProjectData = mutation({
  args: {
    confirmationCode: v.string(),
    // Optional: Add environment check to prevent production usage
    environment: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    // Safety check: Require exact confirmation code
    if (args.confirmationCode !== "DELETE_ALL_PROJECT_DATA") {
      throw new Error(
        "❌ Invalid confirmation code. To proceed, use: { confirmationCode: 'DELETE_ALL_PROJECT_DATA' }"
      );
    }

    // Optional: Environment safety check (uncomment for production safety)
    // if (args.environment === "production") {
    //   throw new Error("❌ This operation is not allowed in production environment!");
    // }

    console.log("🚨 STARTING PROJECT DATA DELETION PROCESS...");
    console.log("⚠️  This operation cannot be undone!");

    const deletionResults = {
      typingIndicators: 0,
      messages: 0,
      imageLikes: 0,
      logEntries: 0,
      projects: 0,
      customers: 0,
      totalDeleted: 0,
      errors: [] as string[]
    };

    try {
      // 1. Delete typing indicators (no dependencies)
      console.log("🔄 Deleting typing indicators...");
      const typingIndicators = await ctx.db.query("typingIndicators").collect();
      for (const indicator of typingIndicators) {
        await ctx.db.delete(indicator._id);
        deletionResults.typingIndicators++;
      }
      console.log(`✅ Deleted ${deletionResults.typingIndicators} typing indicators`);

      // 2. Delete chat messages (depends on logEntries)
      console.log("🔄 Deleting chat messages...");
      const messages = await ctx.db.query("messages").collect();
      for (const message of messages) {
        await ctx.db.delete(message._id);
        deletionResults.messages++;
      }
      console.log(`✅ Deleted ${deletionResults.messages} chat messages`);

      // 3. Delete image likes (depends on logEntries and projects)
      console.log("🔄 Deleting image likes...");
      const imageLikes = await ctx.db.query("imageLikes").collect();
      for (const like of imageLikes) {
        await ctx.db.delete(like._id);
        deletionResults.imageLikes++;
      }
      console.log(`✅ Deleted ${deletionResults.imageLikes} image likes`);

      // 4. Delete log entries (depends on projects)
      console.log("🔄 Deleting log entries...");
      const logEntries = await ctx.db.query("logEntries").collect();
      for (const entry of logEntries) {
        await ctx.db.delete(entry._id);
        deletionResults.logEntries++;
      }
      console.log(`✅ Deleted ${deletionResults.logEntries} log entries`);

      // 5. Delete projects (depends on customers)
      console.log("🔄 Deleting projects...");
      const projects = await ctx.db.query("projects").collect();
      for (const project of projects) {
        await ctx.db.delete(project._id);
        deletionResults.projects++;
      }
      console.log(`✅ Deleted ${deletionResults.projects} projects`);

      // 6. Delete customers (no dependencies on them after projects are deleted)
      console.log("🔄 Deleting customers...");
      const customers = await ctx.db.query("customers").collect();
      for (const customer of customers) {
        await ctx.db.delete(customer._id);
        deletionResults.customers++;
      }
      console.log(`✅ Deleted ${deletionResults.customers} customers`);

      // Calculate total
      deletionResults.totalDeleted = 
        deletionResults.typingIndicators +
        deletionResults.messages +
        deletionResults.imageLikes +
        deletionResults.logEntries +
        deletionResults.projects +
        deletionResults.customers;

      console.log("🎉 PROJECT DATA DELETION COMPLETED SUCCESSFULLY!");
      console.log("📊 DELETION SUMMARY:");
      console.log(`   • Typing Indicators: ${deletionResults.typingIndicators}`);
      console.log(`   • Chat Messages: ${deletionResults.messages}`);
      console.log(`   • Image Likes: ${deletionResults.imageLikes}`);
      console.log(`   • Log Entries: ${deletionResults.logEntries}`);
      console.log(`   • Projects: ${deletionResults.projects}`);
      console.log(`   • Customers: ${deletionResults.customers}`);
      console.log(`   • TOTAL DELETED: ${deletionResults.totalDeleted} records`);
      console.log("✅ Database is now clean for fresh testing!");

      return {
        success: true,
        message: "All project data has been successfully deleted",
        deletionResults,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      deletionResults.errors.push(errorMessage);
      
      console.error("❌ ERROR DURING DELETION PROCESS:", errorMessage);
      console.log("📊 PARTIAL DELETION RESULTS:");
      console.log(`   • Typing Indicators: ${deletionResults.typingIndicators}`);
      console.log(`   • Chat Messages: ${deletionResults.messages}`);
      console.log(`   • Image Likes: ${deletionResults.imageLikes}`);
      console.log(`   • Log Entries: ${deletionResults.logEntries}`);
      console.log(`   • Projects: ${deletionResults.projects}`);
      console.log(`   • Customers: ${deletionResults.customers}`);

      throw new Error(`Deletion process failed: ${errorMessage}. Partial results: ${JSON.stringify(deletionResults)}`);
    }
  }
});

/**
 * DEVELOPMENT UTILITY: Get Project Data Count
 * 
 * This query returns the current count of all project-related data
 * without deleting anything. Useful for checking database state
 * before and after running the deletion script.
 */
export const getProjectDataCount = mutation({
  args: {},
  handler: async (ctx) => {
    try {
      const counts = {
        typingIndicators: 0,
        messages: 0,
        imageLikes: 0,
        logEntries: 0,
        projects: 0,
        customers: 0,
        total: 0
      };

      // Count all project-related data
      counts.typingIndicators = (await ctx.db.query("typingIndicators").collect()).length;
      counts.messages = (await ctx.db.query("messages").collect()).length;
      counts.imageLikes = (await ctx.db.query("imageLikes").collect()).length;
      counts.logEntries = (await ctx.db.query("logEntries").collect()).length;
      counts.projects = (await ctx.db.query("projects").collect()).length;
      counts.customers = (await ctx.db.query("customers").collect()).length;

      counts.total = counts.typingIndicators + counts.messages + counts.imageLikes + 
                    counts.logEntries + counts.projects + counts.customers;

      console.log("📊 CURRENT PROJECT DATA COUNT:");
      console.log(`   • Typing Indicators: ${counts.typingIndicators}`);
      console.log(`   • Chat Messages: ${counts.messages}`);
      console.log(`   • Image Likes: ${counts.imageLikes}`);
      console.log(`   • Log Entries: ${counts.logEntries}`);
      console.log(`   • Projects: ${counts.projects}`);
      console.log(`   • Customers: ${counts.customers}`);
      console.log(`   • TOTAL RECORDS: ${counts.total}`);

      return {
        success: true,
        counts,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("❌ ERROR GETTING DATA COUNT:", errorMessage);
      throw new Error(`Failed to get data count: ${errorMessage}`);
    }
  }
});
