# JobbLogg Development Utilities

This document describes development utility scripts for the JobbLogg application that help with testing and database management.

## 📁 Files Overview

- **`convex/clearAllProjectData.ts`** - Main deletion utility with safety features
- **`convex/testDataUtilities.ts`** - Test data creation utility
- **`scripts/reset-database.sh`** - Interactive shell script for easy database management
- **`convex/DEVELOPMENT_UTILITIES.md`** - This documentation file

## 🚀 Quick Start

### Option 1: Interactive Script (Recommended)
```bash
# From project root directory
./scripts/reset-database.sh
```

### Option 2: Direct Convex Commands
```bash
# Check current data count
npx convex run clearAllProjectData:getProjectDataCount '{}'

# Create test data (replace with your user ID)
npx convex run testDataUtilities:createTestData '{"confirmationCode": "CREATE_TEST_DATA", "testUserId": "your-clerk-user-id"}'

# Clear all data
npx convex run clearAllProjectData:clearAllProjectData '{"confirmationCode": "DELETE_ALL_PROJECT_DATA"}'
```

## ⚠️ IMPORTANT SAFETY WARNINGS

- **These utilities are for DEVELOPMENT/TESTING ONLY**
- **All deletion operations are IRREVERSIBLE**
- **Never run these scripts in production environments**
- **Always backup important data before running deletion scripts**

## 🗑️ Clear All Project Data

### Purpose
The `clearAllProjectData` utility safely deletes all project-related data from the Convex database while preserving user accounts and authentication data. This is useful for:

- Resetting the database for fresh testing
- Cleaning up test data between development cycles
- Preparing demo environments with clean state

### What Gets Deleted
- ✅ All projects from the `projects` table
- ✅ All project logs from the `logEntries` table  
- ✅ All chat messages from the `messages` table
- ✅ All typing indicators from the `typingIndicators` table
- ✅ All image likes from the `imageLikes` table
- ✅ All customer records from the `customers` table

### What Is Preserved
- ✅ User accounts and authentication data (Clerk users)
- ✅ File storage (images/attachments remain in Convex storage)
- ✅ System configuration and settings

### Usage Instructions

#### Method 1: Convex Dashboard (Recommended)
1. Open your Convex project dashboard
2. Navigate to the **"Functions"** tab
3. Find the `clearAllProjectData` mutation in the list
4. Click on it to open the execution interface
5. In the arguments field, enter:
   ```json
   {
     "confirmationCode": "DELETE_ALL_PROJECT_DATA"
   }
   ```
6. Click **"Run"** to execute the deletion
7. Monitor the console output for progress and results

#### Method 2: Convex CLI
```bash
# From your project root directory
npx convex run clearAllProjectData:clearAllProjectData '{"confirmationCode": "DELETE_ALL_PROJECT_DATA"}'
```

#### Method 3: With Environment Check (Optional)
```bash
# Add environment parameter for extra safety
npx convex run clearAllProjectData:clearAllProjectData '{"confirmationCode": "DELETE_ALL_PROJECT_DATA", "environment": "development"}'
```

### Expected Output
```
🚨 STARTING PROJECT DATA DELETION PROCESS...
⚠️  This operation cannot be undone!
🔄 Deleting typing indicators...
✅ Deleted 15 typing indicators
🔄 Deleting chat messages...
✅ Deleted 127 chat messages
🔄 Deleting image likes...
✅ Deleted 43 image likes
🔄 Deleting log entries...
✅ Deleted 89 log entries
🔄 Deleting projects...
✅ Deleted 12 projects
🔄 Deleting customers...
✅ Deleted 8 customers
🎉 PROJECT DATA DELETION COMPLETED SUCCESSFULLY!
📊 DELETION SUMMARY:
   • Typing Indicators: 15
   • Chat Messages: 127
   • Image Likes: 43
   • Log Entries: 89
   • Projects: 12
   • Customers: 8
   • TOTAL DELETED: 294 records
✅ Database is now clean for fresh testing!
```

## 📊 Get Project Data Count

### Purpose
The `getProjectDataCount` utility provides a non-destructive way to check the current state of project-related data in your database. Use this to:

- Check database state before running deletion scripts
- Verify deletion was successful
- Monitor data growth during development

### Usage Instructions

#### Convex Dashboard
1. Navigate to **"Functions"** tab
2. Find `getProjectDataCount` mutation
3. Click and run with no arguments (empty `{}`)

#### Convex CLI
```bash
npx convex run clearAllProjectData:getProjectDataCount '{}'
```

### Expected Output
```
📊 CURRENT PROJECT DATA COUNT:
   • Typing Indicators: 15
   • Chat Messages: 127
   • Image Likes: 43
   • Log Entries: 89
   • Projects: 12
   • Customers: 8
   • TOTAL RECORDS: 294
```

## 🔒 Safety Features

### Confirmation Code Requirement
The deletion script requires an exact confirmation code to prevent accidental execution:
- Required code: `"DELETE_ALL_PROJECT_DATA"`
- Case-sensitive and must match exactly
- Prevents accidental clicks or typos

### Environment Protection (Optional)
Uncomment the environment check in the script to add production protection:
```typescript
if (args.environment === "production") {
  throw new Error("❌ This operation is not allowed in production environment!");
}
```

### Transaction Safety
- Each deletion operation is wrapped in proper error handling
- Partial deletion results are logged if errors occur
- Database remains in consistent state even if script fails

### Detailed Logging
- Real-time progress updates during execution
- Comprehensive deletion summary with counts
- Error reporting with partial results
- Timestamp tracking for audit purposes

## 🚨 Emergency Recovery

If you accidentally delete important data:

1. **Stop immediately** - Don't run any more operations
2. **Check Convex storage** - Files may still exist in storage
3. **Review backups** - Check if you have recent database exports
4. **Contact support** - Convex support may be able to help with recent deletions

## 📝 Best Practices

### Before Running Deletion Scripts
1. **Verify environment** - Ensure you're in development/testing
2. **Check data count** - Run `getProjectDataCount` first
3. **Backup if needed** - Export important data if any
4. **Inform team** - Let other developers know about the reset

### After Running Deletion Scripts
1. **Verify results** - Run `getProjectDataCount` to confirm
2. **Test functionality** - Ensure app works with clean database
3. **Document changes** - Update team about the reset
4. **Monitor for issues** - Watch for any unexpected behavior

## 🔧 Customization

### Adding New Tables
If you add new project-related tables to the schema:

1. Add the table to the deletion order in `clearAllProjectData`
2. Update the count query in `getProjectDataCount`
3. Update this documentation

### Modifying Deletion Order
The current order respects foreign key dependencies:
1. `typingIndicators` (no dependencies)
2. `messages` (depends on logEntries)
3. `imageLikes` (depends on logEntries and projects)
4. `logEntries` (depends on projects)
5. `projects` (depends on customers)
6. `customers` (no dependencies after projects deleted)

## 🧪 Create Test Data

### Purpose
The `createTestData` utility creates a small set of interconnected test data across all project-related tables. This is useful for:

- Testing the deletion utilities with realistic data
- Setting up demo environments with sample content
- Verifying database relationships and functionality

### What Gets Created
- ✅ 2 test customers (1 company, 1 private)
- ✅ 2 test projects with different configurations
- ✅ 3 test log entries (user and system types)
- ✅ 3 test chat messages with reactions and read status
- ✅ 2 test image likes from different customer sessions
- ✅ 2 test typing indicators with different expiration times

### Usage Instructions

#### Convex Dashboard
1. Navigate to **"Functions"** tab
2. Find `createTestData` mutation
3. Execute with your Clerk user ID:
   ```json
   {
     "confirmationCode": "CREATE_TEST_DATA",
     "testUserId": "your-clerk-user-id-here"
   }
   ```

#### Convex CLI
```bash
npx convex run testDataUtilities:createTestData '{"confirmationCode": "CREATE_TEST_DATA", "testUserId": "your-clerk-user-id-here"}'
```

### Testing Workflow
1. **Create test data**: Run `createTestData` with your user ID
2. **Verify creation**: Run `getProjectDataCount` to see the new records
3. **Test deletion**: Run `clearAllProjectData` to clean up
4. **Verify deletion**: Run `getProjectDataCount` again to confirm cleanup

## 📞 Support

If you encounter issues with these utilities:

1. Check the console output for detailed error messages
2. Verify your confirmation code is exactly correct
3. Ensure you have proper permissions in Convex
4. Review the Convex dashboard for any system issues
5. Contact the development team for assistance

---

**Remember: These are powerful tools that can permanently delete data. Use with caution and always in development environments only!**
