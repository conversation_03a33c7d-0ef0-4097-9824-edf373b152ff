import { mutation } from './_generated/server';
import { v } from 'convex/values';

/**
 * DEVELOPMENT UTILITY: Create Test Data
 * 
 * This script creates sample project data for testing the clearAllProjectData utility.
 * It creates a small set of interconnected test data across all project-related tables.
 * 
 * ⚠️  Only use in development/testing environments!
 */
export const createTestData = mutation({
  args: {
    confirmationCode: v.string(),
    testUserId: v.string() // Clerk user ID to associate test data with
  },
  handler: async (ctx, args) => {
    // Safety check: Require confirmation code
    if (args.confirmationCode !== "CREATE_TEST_DATA") {
      throw new Error(
        "❌ Invalid confirmation code. To proceed, use: { confirmationCode: 'CREATE_TEST_DATA' }"
      );
    }

    console.log("🚀 CREATING TEST DATA...");

    const creationResults = {
      customers: 0,
      projects: 0,
      logEntries: 0,
      messages: 0,
      imageLikes: 0,
      typingIndicators: 0,
      totalCreated: 0
    };

    try {
      const now = Date.now();

      // 1. Create test customers
      console.log("🔄 Creating test customers...");
      const customer1Id = await ctx.db.insert("customers", {
        name: "Test Kunde AS",
        type: "firma",
        contactPerson: "Ola Nordmann",
        phone: "+47 123 45 678",
        email: "<EMAIL>",
        streetAddress: "Testgata 123",
        postalCode: "0123",
        city: "Oslo",
        orgNumber: "123456789",
        notes: "Test kunde for utvikling",
        userId: args.testUserId,
        createdAt: now
      });

      const customer2Id = await ctx.db.insert("customers", {
        name: "Kari Testesen",
        type: "privat",
        phone: "+47 987 65 432",
        email: "<EMAIL>",
        streetAddress: "Privatveien 456",
        postalCode: "0456",
        city: "Bergen",
        notes: "Privat testkunde",
        userId: args.testUserId,
        createdAt: now
      });
      creationResults.customers = 2;

      // 2. Create test projects
      console.log("🔄 Creating test projects...");
      const project1Id = await ctx.db.insert("projects", {
        name: "Test Prosjekt 1",
        description: "Dette er et test prosjekt for utvikling",
        userId: args.testUserId,
        customerId: customer1Id,
        sharedId: "test123456",
        createdAt: now,
        isArchived: false,
        isPubliclyShared: true,
        shareSettings: {
          showContractorNotes: true,
          accessCount: 5,
          lastAccessedAt: now
        }
      });

      const project2Id = await ctx.db.insert("projects", {
        name: "Test Prosjekt 2",
        description: "Enda et test prosjekt",
        userId: args.testUserId,
        customerId: customer2Id,
        sharedId: "test789012",
        createdAt: now + 1000,
        isArchived: false
      });
      creationResults.projects = 2;

      // 3. Create test log entries
      console.log("🔄 Creating test log entries...");
      const logEntry1Id = await ctx.db.insert("logEntries", {
        projectId: project1Id,
        userId: args.testUserId,
        description: "Test loggoppføring 1 - Startet arbeid",
        createdAt: now + 2000,
        entryType: "user"
      });

      const logEntry2Id = await ctx.db.insert("logEntries", {
        projectId: project1Id,
        userId: args.testUserId,
        description: "Test loggoppføring 2 - Fremdrift oppdatering",
        createdAt: now + 3000,
        entryType: "user"
      });

      const logEntry3Id = await ctx.db.insert("logEntries", {
        projectId: project2Id,
        userId: args.testUserId,
        description: "Test loggoppføring 3 - Prosjekt registrert",
        createdAt: now + 4000,
        entryType: "system"
      });
      creationResults.logEntries = 3;

      // 4. Create test chat messages
      console.log("🔄 Creating test chat messages...");
      const message1Id = await ctx.db.insert("messages", {
        logId: logEntry1Id,
        senderId: args.testUserId,
        senderRole: "contractor",
        text: "Hei! Dette er en test melding fra leverandør.",
        reactions: [
          {
            emoji: "👍",
            userIds: ["customer123"],
            count: 1
          }
        ],
        readBy: {
          [args.testUserId]: now + 5000
        },
        deliveryStatus: "delivered",
        deliveredTo: {
          [args.testUserId]: now + 5000
        },
        createdAt: now + 5000,
        isEdited: false,
        isDeleted: false
      });

      const message2Id = await ctx.db.insert("messages", {
        logId: logEntry1Id,
        senderId: "customer123",
        senderRole: "customer",
        text: "Takk for oppdateringen! Ser bra ut.",
        readBy: {
          "customer123": now + 6000
        },
        deliveryStatus: "delivered",
        deliveredTo: {
          "customer123": now + 6000,
          [args.testUserId]: now + 6500
        },
        createdAt: now + 6000,
        isEdited: false,
        isDeleted: false
      });

      const message3Id = await ctx.db.insert("messages", {
        logId: logEntry2Id,
        senderId: args.testUserId,
        senderRole: "contractor",
        text: "Oppdatering på fremdrift - alt går etter planen.",
        createdAt: now + 7000,
        isEdited: false,
        isDeleted: false
      });
      creationResults.messages = 3;

      // 5. Create test image likes
      console.log("🔄 Creating test image likes...");
      await ctx.db.insert("imageLikes", {
        logEntryId: logEntry1Id,
        projectId: project1Id,
        sharedId: "test123456",
        customerSessionId: "session123",
        customerName: "Test Kunde",
        customerEmail: "<EMAIL>",
        createdAt: now + 8000
      });

      await ctx.db.insert("imageLikes", {
        logEntryId: logEntry2Id,
        projectId: project1Id,
        sharedId: "test123456",
        customerSessionId: "session456",
        customerName: "Annen Kunde",
        createdAt: now + 9000
      });
      creationResults.imageLikes = 2;

      // 6. Create test typing indicators
      console.log("🔄 Creating test typing indicators...");
      await ctx.db.insert("typingIndicators", {
        logId: logEntry1Id,
        userId: args.testUserId,
        userRole: "contractor",
        expiresAt: now + 30000, // Expires in 30 seconds
        createdAt: now + 10000,
        updatedAt: now + 10000
      });

      await ctx.db.insert("typingIndicators", {
        logId: logEntry2Id,
        userId: "customer123",
        userRole: "customer",
        expiresAt: now + 45000, // Expires in 45 seconds
        createdAt: now + 11000,
        updatedAt: now + 11000
      });
      creationResults.typingIndicators = 2;

      // Calculate total
      creationResults.totalCreated = 
        creationResults.customers +
        creationResults.projects +
        creationResults.logEntries +
        creationResults.messages +
        creationResults.imageLikes +
        creationResults.typingIndicators;

      console.log("🎉 TEST DATA CREATION COMPLETED SUCCESSFULLY!");
      console.log("📊 CREATION SUMMARY:");
      console.log(`   • Customers: ${creationResults.customers}`);
      console.log(`   • Projects: ${creationResults.projects}`);
      console.log(`   • Log Entries: ${creationResults.logEntries}`);
      console.log(`   • Chat Messages: ${creationResults.messages}`);
      console.log(`   • Image Likes: ${creationResults.imageLikes}`);
      console.log(`   • Typing Indicators: ${creationResults.typingIndicators}`);
      console.log(`   • TOTAL CREATED: ${creationResults.totalCreated} records`);
      console.log("✅ Test data is ready for testing deletion utilities!");

      return {
        success: true,
        message: "Test data has been successfully created",
        creationResults,
        testDataIds: {
          customers: [customer1Id, customer2Id],
          projects: [project1Id, project2Id],
          logEntries: [logEntry1Id, logEntry2Id, logEntry3Id],
          messages: [message1Id, message2Id, message3Id]
        },
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      
      console.error("❌ ERROR DURING TEST DATA CREATION:", errorMessage);
      console.log("📊 PARTIAL CREATION RESULTS:");
      console.log(`   • Customers: ${creationResults.customers}`);
      console.log(`   • Projects: ${creationResults.projects}`);
      console.log(`   • Log Entries: ${creationResults.logEntries}`);
      console.log(`   • Chat Messages: ${creationResults.messages}`);
      console.log(`   • Image Likes: ${creationResults.imageLikes}`);
      console.log(`   • Typing Indicators: ${creationResults.typingIndicators}`);

      throw new Error(`Test data creation failed: ${errorMessage}. Partial results: ${JSON.stringify(creationResults)}`);
    }
  }
});
