import { mutation, query } from './_generated/server';
import { v } from 'convex/values';

// Generate upload URL for chat file attachments
export const generateChatUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    return await ctx.storage.generateUploadUrl();
  }
});

// Store uploaded chat file and return metadata
export const storeChatFile = mutation({
  args: {
    storageId: v.id("_storage"),
    fileName: v.string(),
    fileType: v.string(),
    fileSize: v.number(),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get the URL for the stored file
    const url = await ctx.storage.getUrl(args.storageId);

    if (!url) {
      throw new Error("Kunne ikke hente fil-URL");
    }

    // Generate thumbnail for images
    let thumbnailUrl: string | undefined;
    if (args.fileType.startsWith('image/')) {
      // For now, use the same URL as thumbnail
      // In production, you might want to generate actual thumbnails
      thumbnailUrl = url;
    }

    return {
      url,
      name: args.fileName,
      size: args.fileSize,
      type: args.fileType,
      thumbnailUrl
    };
  }
});

// Send a new message to a log entry thread
export const sendMessage = mutation({
  args: {
    logId: v.id("logEntries"),
    parentId: v.optional(v.id("messages")), // null = root message, value = reply
    senderId: v.string(),
    senderRole: v.union(v.literal("customer"), v.literal("contractor")),
    text: v.optional(v.string()),
    file: v.optional(v.object({
      url: v.string(),
      name: v.string(),
      size: v.number(),
      type: v.string(),
      thumbnailUrl: v.optional(v.string())
    }))
  },
  handler: async (ctx, args) => {
    // Validate that either text or file is provided
    if (!args.text && !args.file) {
      throw new Error("Melding må inneholde tekst eller fil");
    }

    // Verify the log entry exists
    const logEntry = await ctx.db.get(args.logId);
    if (!logEntry) {
      throw new Error("Loggoppføring ikke funnet");
    }

    // Verify the project exists and user has access
    const project = await ctx.db.get(logEntry.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    // Check user access based on role
    if (args.senderRole === "contractor") {
      // Contractor must own the project
      if (project.userId !== args.senderId) {
        throw new Error("Du har ikke tilgang til å sende meldinger i dette prosjektet");
      }
    } else if (args.senderRole === "customer") {
      // Customer must access via shared project
      if (!project.isPubliclyShared) {
        throw new Error("Dette prosjektet er ikke delt offentlig");
      }
    }

    // If this is a reply, verify the parent message exists and belongs to the same log
    if (args.parentId) {
      const parentMessage = await ctx.db.get(args.parentId);
      if (!parentMessage) {
        throw new Error("Opprinnelig melding ikke funnet");
      }
      if (parentMessage.logId !== args.logId) {
        throw new Error("Svar må tilhøre samme loggoppføring");
      }
    }

    // Create the message
    const messageId = await ctx.db.insert("messages", {
      logId: args.logId,
      parentId: args.parentId,
      senderId: args.senderId,
      senderRole: args.senderRole,
      text: args.text,
      file: args.file,
      deliveryStatus: "sent", // Message successfully sent to server
      deliveredTo: {
        [args.senderId]: Date.now() // Mark as delivered to sender immediately
      },
      createdAt: Date.now(),
      isEdited: false,
      isDeleted: false
    });

    return messageId;
  }
});

// Mark message as delivered to a user
export const markMessageDelivered = mutation({
  args: {
    messageId: v.id("messages"),
    userId: v.string(),
    userRole: v.union(v.literal("customer"), v.literal("contractor"))
  },
  handler: async (ctx, args) => {
    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Melding ikke funnet");
    }

    // Verify user has access to this message
    const logEntry = await ctx.db.get(message.logId);
    if (!logEntry) {
      throw new Error("Loggoppføring ikke funnet");
    }

    const project = await ctx.db.get(logEntry.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    // Check user access based on role
    if (args.userRole === "contractor") {
      if (project.userId !== args.userId) {
        throw new Error("Du har ikke tilgang til denne meldingen");
      }
    } else if (args.userRole === "customer") {
      if (!project.isPubliclyShared) {
        throw new Error("Dette prosjektet er ikke delt offentlig");
      }
    }

    // Update delivery status
    const currentDeliveredTo = message.deliveredTo || {};
    const updatedDeliveredTo = {
      ...currentDeliveredTo,
      [args.userId]: Date.now()
    };

    await ctx.db.patch(args.messageId, {
      deliveredTo: updatedDeliveredTo,
      deliveryStatus: "delivered"
    });

    return true;
  }
});

// Edit an existing message
export const editMessage = mutation({
  args: {
    messageId: v.id("messages"),
    userId: v.string(),
    text: v.optional(v.string()),
    file: v.optional(v.object({
      url: v.string(),
      name: v.string(),
      size: v.number(),
      type: v.string(),
      thumbnailUrl: v.optional(v.string())
    }))
  },
  handler: async (ctx, args) => {
    // Validate that either text or file is provided
    if (!args.text && !args.file) {
      throw new Error("Melding må inneholde tekst eller fil");
    }

    // Get the message
    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Melding ikke funnet");
    }

    // Check if message is deleted
    if (message.isDeleted) {
      throw new Error("Kan ikke redigere slettet melding");
    }

    // Verify user owns the message
    if (message.senderId !== args.userId) {
      throw new Error("Du kan bare redigere dine egne meldinger");
    }

    // Update the message
    await ctx.db.patch(args.messageId, {
      text: args.text,
      file: args.file,
      updatedAt: Date.now(),
      isEdited: true
    });

    return { success: true };
  }
});

// Soft delete a message
export const deleteMessage = mutation({
  args: {
    messageId: v.id("messages"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get the message
    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Melding ikke funnet");
    }

    // Check if message is already deleted
    if (message.isDeleted) {
      throw new Error("Melding er allerede slettet");
    }

    // Verify user owns the message
    if (message.senderId !== args.userId) {
      throw new Error("Du kan bare slette dine egne meldinger");
    }

    // Soft delete the message
    await ctx.db.patch(args.messageId, {
      isDeleted: true,
      updatedAt: Date.now()
    });

    return { success: true };
  }
});

// Get a specific message by ID
export const getMessage = query({
  args: { messageId: v.id("messages") },
  handler: async (ctx, args) => {
    const message = await ctx.db.get(args.messageId);
    if (!message) {
      return null;
    }

    // Don't return deleted messages
    if (message.isDeleted) {
      return null;
    }

    return message;
  }
});

// Get all messages for a log entry (real-time subscription)
export const getMessagesByLog = query({
  args: {
    logId: v.id("logEntries"),
    userId: v.string(),
    userRole: v.union(v.literal("customer"), v.literal("contractor"))
  },
  handler: async (ctx, args) => {
    // Verify the log entry exists
    const logEntry = await ctx.db.get(args.logId);
    if (!logEntry) {
      throw new Error("Loggoppføring ikke funnet");
    }

    // Verify the project exists and user has access
    const project = await ctx.db.get(logEntry.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    // Check user access based on role
    if (args.userRole === "contractor") {
      // Contractor must own the project
      if (project.userId !== args.userId) {
        throw new Error("Du har ikke tilgang til meldinger i dette prosjektet");
      }
    } else if (args.userRole === "customer") {
      // Customer must access via shared project
      if (!project.isPubliclyShared) {
        throw new Error("Dette prosjektet er ikke delt offentlig");
      }
    }

    // Get all messages for this log entry, ordered by creation time
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_log_and_created", (q) => q.eq("logId", args.logId))
      .filter((q) => q.neq(q.field("isDeleted"), true))
      .collect();

    // Build thread structure: separate root messages from replies
    const rootMessages = messages.filter(m => !m.parentId);
    const replies = messages.filter(m => m.parentId);

    // Create a map of replies by parent ID for efficient lookup
    const repliesByParent = new Map<string, typeof replies>();
    replies.forEach(reply => {
      const parentId = reply.parentId!;
      if (!repliesByParent.has(parentId)) {
        repliesByParent.set(parentId, []);
      }
      repliesByParent.get(parentId)!.push(reply);
    });

    // Build threaded structure
    const threadedMessages = rootMessages.map(rootMessage => ({
      ...rootMessage,
      replies: repliesByParent.get(rootMessage._id) || []
    }));

    return {
      messages: threadedMessages,
      totalCount: messages.length,
      rootCount: rootMessages.length,
      replyCount: replies.length
    };
  }
});

// Mark messages as read for a user
export const markAsRead = mutation({
  args: {
    logId: v.id("logEntries"),
    userId: v.string(),
    userRole: v.union(v.literal("customer"), v.literal("contractor"))
  },
  handler: async (ctx, args) => {
    // Verify the log entry exists and user has access (same logic as getMessagesByLog)
    const logEntry = await ctx.db.get(args.logId);
    if (!logEntry) {
      throw new Error("Loggoppføring ikke funnet");
    }

    const project = await ctx.db.get(logEntry.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    // Check user access based on role
    if (args.userRole === "contractor") {
      if (project.userId !== args.userId) {
        throw new Error("Du har ikke tilgang til meldinger i dette prosjektet");
      }
    } else if (args.userRole === "customer") {
      if (!project.isPubliclyShared) {
        throw new Error("Dette prosjektet er ikke delt offentlig");
      }
    }

    // Get all unread messages for this log entry
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_log", (q) => q.eq("logId", args.logId))
      .filter((q) => q.neq(q.field("isDeleted"), true))
      .collect();

    const timestamp = Date.now();
    let markedCount = 0;

    // Mark each message as read by this user
    for (const message of messages) {
      // Skip messages sent by the same user
      if (message.senderId === args.userId) {
        continue;
      }

      // Check if already read by this user
      const readBy = message.readBy || {};
      if (!readBy[args.userId]) {
        // Mark as read
        await ctx.db.patch(message._id, {
          readBy: {
            ...readBy,
            [args.userId]: timestamp
          }
        });
        markedCount++;
      }
    }

    return { success: true, markedCount };
  }
});

// Get unread message counts for a user across all their projects
export const getUnreadCounts = query({
  args: {
    userId: v.string(),
    userRole: v.union(v.literal("customer"), v.literal("contractor"))
  },
  handler: async (ctx, args) => {
    let projects;

    if (args.userRole === "contractor") {
      // Get all projects owned by the contractor
      projects = await ctx.db
        .query("projects")
        .withIndex("by_user_and_archive_status", (q) => q.eq("userId", args.userId).eq("isArchived", false))
        .collect();
    } else {
      // For customers, find projects they have access to through messages they've sent
      // This identifies shared projects the customer has participated in
      const customerMessages = await ctx.db
        .query("messages")
        .withIndex("by_sender", (q) => q.eq("senderId", args.userId))
        .collect();

      // Get unique project IDs from log entries the customer has messaged in
      const logIds = [...new Set(customerMessages.map(msg => msg.logId))];

      if (logIds.length === 0) {
        return { totalUnread: 0, conversationCounts: [] };
      }

      // Get log entries and their projects
      const logEntries = await Promise.all(
        logIds.map(logId => ctx.db.get(logId))
      );

      const validLogEntries = logEntries.filter(log => log !== null);
      const projectIds = [...new Set(validLogEntries.map(log => log!.projectId))];

      // Get the actual projects
      projects = await Promise.all(
        projectIds.map(projectId => ctx.db.get(projectId))
      );

      // Filter out null projects and only include publicly shared ones
      projects = projects.filter(project =>
        project !== null && project.isPubliclyShared
      ) as any[];
    }

    const conversationCounts = [];
    let totalUnread = 0;

    for (const project of projects) {
      // Get all log entries for this project
      const logEntries = await ctx.db
        .query("logEntries")
        .withIndex("by_project", (q) => q.eq("projectId", project._id))
        .collect();

      for (const logEntry of logEntries) {
        // Get unread messages for this log entry
        const messages = await ctx.db
          .query("messages")
          .withIndex("by_log", (q) => q.eq("logId", logEntry._id))
          .filter((q) => q.neq(q.field("isDeleted"), true))
          .collect();

        // Count unread messages (messages not sent by user and not marked as read)
        const unreadCount = messages.filter(message => {
          if (message.senderId === args.userId) return false;
          const readBy = message.readBy || {};
          return !readBy[args.userId];
        }).length;

        if (unreadCount > 0) {
          conversationCounts.push({
            projectId: project._id,
            projectName: project.name,
            logId: logEntry._id,
            logDescription: logEntry.description,
            unreadCount
          });
          totalUnread += unreadCount;
        }
      }
    }

    return { totalUnread, conversationCounts };
  }
});

// Helper function to get display name based on role
export const getDisplayName = (senderRole: "customer" | "contractor"): string => {
  return senderRole === "customer" ? "Kunde" : "Leverandør";
};

// Get messages with role-based display names for UI (paginated)
export const getMessagesWithDisplayNames = query({
  args: {
    logId: v.id("logEntries"),
    userId: v.string(),
    userRole: v.union(v.literal("customer"), v.literal("contractor")),
    limit: v.optional(v.number()), // Number of messages to fetch (default: 50)
    cursor: v.optional(v.number())  // Timestamp cursor for pagination
  },
  handler: async (ctx, args) => {
    // Get messages using existing logic (duplicate the logic here for now)
    // Verify the log entry exists
    const logEntry = await ctx.db.get(args.logId);
    if (!logEntry) {
      throw new Error("Loggoppføring ikke funnet");
    }

    // Verify the project exists and user has access
    const project = await ctx.db.get(logEntry.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    // Check user access based on role
    if (args.userRole === "contractor") {
      // Contractor must own the project
      if (project.userId !== args.userId) {
        throw new Error("Du har ikke tilgang til meldinger i dette prosjektet");
      }
    } else if (args.userRole === "customer") {
      // Customer must access via shared project
      if (!project.isPubliclyShared) {
        throw new Error("Dette prosjektet er ikke delt offentlig");
      }
    }

    // Get messages for this log entry with pagination
    const limit = args.limit || 50; // Default to 50 messages
    let messagesQuery = ctx.db
      .query("messages")
      .withIndex("by_log_and_created", (q) => q.eq("logId", args.logId))
      .filter((q) => q.neq(q.field("isDeleted"), true))
      .order("desc"); // Most recent first for pagination

    // Apply cursor-based pagination if provided
    if (args.cursor) {
      messagesQuery = messagesQuery.filter((q) => q.lt(q.field("createdAt"), args.cursor!));
    }

    const messages = await messagesQuery.take(limit);

    // Reverse to show oldest first in UI (chronological order)
    messages.reverse();

    // Build thread structure: separate root messages from replies
    const rootMessages = messages.filter(m => !m.parentId);
    const replies = messages.filter(m => m.parentId);

    // Create a map of replies by parent ID for efficient lookup
    const repliesByParent = new Map<string, typeof replies>();
    replies.forEach(reply => {
      const parentId = reply.parentId!;
      if (!repliesByParent.has(parentId)) {
        repliesByParent.set(parentId, []);
      }
      repliesByParent.get(parentId)!.push(reply);
    });

    // Build threaded structure
    const threadedMessages = rootMessages.map(rootMessage => ({
      ...rootMessage,
      replies: repliesByParent.get(rootMessage._id) || []
    }));

    // Determine if there are more messages to load
    const hasMore = messages.length === limit;
    const nextCursor = hasMore && messages.length > 0 ? messages[0].createdAt : null;

    const result = {
      messages: threadedMessages,
      totalCount: messages.length,
      rootCount: rootMessages.length,
      replyCount: replies.length,
      hasMore,
      nextCursor
    };

    // Add display names to messages
    const messagesWithDisplayNames = result.messages.map((rootMessage: any) => {
      const isOwnMessage = rootMessage.senderId === args.userId;



      return {
        ...rootMessage,
        senderDisplayName: getDisplayName(rootMessage.senderRole),
        isOwnMessage,
        replies: rootMessage.replies.map((reply: any) => {
          const replyIsOwnMessage = reply.senderId === args.userId;



          return {
            ...reply,
            senderDisplayName: getDisplayName(reply.senderRole),
            isOwnMessage: replyIsOwnMessage
          };
        })
      };
    });

    return {
      ...result,
      messages: messagesWithDisplayNames
    };
  }
});

// Get conversation overview for dashboard (role-based)
export const getConversationOverview = query({
  args: {
    userId: v.string(),
    userRole: v.union(v.literal("customer"), v.literal("contractor"))
  },
  handler: async (ctx, args) => {
    let projects;

    if (args.userRole === "contractor") {
      // Get all projects owned by the contractor
      projects = await ctx.db
        .query("projects")
        .withIndex("by_user_and_archive_status", (q) => q.eq("userId", args.userId).eq("isArchived", false))
        .collect();
    } else {
      // For customers, this would require tracking shared project access
      // For now, return empty array
      return [];
    }

    const conversations = [];

    for (const project of projects) {
      // Get all log entries for this project
      const logEntries = await ctx.db
        .query("logEntries")
        .withIndex("by_project", (q) => q.eq("projectId", project._id))
        .collect();

      for (const logEntry of logEntries) {
        // Get latest message for this log entry
        const latestMessage = await ctx.db
          .query("messages")
          .withIndex("by_log_and_created", (q) => q.eq("logId", logEntry._id))
          .filter((q) => q.neq(q.field("isDeleted"), true))
          .order("desc")
          .first();

        if (latestMessage) {
          // Count unread messages
          const messages = await ctx.db
            .query("messages")
            .withIndex("by_log", (q) => q.eq("logId", logEntry._id))
            .filter((q) => q.neq(q.field("isDeleted"), true))
            .collect();

          const unreadCount = messages.filter(message => {
            if (message.senderId === args.userId) return false;
            const readBy = message.readBy || {};
            return !readBy[args.userId];
          }).length;

          conversations.push({
            projectId: project._id,
            projectName: project.name,
            logId: logEntry._id,
            logDescription: logEntry.description,
            latestMessage: {
              ...latestMessage,
              senderDisplayName: getDisplayName(latestMessage.senderRole),
              isOwnMessage: latestMessage.senderId === args.userId
            },
            unreadCount,
            lastActivity: latestMessage.createdAt
          });
        }
      }
    }

    // Sort by last activity (most recent first)
    conversations.sort((a, b) => b.lastActivity - a.lastActivity);

    return conversations;
  }
});

// Add emoji reaction to a message
export const addReaction = mutation({
  args: {
    messageId: v.id("messages"),
    userId: v.string(),
    emoji: v.string()
  },
  handler: async (ctx, args) => {
    // Get the message
    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Melding ikke funnet");
    }

    // Check if message is deleted
    if (message.isDeleted) {
      throw new Error("Kan ikke reagere på slettet melding");
    }

    // Verify user has access to the log entry (same logic as sendMessage)
    const logEntry = await ctx.db.get(message.logId);
    if (!logEntry) {
      throw new Error("Loggoppføring ikke funnet");
    }

    const project = await ctx.db.get(logEntry.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    // Basic access check (contractor owns project OR customer has shared access)
    const isContractor = project.userId === args.userId;
    const isCustomerWithAccess = project.isPubliclyShared;

    if (!isContractor && !isCustomerWithAccess) {
      throw new Error("Du har ikke tilgang til å reagere på denne meldingen");
    }

    // Get current reactions
    const reactions = message.reactions || [];

    // Find existing reaction with this emoji
    const existingReactionIndex = reactions.findIndex(r => r.emoji === args.emoji);

    if (existingReactionIndex >= 0) {
      // Emoji already exists, add user to it (if not already there)
      const existingReaction = reactions[existingReactionIndex];
      if (!existingReaction.userIds.includes(args.userId)) {
        existingReaction.userIds.push(args.userId);
        existingReaction.count = existingReaction.userIds.length;
      }
    } else {
      // New emoji reaction
      reactions.push({
        emoji: args.emoji,
        userIds: [args.userId],
        count: 1
      });
    }

    // Update the message
    await ctx.db.patch(args.messageId, {
      reactions: reactions
    });

    return { success: true };
  }
});

// Remove emoji reaction from a message
export const removeReaction = mutation({
  args: {
    messageId: v.id("messages"),
    userId: v.string(),
    emoji: v.string()
  },
  handler: async (ctx, args) => {
    // Get the message
    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Melding ikke funnet");
    }

    // Get current reactions
    const reactions = message.reactions || [];

    // Find existing reaction with this emoji
    const existingReactionIndex = reactions.findIndex(r => r.emoji === args.emoji);

    if (existingReactionIndex >= 0) {
      const existingReaction = reactions[existingReactionIndex];

      // Remove user from reaction
      const userIndex = existingReaction.userIds.indexOf(args.userId);
      if (userIndex >= 0) {
        existingReaction.userIds.splice(userIndex, 1);
        existingReaction.count = existingReaction.userIds.length;

        // If no users left, remove the entire reaction
        if (existingReaction.userIds.length === 0) {
          reactions.splice(existingReactionIndex, 1);
        }
      }
    }

    // Update the message
    await ctx.db.patch(args.messageId, {
      reactions: reactions
    });

    return { success: true };
  }
});

// Typing indicator functions
export const startTyping = mutation({
  args: {
    logId: v.id("logEntries"),
    userId: v.string(),
    userRole: v.union(v.literal("customer"), v.literal("contractor"))
  },
  handler: async (ctx, args) => {
    // Verify access to the log entry
    const logEntry = await ctx.db.get(args.logId);
    if (!logEntry) {
      throw new Error("Loggoppføring ikke funnet");
    }

    // Get the project to verify access
    const project = await ctx.db.get(logEntry.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    // Check user access based on role
    if (args.userRole === "contractor") {
      if (project.userId !== args.userId) {
        throw new Error("Du har ikke tilgang til denne samtalen");
      }
    } else if (args.userRole === "customer") {
      if (!project.isPubliclyShared) {
        throw new Error("Dette prosjektet er ikke delt offentlig");
      }
    }

    // Store typing indicator with expiration (will be cleaned up by a scheduled function)
    const expiresAt = Date.now() + 5000; // 5 seconds from now

    // Check if user is already typing for this log
    const existingTyping = await ctx.db
      .query("typingIndicators")
      .withIndex("by_log_and_user", (q) => q.eq("logId", args.logId).eq("userId", args.userId))
      .first();

    if (existingTyping) {
      // Update existing typing indicator
      await ctx.db.patch(existingTyping._id, {
        expiresAt,
        updatedAt: Date.now()
      });
    } else {
      // Create new typing indicator
      await ctx.db.insert("typingIndicators", {
        logId: args.logId,
        userId: args.userId,
        userRole: args.userRole,
        expiresAt,
        createdAt: Date.now(),
        updatedAt: Date.now()
      });
    }

    return { success: true };
  }
});

export const stopTyping = mutation({
  args: {
    logId: v.id("logEntries"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Find and remove typing indicator
    const typingIndicator = await ctx.db
      .query("typingIndicators")
      .withIndex("by_log_and_user", (q) => q.eq("logId", args.logId).eq("userId", args.userId))
      .first();

    if (typingIndicator) {
      await ctx.db.delete(typingIndicator._id);
    }

    return { success: true };
  }
});

export const getTypingIndicators = query({
  args: {
    logId: v.id("logEntries"),
    userId: v.string() // Current user ID to exclude from results
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    // Get all active typing indicators for this log (excluding current user and expired ones)
    const typingIndicators = await ctx.db
      .query("typingIndicators")
      .withIndex("by_log", (q) => q.eq("logId", args.logId))
      .filter((q) => q.and(
        q.neq(q.field("userId"), args.userId), // Exclude current user
        q.gt(q.field("expiresAt"), now) // Only active indicators
      ))
      .collect();

    // Map to display format
    return typingIndicators.map(indicator => ({
      userId: indicator.userId,
      userRole: indicator.userRole,
      displayName: indicator.userRole === "contractor" ? "Leverandør" : "Kunde",
      timestamp: indicator.updatedAt
    }));
  }
});

// Helper function to find customer session ID from existing messages in a project
export const findCustomerSessionForProject = query({
  args: {
    projectId: v.id("projects"),
    currentSessionId: v.string()
  },
  handler: async (ctx, args) => {
    // Get the project to verify it's publicly shared
    const project = await ctx.db.get(args.projectId);
    if (!project || !project.isPubliclyShared) {
      throw new Error("Prosjekt ikke funnet eller ikke delt offentlig");
    }

    // Get all log entries for this project
    const logEntries = await ctx.db
      .query("logEntries")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .collect();

    // Look for customer messages in any of the log entries
    for (const logEntry of logEntries) {
      const customerMessages = await ctx.db
        .query("messages")
        .withIndex("by_log", (q) => q.eq("logId", logEntry._id))
        .filter((q) => q.eq(q.field("senderRole"), "customer"))
        .collect();

      if (customerMessages.length > 0) {
        // Return the first customer session ID found
        const existingCustomerSessionId = customerMessages[0].senderId;


        return {
          existingSessionId: existingCustomerSessionId,
          shouldRecover: existingCustomerSessionId !== args.currentSessionId,
          messageCount: customerMessages.length
        };
      }
    }

    // No existing customer messages found
    return {
      existingSessionId: null,
      shouldRecover: false,
      messageCount: 0
    };
  }
});
