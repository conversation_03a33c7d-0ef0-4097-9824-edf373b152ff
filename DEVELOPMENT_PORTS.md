# JobbLogg Development Port Management

This document describes the fixed port configuration and management system for the JobbLogg development environment.

## Fixed Port Configuration

JobbLogg uses **fixed, consistent ports** to ensure predictable development server addresses:

- **Vite Dev Server**: `http://localhost:5173/` (FIXED)
- **Vite Preview**: `http://localhost:4173/` (FIXED)
- **Convex Backend**: Uses Convex Cloud (no local port)

## Why Fixed Ports?

Previously, Vite would automatically try multiple ports (5173, 5174, 5175, etc.) when the preferred port was occupied. This caused:
- Inconsistent development URLs
- Confusion about which server was running where
- Difficulty with bookmarks and external integrations
- Problems with CORS and authentication configurations

## Port Management Scripts

### Available Commands

```bash
# Start both Vite and Convex (recommended)
npm run dev

# Start with automatic port clearing
npm run dev:clear

# Start only Vite dev server
npm run dev:vite

# Start only Convex backend
npm run dev:convex

# Force start Vite (bypasses port check)
npm run dev:vite:force

# Check port availability
npm run port-check

# Clear conflicting processes on required ports
npm run port-clear
```

### Port Conflict Resolution

When ports are occupied, you have several options:

1. **Automatic clearing** (recommended):
   ```bash
   npm run dev:clear
   ```

2. **Manual port check**:
   ```bash
   npm run port-check
   ```

3. **Manual port clearing**:
   ```bash
   npm run port-clear
   ```

## Configuration Details

### Vite Configuration (`vite.config.ts`)

```typescript
export default defineConfig({
  plugins: [react()],
  server: {
    port: 5173,
    strictPort: true, // Fail if port is occupied
    host: 'localhost',
    open: false
  },
  preview: {
    port: 4173,
    strictPort: true,
    host: 'localhost'
  }
})
```

Key settings:
- `strictPort: true` - Prevents automatic port switching
- `open: false` - Prevents auto-opening browser (reduces conflicts)

### Port Manager Script

The `scripts/port-manager.js` script:
- Checks if required ports are available
- Identifies processes using those ports
- Can automatically kill conflicting processes
- Provides clear error messages and solutions

### Development Starter Script

The `scripts/dev-start.js` script:
- Manages both Vite and Convex processes
- Provides colored, prefixed logging
- Handles graceful shutdown (Ctrl+C)
- Detects startup success/failure
- Offers flexible startup options

## Troubleshooting

### Port 5173 is occupied

```bash
# Check what's using the port
npm run port-check

# Clear the port automatically
npm run port-clear

# Or start with automatic clearing
npm run dev:clear
```

### Multiple Vite instances running

```bash
# Kill all processes on development ports
npm run port-clear

# Start fresh
npm run dev
```

### Permission errors when killing processes

Some processes may require elevated permissions:

```bash
# On macOS/Linux, you might need sudo
sudo npm run port-clear
```

### Script execution errors

Ensure scripts are executable:

```bash
chmod +x scripts/port-manager.js
chmod +x scripts/dev-start.js
```

## Best Practices

1. **Always use `npm run dev`** instead of direct `vite` command
2. **Use `npm run dev:clear`** if you frequently have port conflicts
3. **Check ports before starting** with `npm run port-check`
4. **Use Ctrl+C** to stop all services gracefully
5. **Bookmark `http://localhost:5173/`** - it will always be the same

## Migration from Old Setup

If you were previously using different ports:

1. Update any bookmarks to `http://localhost:5173/`
2. Update any external integrations or CORS settings
3. Clear browser cache if authentication issues occur
4. Use the new npm scripts instead of direct commands

## Technical Details

- **Port detection**: Uses `lsof` command to find processes
- **Process management**: Uses Node.js `child_process` for spawning
- **Graceful shutdown**: Handles SIGINT, SIGTERM, SIGQUIT signals
- **Cross-platform**: Works on macOS, Linux, and Windows (with WSL)
- **Error handling**: Comprehensive error messages and recovery suggestions

This system ensures that JobbLogg development environment always runs on predictable, consistent ports while providing robust conflict resolution mechanisms.
